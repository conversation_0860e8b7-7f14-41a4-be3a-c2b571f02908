/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.springblade.system.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.system.entity.AttachmentInfoEntity;

/**
 * 附件表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AttachmentInfoVO extends AttachmentInfoEntity {
	private static final long serialVersionUID = 1L;

}
