/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.Date;

/**
 * 设备软件版本信息表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@TableName("device_software_version_info")
@ApiModel(value = "DeviceSoftwareVersionInfo对象", description = "设备软件版本信息表")
@EqualsAndHashCode(callSuper = true)
public class DeviceSoftwareVersionInfoEntity extends SkyWorthEntity {

	/**
	 * SN号
	 */
	@ApiModelProperty(value = "SN号")
	private String serialNumber;
	/**
	 * 当前版本
	 */
	@ApiModelProperty(value = "当前版本")
	private String currentVersionNumber;
	/**
	 * 大类型（device逆变器、battery电池）
	 */
	@ApiModelProperty(value = "大类型（device逆变器、battery电池）")
	private String bigType;
	/**
	 * 小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）
	 */
	@ApiModelProperty(value = "小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）")
	private String smallType;
	/**
	 * 升级完成时间
	 */
	@ApiModelProperty(value = "升级完成时间")
	private Date upgradeCompletionTime;
	/**
	 * 公司
	 */
	@ApiModelProperty(value = "公司")
	private String company;

	/**
	 * 失败重试次数，最大3次
	 */
	@ApiModelProperty(value = "失败重试次数，最大3次")
	private Integer retryCount;

	/**
	 * 最新下发版本
	 */
	@ApiModelProperty(value = "最新下发版本")
	private String latestReleasedVersion;

	/**
	 * 固件批次
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "固件批次")
	private String firmwareBatch;

	/**
	 * 逆变器型号
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "逆变器型号")
	private String deviceType;


	/**
	 * 大类型描述
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "大类型描述")
	private String bigTypeName;

	/**
	 * 小类型描述
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "小类型描述")
	private String smallTypeName;

	/**
	 * 公司名称
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "公司名称")
	private String companyName;

	/**
	 * 设备状态
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "设备状态")
	private String deviceStatus;

	/**
	 * 是否可勾升级
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "是否可勾升级")
	private Boolean canCheck = false;

}
