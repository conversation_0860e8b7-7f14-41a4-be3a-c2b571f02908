package org.skyworth.ess.dailyStatistics.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电池列表查询条件
 *
 * @description
 * @author: SDT50545
 * @since: 2023-09-14 17:58
 **/
@Data
@ApiModel(value = "电池列表查询条件", description = "电池列表查询条件")
public class QueryDeviceLog22Condition implements Serializable {
	private static final long serialVersionUID = -1;

	@ApiModelProperty(value = "站点id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;

	@ApiModelProperty(value = "电池SN")
	private String batterySerialNumber;

	@ApiModelProperty(value = "开始时间")
	private Date startDateTime;

	@ApiModelProperty(value = "结束时间")
	private Date endDateTime;

	@ApiModelProperty(value = "逆变器SN")
	private String deviceSerialNumber;

	@ApiModelProperty(value = "日期")
	private String date;
	// 是否并机
	private String isParallelMode;
	private String beginTime;
	private String endTime;
	private String timeZone;
}
