package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/***
 * 设备/逆变器日志表，记录2.1数据;
 */
@Data
@TableName("device_log21")
public class DeviceLog21 extends TenantEntity {

	@ApiModelProperty(name = "同步状态（N未同步；Y已同步）",notes = "")
	private String synchStatus ;

	/** 设备型号 */
	@ApiModelProperty(name = "设备型号",notes = "")
	private String deviceModel ;
	/** 主机软件版本 */
	@ApiModelProperty(name = "主机软件版本",notes = "")
	private String masterSoftwareVersion ;
	/** 主机软件建立日期 */
	@ApiModelProperty(name = "主机软件建立日期",notes = "")
	private String masterSoftwareBuildDate ;
	/** 从机固件版本 */
	@ApiModelProperty(name = "从机固件版本",notes = "")
	private String slaveFirmwareVersion ;
	/** 从机固件建立日期 */
	@ApiModelProperty(name = "从机固件建立日期",notes = "")
	private String slaveFirmwareBuildDate ;
	/** mppt路数 */
	@ApiModelProperty(name = "mppt路数",notes = "")
	private String mpptNumber ;
	/** 额定电压 */
	@ApiModelProperty(name = "额定电压",notes = "")
	private BigDecimal ratedVoltage ;
	/** 额定频率 */
	@ApiModelProperty(name = "额定频率",notes = "")
	private BigDecimal ratedFrequency ;
	/** 额定功率 */
	@ApiModelProperty(name = "额定功率",notes = "")
	private BigDecimal ratedPower ;
	/** 电网相数 */
	@ApiModelProperty(name = "电网相数",notes = "")
	private String gridPhaseNumber ;
	/** EMS固件版本 */
	@ApiModelProperty(name = "EMS固件版本",notes = "")
	private String emsFirmwareVersion ;
	/** EMS固件建立日期 */
	@ApiModelProperty(name = "EMS固件建立日期",notes = "")
	private String emsFirmwareBuildDate ;
	/** DCDC固件版本 */
	@ApiModelProperty(name = "DCDC固件版本",notes = "")
	private String dcdcFirmwareVersion ;
	/** DCDC固件建立日期 */
	@ApiModelProperty(name = "DCDC固件建立日期",notes = "")
	private String dcdcFirmwareBuildDate ;
	/** wifi棒SN */
	@ApiModelProperty(name = "wifi棒SN",notes = "")
	private String wifiStickSerialNumber ;
	/** 设备状态 */
	@ApiModelProperty(name = "设备状态",notes = "")
	private String deviceStatus ;

	/**wifi棒版本号*/
	@ApiModelProperty(name = "wifi棒版本号",notes = "")
	private String wifiVersion;

	/**电池版本号*/
	@ApiModelProperty(name = "电池版本号",notes = "")
	private String batteryVersion;


	public DeviceLog21(){

	}

	/** 站点ID */
	@ApiModelProperty(name = "站点ID",notes = "")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private long plantId ;

	/** 设备时间，设备上报时时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(name = "设备时间，设备上报时时间",notes = "")
	private Date deviceDateTime ;

	/** modbus协议版本 */
	@ApiModelProperty(name = "modbus协议版本",notes = "")
	private String modbusProtocolVersion ;


	/** 逆变器/设备SN */
	@ApiModelProperty(name = "逆变器/设备SN",notes = "")
	private String deviceSerialNumber ;

	/** 创建人账号 */
	@ApiModelProperty(name = "创建人账号",notes = "")
	private String createUserAccount ;
	/** 更新人账号 */
	@ApiModelProperty(name = "更新人账号",notes = "")
	private String updateUserAccount ;

	public long getPlantId() {
		return plantId;
	}

	public void setPlantId(long plantId) {
		this.plantId = plantId;
	}

	public Date getDeviceDateTime() {
		return deviceDateTime;
	}

	public void setDeviceDateTime(Date deviceDateTime) {
		this.deviceDateTime = deviceDateTime;
	}

	public String getModbusProtocolVersion() {
		return modbusProtocolVersion;
	}

	public void setModbusProtocolVersion(String modbusProtocolVersion) {
		this.modbusProtocolVersion = modbusProtocolVersion;
	}

	public String getDeviceSerialNumber() {
		return deviceSerialNumber;
	}

	public void setDeviceSerialNumber(String deviceSerialNumber) {
		this.deviceSerialNumber = deviceSerialNumber;
	}

	public String getCreateUserAccount() {
		return createUserAccount;
	}

	public void setCreateUserAccount(String createUserAccount) {
		this.createUserAccount = createUserAccount;
	}

	public String getUpdateUserAccount() {
		return updateUserAccount;
	}

	public void setUpdateUserAccount(String updateUserAccount) {
		this.updateUserAccount = updateUserAccount;
	}

	/** 同步状态（N未同步；Y已同步） */
	public String getSynchStatus(){
		return this.synchStatus;
	}
	/** 同步状态（N未同步；Y已同步） */
	public void setSynchStatus(String synchStatus){
		this.synchStatus=synchStatus;
	}


	/** 设备型号 */
	public String getDeviceModel(){
		return this.deviceModel;
	}
	/** 设备型号 */
	public void setDeviceModel(String deviceModel){
		this.deviceModel=deviceModel;
	}
	/** 主机软件版本 */
	public String getMasterSoftwareVersion(){
		return this.masterSoftwareVersion;
	}
	/** 主机软件版本 */
	public void setMasterSoftwareVersion(String masterSoftwareVersion){
		this.masterSoftwareVersion=masterSoftwareVersion;
	}
	/** 主机软件建立日期 */
	public String getMasterSoftwareBuildDate(){
		return this.masterSoftwareBuildDate;
	}
	/** 主机软件建立日期 */
	public void setMasterSoftwareBuildDate(String masterSoftwareBuildDate){
		this.masterSoftwareBuildDate=masterSoftwareBuildDate;
	}
	/** 从机固件版本 */
	public String getSlaveFirmwareVersion(){
		return this.slaveFirmwareVersion;
	}
	/** 从机固件版本 */
	public void setSlaveFirmwareVersion(String slaveFirmwareVersion){
		this.slaveFirmwareVersion=slaveFirmwareVersion;
	}
	/** 从机固件建立日期 */
	public String getSlaveFirmwareBuildDate(){
		return this.slaveFirmwareBuildDate;
	}
	/** 从机固件建立日期 */
	public void setSlaveFirmwareBuildDate(String slaveFirmwareBuildDate){
		this.slaveFirmwareBuildDate=slaveFirmwareBuildDate;
	}
	/** mppt路数 */
	public String getMpptNumber(){
		return this.mpptNumber;
	}
	/** mppt路数 */
	public void setMpptNumber(String mpptNumber){
		this.mpptNumber=mpptNumber;
	}
	/** 额定电压 */
	public BigDecimal getRatedVoltage(){
		return this.ratedVoltage;
	}
	/** 额定电压 */
	public void setRatedVoltage(BigDecimal ratedVoltage){
		this.ratedVoltage=ratedVoltage;
	}
	/** 额定频率 */
	public BigDecimal getRatedFrequency(){
		return this.ratedFrequency;
	}
	/** 额定频率 */
	public void setRatedFrequency(BigDecimal ratedFrequency){
		this.ratedFrequency=ratedFrequency;
	}
	/** 额定功率 */
	public BigDecimal getRatedPower(){
		return this.ratedPower;
	}
	/** 额定功率 */
	public void setRatedPower(BigDecimal ratedPower){
		this.ratedPower=ratedPower;
	}
	/** 电网相数 */
	public String getGridPhaseNumber(){
		return this.gridPhaseNumber;
	}
	/** 电网相数 */
	public void setGridPhaseNumber(String gridPhaseNumber){
		this.gridPhaseNumber=gridPhaseNumber;
	}
	/** EMS固件版本 */
	public String getEmsFirmwareVersion(){
		return this.emsFirmwareVersion;
	}
	/** EMS固件版本 */
	public void setEmsFirmwareVersion(String emsFirmwareVersion){
		this.emsFirmwareVersion=emsFirmwareVersion;
	}
	/** EMS固件建立日期 */
	public String getEmsFirmwareBuildDate(){
		return this.emsFirmwareBuildDate;
	}
	/** EMS固件建立日期 */
	public void setEmsFirmwareBuildDate(String emsFirmwareBuildDate){
		this.emsFirmwareBuildDate=emsFirmwareBuildDate;
	}
	/** DCDC固件版本 */
	public String getDcdcFirmwareVersion(){
		return this.dcdcFirmwareVersion;
	}
	/** DCDC固件版本 */
	public void setDcdcFirmwareVersion(String dcdcFirmwareVersion){
		this.dcdcFirmwareVersion=dcdcFirmwareVersion;
	}
	/** DCDC固件建立日期 */
	public String getDcdcFirmwareBuildDate(){
		return this.dcdcFirmwareBuildDate;
	}
	/** DCDC固件建立日期 */
	public void setDcdcFirmwareBuildDate(String dcdcFirmwareBuildDate){
		this.dcdcFirmwareBuildDate=dcdcFirmwareBuildDate;
	}
	/** wifi棒SN */
	public String getWifiStickSerialNumber(){
		return this.wifiStickSerialNumber;
	}
	/** wifi棒SN */
	public void setWifiStickSerialNumber(String wifiStickSerialNumber){
		this.wifiStickSerialNumber=wifiStickSerialNumber;
	}
	/** 设备状态 */
	public String getDeviceStatus(){
		return this.deviceStatus;
	}
	/** 设备状态 */
	public void setDeviceStatus(String deviceStatus){
		this.deviceStatus=deviceStatus;
	}

}
