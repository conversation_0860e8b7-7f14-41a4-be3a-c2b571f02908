package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器日志表，记录23数据;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-14
 */
@Data
@ApiModel(value = "设备/逆变器日志表，记录23数据",description = "")
@TableName("device_log23")
public class DeviceLog23 extends TenantEntity implements Serializable,Cloneable{

	/** 工作模式 */
	@ApiModelProperty(name = "工作模式",notes = "")
	private String hybridWorkMode ;
	/** 一次/每天 */
	@ApiModelProperty(name = "一次/每天",notes = "")
	private String onceEveryday ;
	/** 充电开始时间 */
	@ApiModelProperty(name = "充电开始时间",notes = "")
	private String chargeStartTime1 ;
	/** 充电结束时间 */
	@ApiModelProperty(name = "充电结束时间",notes = "")
	private String chargeEndTime1 ;
	/** 放电开始时间 */
	@ApiModelProperty(name = "放电开始时间",notes = "")
	private String dischargeStartTime1 ;
	/** 放电结束时间 */
	@ApiModelProperty(name = "放电结束时间",notes = "")
	private String dischargeEndTime1 ;
	/** 电池类型选择 */
	@ApiModelProperty(name = "电池类型选择",notes = "")
	private String batteryTypeSelection ;
	/** 通讯地址 */
	@ApiModelProperty(name = "通讯地址",notes = "")
	private String commAddress ;
	/** 电池容量 */
	@ApiModelProperty(name = "电池容量",notes = "")
	private BigDecimal batteryAh ;
	/** 铅酸电池放电截止电压 */
	@ApiModelProperty(name = "铅酸电池放电截止电压",notes = "")
	private BigDecimal stopDischargeVoltage ;
	/** 铅酸电池充电截止电压 */
	@ApiModelProperty(name = "铅酸电池充电截止电压",notes = "")
	private BigDecimal stopChargeVoltage ;
	/** 电网充电使能 */
	@ApiModelProperty(name = "电网充电使能",notes = "")
	private String gridCharge ;
	/** 最大电网充电功率 */
	@ApiModelProperty(name = "最大电网充电功率",notes = "")
	private BigDecimal maximumGridChargerPower ;
	/** 电网充电截止电量% */
	@ApiModelProperty(name = "电网充电截止电量%",notes = "")
	private BigDecimal capacityOfGridChargerEnd ;
	/** 最大充电功率 */
	@ApiModelProperty(name = "最大充电功率",notes = "")
	private BigDecimal maximumChargerPower ;
	/** 充电截止SOC */
	@ApiModelProperty(name = "充电截止SOC",notes = "")
	private BigDecimal capacityOfChargerEnd ;
	/** 最大放电功率 */
	@ApiModelProperty(name = "最大放电功率",notes = "")
	private BigDecimal maximumDischargerPower ;
	/** 放电截止EOD */
	@ApiModelProperty(name = "放电截止EOD",notes = "")
	private BigDecimal capacityOfDischargerEnd ;
	/** 离网模式使能 */
	@ApiModelProperty(name = "离网模式使能",notes = "")
	private String offGridMode ;
	/** 额定输出电压 */
	@ApiModelProperty(name = "额定输出电压",notes = "")
	private BigDecimal ratedOutputVoltage ;
	/** 额定输出频率 */
	@ApiModelProperty(name = "额定输出频率",notes = "")
	private BigDecimal ratedOutputFrequency ;
	/** 切换离网模式的电池最低SOC */
	@ApiModelProperty(name = "切换离网模式的电池最低SOC",notes = "")
	private BigDecimal offGridStartUpBatteryCapacity ;
	/** 最大放电电流 */
	@ApiModelProperty(name = "最大放电电流",notes = "")
	private BigDecimal maximumDischargeCurrent ;
	/** 最大充电电流 */
	@ApiModelProperty(name = "最大充电电流",notes = "")
	private BigDecimal maximumChargerCurrent ;

	@ApiModelProperty(name = "GEN端口",notes = "")
	private String genPort;


	@ApiModelProperty(name = "锂电池激活功能",notes = "")
	private String lithiumBatteryActivationFunction;

	@ApiModelProperty(name = "最大发电机充电功率",notes = "")
	private BigDecimal maximumGenChargePower;

	@ApiModelProperty(name = "发电机的最大输入功率",notes = "")
	private BigDecimal maximumInputPowerFromGenerator;


	@ApiModelProperty(name = "支持正常负载功能",notes = "")
	private String supportNormalLoadFunction;

	@ApiModelProperty(name = "并行模式功能",notes = "")
	private String parallelModeFunction;

	@ApiModelProperty(name = "馈入网格功能",notes = "")
	private String feedInGridFunction;

	@ApiModelProperty(name = "最大电网强制充电功率",notes = "")
	private BigDecimal maximumGridForcedChargePower;
	/** 分时控制开关 */
	@ApiModelProperty(name = "分时控制开关",notes = "分时控制开关")
	private String timeBasedControlEnable ;

	private BigDecimal generatorStartSoc;

	private BigDecimal generatorEndSoc;

	private BigDecimal maximumInputPowerFromGrid;

	private BigDecimal capacityOfDischargeEndOnGrid;

	private BigDecimal forceChargeStartSoc;

	private BigDecimal forceChargeEndSoc;

	private BigDecimal backupMinimumOutputVoltage;

	private BigDecimal backupMaximumOutputVoltage;

	private String generatorDryForceOnOrOff;

	private String onceEveryday2;
	private String chargeStartTime2;
	private String chargeEndTime2;
	private String chargePowerInTime2HighWord;
	private String chargeEndSocInTime2;
	private String dischargeStartTime2;
	private String dischargeEndTime2;
	private String dischargePowerInTime2HighWord;
	private String dischargeEndSocInTime2;

	private String onceEveryday3;
	private String chargeStartTime3;
	private String chargeEndTime3;
	private String chargePowerInTime3HighWord;
	private String chargeEndSocInTime3;
	private String dischargeStartTime3;
	private String dischargeEndTime3;
	private String dischargePowerInTime3HighWord;
	private String dischargeEndSocInTime3;

	/** 站点ID */
	@ApiModelProperty(name = "站点ID",notes = "")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private long plantId ;

	/** 设备时间，设备上报时时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(name = "设备时间，设备上报时时间",notes = "")
	private Date deviceDateTime ;

	/** modbus协议版本 */
	@ApiModelProperty(name = "modbus协议版本",notes = "")
	private String modbusProtocolVersion ;

	@ApiModelProperty(name = "同步状态（N未同步；Y已同步）",notes = "")
	private String synchStatus ;


	/** 逆变器/设备SN */
	@ApiModelProperty(name = "逆变器/设备SN",notes = "")
	private String deviceSerialNumber ;

	/** 创建人账号 */
	@ApiModelProperty(name = "创建人账号",notes = "")
	private String createUserAccount ;
	/** 更新人账号 */
	@ApiModelProperty(name = "更新人账号",notes = "")
	private String updateUserAccount ;

	@ApiModelProperty(value = "最大充电功率(新)")
	private String chargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束充电容量比例(新)")
	private String chargeEndSocInTime1;

	@ApiModelProperty(value = "最大放电功率(新)")
	private String dischargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束放电容量比例(新)")
	private String dischargeEndSocInTime1;
	// ac_coupling开关;(0/1:关/开)
	private BigDecimal acCouplingFunction;
	// 通讯电表,业务字典 device_re_485_hybrid
	private String rs485Device;
	private String parallelSystemBatteryConnectType;

}
