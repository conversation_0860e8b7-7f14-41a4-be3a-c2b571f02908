/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;

/**
 * OTA升级推送设备端报文信息
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtaUpdatePackVO extends OtaUpdatePackEntity {
	private static final long serialVersionUID = 1L;
	private String requestId;
	private String currentVersionNumber;
	private String serialNumber;
	private Long deviceSoftwareVersionId;
	private String container;
	private String azureKey;
}
