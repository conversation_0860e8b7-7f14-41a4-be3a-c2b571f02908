/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer2Scale;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器每日统计 实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@TableName("device_everyday_total")
@ApiModel(value = "DeviceEverydayTotal对象", description = "设备/逆变器每日统计")
@EqualsAndHashCode(callSuper = true)
public class DeviceEverydayTotalEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 设备/逆变器时间，设备/逆变器上报时时间
	 */
	@ApiModelProperty(value = "设备/逆变器时间，设备/逆变器上报时时间")
	private Date deviceDateTime;
	/**
	 * 统计日期
	 */
	@ApiModelProperty(value = "统计日期")
	private String totalDate;
	/**
	 * 发电能量
	 */
	@ApiModelProperty(value = "发电能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayEnergy;
	/**
	 * 输入能量
	 */
	@ApiModelProperty(value = "电网输入能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayImportEnergy;
	/**
	 * 输出能量
	 */
	@ApiModelProperty(value = "馈网能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayExportEnergy;
	/**
	 * 负载能量
	 */
	@ApiModelProperty(value = "一般负载能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayLoadEnergy;

	@ApiModelProperty(value = "关键负载能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal dailyEnergyToEps;

	@ApiModelProperty(value = "其他PV")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	@TableField(exist = false)
	private BigDecimal energyTodayOfAcCoupleWh;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
