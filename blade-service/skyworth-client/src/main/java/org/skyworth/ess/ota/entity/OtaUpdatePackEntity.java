/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.system.entity.AttachmentInfoEntity;

/**
 * OTA升级包 实体类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@TableName("ota_update_pack")
@ApiModel(value = "OtaUpdatePack对象", description = "OTA升级包")
@EqualsAndHashCode(callSuper = true)
public class OtaUpdatePackEntity extends SkyWorthEntity {

	/**
	 * 大类型（device逆变器、battery电池）
	 */
	@ApiModelProperty(value = "大类型（inverter逆变器、battery电池、app）")
	private String bigType;
	/**
	 * 小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）
	 */
	@ApiModelProperty(value = "小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）")
	private String smallType;
	/**
	 * 版本号
	 */
	@ApiModelProperty(value = "版本号")
	private String versionNumber;
	/**
	 * 公司
	 */
	@ApiModelProperty(value = "公司编码")
	private String company;
	/**
	 * 升级包地址
	 */
	@ApiModelProperty(value = "升级包CDN地址")
	private String packCdnUrl;
	/**
	 * 是否最新版本
	 */
	@ApiModelProperty(value = "是否最新版本")
	private Integer isNewVersion;
	/**
	 * 是否强制升级，默认为'否'
	 */
	@ApiModelProperty(value = "是否强制升级，默认为'否'")
	private Byte whetherToForceUpgrade;

	/**
	 * 附件批次id
	 */
	@ApiModelProperty(value = "附件批次id")
	private Long businessId;

	/**
	 * 校验和
	 */
	@ApiModelProperty(value = "校验和")
	private Long checkSum;

	/**
	 * 固件批次
	 */
	@ApiModelProperty(value = "固件批次")
	private String firmwareBatch;

	/**
	 * 逆变器型号
	 */
	@ApiModelProperty(value = "逆变器型号")
	private String deviceType;


	/**
	 * 版本说明
	 */
	@ApiModelProperty(value = "版本说明")
	private String remark;

	/**
	 * 附件
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "附件展示")
	private BatchVO<AttachmentInfoEntity> batchVO;

	@TableField(exist = false)
	@ApiModelProperty(value = "附件保存对象")
	private AttachmentInfoEntity attachmentInfoEntity;

	@TableField(exist = false)
	@ApiModelProperty(value = "大类型描述")
	private String bigTypeName;

	@TableField(exist = false)
	@ApiModelProperty(value = "小类型描述")
	private String smallTypeName;

	@TableField(exist = false)
	@ApiModelProperty(value = "公司名称")
	private String companyName;

	@TableField(exist = false)
	@ApiModelProperty(value = "时区")
	private String timeZone;
}
