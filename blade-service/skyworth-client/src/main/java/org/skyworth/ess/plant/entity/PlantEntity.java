/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.Date;
import java.util.List;


/**
 * 站点信息表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@TableName("plant")
@ApiModel(value = "Plant对象", description = "站点信息表")
@EqualsAndHashCode(callSuper = true)
public class PlantEntity extends SkyWorthEntity {

	/**
	 * 站点名称
	 */
	@ApiModelProperty(value = "站点名称")
	private String plantName;
	/**
	 * 国家
	 */
	@ApiModelProperty(value = "国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ApiModelProperty(value = "省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ApiModelProperty(value = "区县")
	private String countyCode;
	/**
	 * 详细地址
	 */
	@ApiModelProperty(value = "详细地址")
	private String detailAddress;
	/**
	 * 时区
	 */
	@ApiModelProperty(value = "时区")
	private String timeZone;
	/**
	 * 光伏数量
	 */
	@ApiModelProperty(value = "光伏数量")
	private Integer photovoltaicNumber;
	/**
	 * 逆变器数量
	 */
	@ApiModelProperty(value = "逆变器数量")
	private Integer deviceNumber;
	/**
	 * 储能数量
	 */
	@ApiModelProperty(value = "储能数量")
	private Integer batteryNumber;
	/**
	 * 充电桩数量
	 */
	@ApiModelProperty(value = "充电桩数量")
	private Integer chargingStation;
	/**
	 * 安装容量
	 */
	@ApiModelProperty(value = "安装容量")
	private String installCapacity;
	/**
	 * 安装日期
	 */
	@ApiModelProperty(value = "安装日期")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date installDate;
	/**
	 * 安装团队
	 */
	@ApiModelProperty(value = "安装团队")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String installTeam;
	/**
	 * 运维团队
	 */
	@ApiModelProperty(value = "运维团队")
	private String operationTeam;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 站点状态，由站点下不同设备（逆变器、电池等）汇总，如果其中一个异常站点则异常
	 */
	@ApiModelProperty(value = "站点状态，由站点下不同设备（逆变器、电池等）汇总，如果其中一个异常站点则异常")
	private String plantStatus;

	@ApiModelProperty(value = "代理商id")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long operationCompanyId;

	@TableField(exist = false)
	private List<Long> createUserList;

	/**
	 * 运维人员id
	 */
	@ApiModelProperty(value = "运维人员id")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long operationUserId;

	/**
	 * 安装团队id
	 */
	@ApiModelProperty(value = "安装团队id")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long installTeamId;

	/**是否存在用户类告警(0/1:不存在/存在)*/
	@ApiModelProperty("是否存在用户类告警(0/1:不存在/存在)")
	private Integer existUserTypeAlarm;

	/**是否存在代理类告警(0/1:不存在/存在)*/
	@ApiModelProperty("是否存在代理类告警(0/1:不存在/存在)")
	private Integer existAgentTypeAlarm;

	/**
	 * 是否并机模式
	 * 0否
	 * 1是
	 * */
	@ApiModelProperty(value = "是否并机模式")
	private String isParallelMode;
}
