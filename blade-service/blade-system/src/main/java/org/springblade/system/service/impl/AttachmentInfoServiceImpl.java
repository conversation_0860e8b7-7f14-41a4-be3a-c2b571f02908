/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.springblade.system.service.impl;

import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.mapper.AttachmentInfoMapper;
import org.springblade.system.service.IAttachmentInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 附件表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Service
@AllArgsConstructor
public class AttachmentInfoServiceImpl extends BaseServiceImpl<AttachmentInfoMapper, AttachmentInfoEntity> implements IAttachmentInfoService {
	/**
	 * 通过businessIds查询附件信息
	 *
	 * @param businessIds 入参
	 * @return List<AttachmentInfoEntity>
	 * <AUTHOR>
	 * @since 2023/10/20 17:51
	 **/
	@Override
	public List<AttachmentInfoEntity> findByBusinessIds(List<Long> businessIds) {
		return baseMapper.findByBusinessIds(businessIds);
	}

	/**
	 * 批量修改附件信息
	 *
	 * @param attachmentInfoEntityList 入参
	 * <AUTHOR>
	 * @since 2023/10/24 16:45
	 **/
	@Override
	public void updateBatch(List<AttachmentInfoEntity> attachmentInfoEntityList,Long updateUserId) {
		baseMapper.updateBatch(attachmentInfoEntityList,updateUserId);
	}

}
