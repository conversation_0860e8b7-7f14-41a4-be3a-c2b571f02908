/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ota.service.IDeviceSoftwareVersionInfoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * 设备软件版本信息表 Feign实现类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@ApiIgnore()
@RestController
@AllArgsConstructor
public class DeviceSoftwareVersionInfoClient implements IDeviceSoftwareVersionInfoClient {

	private final IDeviceSoftwareVersionInfoService deviceSoftwareVersionInfoService;

	/**
	 * 获取OTA最终升级结果
	 *
	 * @param map 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:55
	 **/
	@Override
	@PostMapping(OBTAIN_UPGRADE_RESULTS_ADDRESS)
	public void obtainUpgradeResults(@RequestBody Map<String, String> map) {
		deviceSoftwareVersionInfoService.obtainUpgradeResults(map);
	}

	/**
	 * 获取OTA软件下发结果
	 *
	 * @param deviceSns 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:55
	 **/
	@Override
	@PostMapping(OBTAIN_UPGRADE_PUSH_RESULTS_ADDRESS)
	public void obtainUpgradePushResults(@RequestBody List<String> deviceSns) {
		deviceSoftwareVersionInfoService.obtainUpgradePushResults(deviceSns);
	}

	/**
	 * 告知app ota升级结果
	 *
	 * @param map
	 */
	@Override
	public void publishOtaUpgradeResultToApp(@RequestBody Map<String, Object> map) {
		deviceSoftwareVersionInfoService.publishOtaUpgradeResultToApp(map);
	}

	/**
	 * 脱机异常回写
	 *
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/3/5 17:46
	 **/
	@Override
	@PostMapping(OTA_OFFLINE_UPGRADE_EXCEPTION)
	public void otaOfflineUpgradeException(@RequestBody JSONObject jsonObject) {
		deviceSoftwareVersionInfoService.otaOfflineUpgradeException(jsonObject);
	}
}
