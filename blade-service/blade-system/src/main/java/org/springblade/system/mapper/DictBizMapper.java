/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.vo.DictBizVO;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface DictBizMapper extends BaseMapper<DictBiz> {

	/**
	 * 获取字典表对应中文
	 *
	 * @param code    字典编号
	 * @param dictKey 字典序号
	 * @return
	 */
	String getValue(String code, String dictKey);

	/**
	 * 获取字典表
	 *
	 * @param code 字典编号
	 * @return
	 */
	List<DictBiz> getList(String code,String language);

	/**
	 * 获取树形节点
	 *
	 * @return
	 */
	List<DictBizVO> tree();

	/**
	 * 获取树形节点
	 *
	 * @return
	 */
	List<DictBizVO> parentTree();

	List<DictBiz> getListAllLang(String code);

	List<DictBiz> getListByParentCode(@Param("code") String code,@Param("parentCode") String parentCode,
									  @Param("language") String language);

    String getValueByLang(@Param("code")String code,@Param("dictKey") String dictKey,@Param("language") String language);
	List<DictBiz> batchGetList(@Param("codeList") List<String> codeList);

	/**
	 * 获取字典表
	 *
	 * @param code 字典编号 key 字典key
	 * @return
	 */
	List<DictBiz> getDataByCodeAndKey(@Param("code") String code,@Param("key") String key,@Param("lang") String language);
	List<DictBiz> queryChildByDictKey(@Param("code")String code,@Param("dictKey") String dictKey,@Param("language") String language);
}
