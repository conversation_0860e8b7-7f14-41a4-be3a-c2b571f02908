package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-11-13 16:54
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("bladx_redis_sequence")
@Accessors(chain = true)
public class RedisSequence implements Serializable {

	/**业务标签*/
	@TableId
	private String bizTag;

	/**最开头 字母前缀*/
	private String prefixAbc;

	/**日期前缀*/
	private String prefixDate;

	/**当前编号*/
	private Long currentId;

	/**长度*/
	private Integer length;

	/**是否每日刷新，1 刷新  0 不刷新*/
	private Integer refresh;
}

