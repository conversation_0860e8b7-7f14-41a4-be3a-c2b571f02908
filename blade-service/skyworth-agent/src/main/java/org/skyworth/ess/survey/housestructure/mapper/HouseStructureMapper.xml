<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.survey.housestructure.mapper.HouseStructureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="houseStructureResultMap" type="org.skyworth.ess.survey.housestructure.entity.HouseStructureEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="type_of_install" property="typeOfInstall"/>
        <result column="type_of_install_remark" property="typeOfInstallRemark"/>
        <result column="access_easy" property="accessEasy"/>
        <result column="access_specify" property="accessSpecify"/>
        <result column="building_permit" property="buildingPermit"/>
        <result column="building_permit_remark" property="buildingPermitRemark"/>
        <result column="electrical_permit" property="electricalPermit"/>
        <result column="electrical_permit_remark" property="electricalPermitRemark"/>
        <result column="zoning_permit" property="zoningPermit"/>
        <result column="zoning_permit_remark" property="zoningPermitRemark"/>
        <result column="structure" property="structure"/>
        <result column="structure_img_biz_key" property="structureImgBizKey"/>
        <result column="structure_remark" property="structureRemark"/>
        <result column="frame_type" property="frameType"/>
        <result column="frame_type_img_biz_key" property="frameTypeImgBizKey"/>
        <result column="frame_type_remark" property="frameTypeRemark"/>
        <result column="house_type" property="houseType"/>
        <result column="house_type_img_biz_key" property="houseTypeImgBizKey"/>
        <result column="house_type_remark" property="houseTypeRemark"/>
        <result column="full_view_of_house_img_biz_key" property="fullViewOfHouseImgBizKey"/>
        <result column="full_view_of_enterance_img_biz_key" property="fullViewOfEnteranceImgBizKey"/>
        <result column="vertical_view_img_biz_key" property="verticalViewImgBizKey"/>
        <result column="roof_type" property="roofType"/>
        <result column="roof_type_img_biz_key" property="roofTypeImgBizKey"/>
        <result column="roof_type_remark" property="roofTypeRemark"/>
        <result column="roof_material" property="roofMaterial"/>
        <result column="roof_material_img_biz_key" property="roofMaterialImgBizKey"/>
        <result column="roof_size" property="roofSize"/>
        <result column="roof_face_direction" property="roofFaceDirection"/>
        <result column="roof_face_direction_img_biz_key" property="roofFaceDirectionImgBizKey"/>
        <result column="roof_face_side_angel" property="roofFaceSideAngel"/>
        <result column="roof_face_side_slope" property="roofFaceSideSlope"/>
        <result column="roof_gutter_img_biz_key" property="roofGutterImgBizKey"/>
        <result column="roof_occlusion" property="roofOcclusion"/>
        <result column="roof_occlusion_img_biz_key" property="roofOcclusionImgBizKey"/>
        <result column="mounting_structure" property="mountingStructure"/>
        <result column="mounting_structure_other" property="mountingStructureOther"/>
        <result column="mounting_structure_img_biz_key" property="mountingStructureImgBizKey"/>
        <result column="panel_type" property="panelType"/>
        <result column="panel_type_other" property="panelTypeOther"/>
        <result column="panel_type_img_biz_key" property="panelTypeImgBizKey"/>
        <result column="panel_orientation" property="panelOrientation"/>
        <result column="panel_orientation_other" property="panelOrientationOther"/>
        <result column="panel_orientation_img_biz_key" property="panelOrientationImgBizKey"/>
        <result column="relevant_doc" property="relevantDoc"/>
        <result column="relevant_doc_biz_key" property="relevantDocBizKey"/>
        <result column="special_request" property="specialRequest"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectHouseStructurePage" resultMap="houseStructureResultMap">
        select * from survey_house_structure where is_deleted = 0
    </select>


    <select id="exportHouseStructure" resultType="org.skyworth.ess.survey.housestructure.excel.HouseStructureExcel">
        SELECT * FROM survey_house_structure ${ew.customSqlSegment}
    </select>

</mapper>
