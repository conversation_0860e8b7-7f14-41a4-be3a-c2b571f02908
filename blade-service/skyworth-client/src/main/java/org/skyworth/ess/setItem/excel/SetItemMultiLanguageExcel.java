/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.excel;


import lombok.Data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * APP设置项配置名称多语言 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SetItemMultiLanguageExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设置项ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项ID")
	private Long itemId;
	/**
	 * 设置项语言类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项语言类型")
	private String itemLanguageType;
	/**
	 * 设置项语言
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项语言")
	private String itemLanguageName;

}
