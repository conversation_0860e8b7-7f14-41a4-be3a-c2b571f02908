package org.springblade.system.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.system.entity.User;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserInfoMappingVO extends User implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 源头userId
	 */
	private Long sourceUserId;
	/**
	 * 映射userId
	 */
	private Long mappingUserId;
}
