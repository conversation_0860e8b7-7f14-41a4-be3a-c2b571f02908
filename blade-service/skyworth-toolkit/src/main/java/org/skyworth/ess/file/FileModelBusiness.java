/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.file;


import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.microsoft.azure.storage.StorageException;
import com.microsoft.azure.storage.blob.CloudBlobContainer;
import com.microsoft.azure.storage.blob.CloudBlockBlob;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.config.BlobConfig;
import org.skyworth.ess.ota.feign.IFileModelBusinessClient;
import org.skyworth.ess.ota.vo.FileModelVO;
import org.skyworth.ess.ota.vo.FileResultVO;
import org.skyworth.ess.utils.BlobHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.FileUtil;
import org.springblade.common.utils.UniqueIdentifierGenerator;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.resource.feign.ISmsClient;
import org.springblade.resource.vo.AttachVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.util.List;
import java.util.*;
import java.util.concurrent.*;

/**
 * 附件上传
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-09-27 10:31
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("file")
public class FileModelBusiness implements IFileModelBusinessClient {

	private static final Logger logger = LoggerFactory.getLogger(FileModelBusiness.class);
	private final BlobConfig blobConfig;
	private final BladeRedis bladeRedis;
	@Value("${oss.domestic:false}")
	private boolean ossDomestic;
	private final ISmsClient smsClient;
	/**
	 * 设置缩略图的宽高
	 */
	private static final int THUMBNAIL_WIDTH = 150;
	/**
	 * 设置缩略图的宽高
	 */
	private static final int THUMBNAIL_HEIGHT = 150;
	/**
	 * 图片contentType
	 */
	private static final String IMAGE_CONTENT_TYPE = "image/jpg;image/jpeg;image/png;";
	/**
	 * 图片后缀
	 */
	private static final String IMAGE_SUFFIX = ".jpg;.jpeg;.png;.heif;.heic;";

	private static final String OTA_STORAGE_CONTAINER = "ota-storage";

	/**
	 * 上传附件
	 *
	 * @param container 文件桶
	 * @param type      文件类型
	 * @param file      入参
	 * @return R
	 * <AUTHOR>
	 * @since 2023/9/27 17:16
	 **/
	@Override
	@RequestMapping(value = "upload", method = RequestMethod.POST)
	public R<FileResultVO> uploadFile(@RequestParam("container") String container, @RequestParam("type") int type,
									  @RequestParam(value = "businessId", required = false) Long businessId,
									  @RequestPart("file") MultipartFile[] file) {
		logger.info("FileModelBusiness : uploadFile : file path is {}, ", file);
		FileResultVO fileResultVO = new FileResultVO();
		List<FileModelVO> fileModelVOList = new ArrayList<>();
		if (businessId == null) {
			businessId = IdWorker.getId();
		}
		try {
			if (file != null) {
				// 国内华为云obs
				if (ossDomestic) {
					List<AttachVO> attachVOList = smsClient.putFiles(container, file).getData();
					objectConversion(attachVOList, fileModelVOList, businessId, container);
				}// 国外微软云Azure Clob
				else {
					azureClobUploadFile(container, type, businessId, file, fileModelVOList);
				}
			}
		} catch (Exception e) {
			logger.error("File upload exception,error:", e);
			throw new BusinessException("toolkit.file.upload.error", e.getMessage());
		}
		fileResultVO.setFileResultVOList(fileModelVOList);
		fileResultVO.setBusinessId(businessId);
		return R.data(fileResultVO);
	}

	/**
	 * 海外微软云clob上传文件
	 *
	 * @param container       容器
	 * @param type            类型
	 * @param businessId      主键id
	 * @param multipartFile   文件流
	 * @param fileModelVOList 入参
	 * <AUTHOR>
	 * @since 2024/2/28 9:38
	 **/
	private void azureClobUploadFile(String container, int type, Long businessId, MultipartFile[] multipartFile, List<FileModelVO> fileModelVOList) {
		if (multipartFile == null || multipartFile.length == 0) {
			return;
		}
		CloudBlobContainer blobContainer = BlobHelper.getBlobContainer(container.toLowerCase(), blobConfig);
		ThreadPoolExecutor threadPoolExecutor = getThreadPoolExecutor();
		List<Future<?>> futures = new ArrayList<>();
		for (MultipartFile tempMultipartFile : multipartFile) {
			if (!tempMultipartFile.isEmpty()) {
				final MultipartFile finalTempMultipartFile = tempMultipartFile;
				futures.add(threadPoolExecutor.submit(() -> {
					try (InputStream in = finalTempMultipartFile.getInputStream()) {
						processFileUpload(finalTempMultipartFile, blobContainer, container, type, businessId, fileModelVOList, in);
					} catch (Exception e) {
						logger.error("File upload exception,error:", e);
						// 包含原始异常
						throw new BusinessException("toolkit.file.upload.error", e.getMessage());
					}
				}));
			}
		}
		waitForUploadsToComplete(futures);
		threadPoolExecutor.shutdown();
	}

	@NotNull
	private ThreadPoolExecutor getThreadPoolExecutor() {
		// 使用配置化的线程池参数
		int corePoolSize = 5;
		int maximumPoolSize = 10;
		long keepAliveTime = 1;
		TimeUnit unit = TimeUnit.MINUTES;
		LinkedBlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(100);
		return new ThreadPoolExecutor(
			corePoolSize,
			maximumPoolSize,
			keepAliveTime,
			unit,
			workQueue,
			Executors.defaultThreadFactory(),
			new ThreadPoolExecutor.AbortPolicy()
		);
	}

	/**
	 * 处理文件上传。
	 * 将上传的文件存储到Azure云存储服务，并更新文件模型列表。
	 *
	 * @param multipartFile   上传的文件对象，包含文件内容和元数据。
	 * @param blobContainer   Azure云存储的容器对象，用于文件存储。
	 * @param container       存储文件的Azure容器名称。
	 * @param type            文件类型，用于分类和处理不同的文件。
	 * @param businessId      业务ID，与上传的文件相关联。
	 * @param fileModelVOList 文件模型列表，用于记录上传文件的元数据信息。
	 * @param in              文件输入流，用于读取文件内容。
	 * @throws Exception 如果文件上传或处理过程中出现错误，则抛出异常。
	 */
	private void processFileUpload(MultipartFile multipartFile, CloudBlobContainer blobContainer, String container, int type, Long businessId, List<FileModelVO> fileModelVOList, InputStream in) throws Exception {
		// 文件上传处理逻辑
		String uniqueId = UniqueIdentifierGenerator.generateUniqueId();
		String fileExtension = BlobHelper.getFileExtension(Objects.requireNonNull(multipartFile.getOriginalFilename())).toLowerCase();
		String preName = BlobHelper.getBlobPreName(type, false).toLowerCase();
		String blobName = preName + uniqueId + fileExtension;
		CloudBlockBlob blob = blobContainer.getBlockBlobReference(blobName);
		blob.getProperties().setContentType(multipartFile.getContentType());
		blob.upload(in, multipartFile.getSize());
		blob.downloadAttributes();
		/**
		 * 验证上传的文件。
		 * 此方法的目的是对通过multipart/form-data形式上传的文件进行验证。
		 * 它主要检查上传文件的合法性，例如文件类型、大小等，以确保上传的文件符合预期的要求。
		 * 这种验证是必要的，因为它可以防止非法或有害文件的上传，从而维护系统的安全性和稳定性。
		 */
		validateUploadedFile(blob, multipartFile);
		FileModelVO fileModelVO = createFileModelVO(multipartFile, blob, blobName, container, businessId);
		// 上传文件需要设置文件校验和
		if (OTA_STORAGE_CONTAINER.equals(container)) {
			// 确保这里不会重新读取整个文件
			fileModelVO.setCheckSum(FileUtil.calculateChecksum(in));
		}
		if (isImageContentType(Objects.requireNonNull(multipartFile.getContentType())) || IMAGE_SUFFIX.contains(fileExtension)) {
			uploadThumbnailImages(type, multipartFile, blobContainer, fileModelVO);
		}
		synchronized (fileModelVOList) {
			fileModelVOList.add(fileModelVO);
		}
	}

	/**
	 * 验证上传的文件大小与预期大小是否一致。
	 * 若大小不符，删除无效文件并抛出异常。
	 *
	 * @param blob          代表Azure Blob存储中上传文件的CloudBlockBlob对象。
	 * @param multipartFile 代表应用程序中上传文件的MultipartFile对象。
	 * @throws BusinessException 文件大小验证失败时抛出此异常。
	 * @throws StorageException  与Azure Blob存储交互时发生错误，抛出此异常。
	 */
	private void validateUploadedFile(CloudBlockBlob blob, MultipartFile multipartFile) throws BusinessException, StorageException {
		// 获取Azure Blob存储中文件的大小
		long blobSize = blob.getProperties().getLength();
		// 获取上传文件的大小
		long localSize = multipartFile.getSize();
		// 检查文件大小是否匹配
		if (blobSize != localSize) {
			// 记录上传失败信息
			logger.info("AzureBlobFileService : upload : Recording uploaded fail");
			// 尝试删除损坏的文件，若删除失败则记录警告信息
			if (!blob.deleteIfExists()) {
				logger.warn("Failed to delete the corrupted blob: " + blob.getName());
			}
			// 抛出服务异常，指示上传失败
			throw new BusinessException("toolkit.azure.blob.file.record.upload.fail");
		}
	}


	/**
	 * 根据上传的multipart文件和Azure Blob存储的相关信息，创建文件模型VO对象。
	 *
	 * @param multipartFile 上传的multipart文件对象。
	 * @param blob          Azure Blob存储中的块 blob对象。
	 * @param blobName      blob在存储中的名称。
	 * @param container     blob所在的容器名称。
	 * @param businessId    业务ID，用于关联文件和业务。
	 * @return 文件模型VO对象，包含文件相关信息。
	 * @throws InvalidKeyException 如果Azure存储账户密钥无效。
	 * @throws StorageException    如果与Azure存储通信发生错误。
	 */
	private FileModelVO createFileModelVO(MultipartFile multipartFile, CloudBlockBlob blob, String blobName, String container, Long businessId) throws InvalidKeyException, StorageException {
		FileModelVO fileModelVO = new FileModelVO();
		fileModelVO.setFileName(multipartFile.getOriginalFilename());
		fileModelVO.setFileUrl(blob.getUri().toString());
		fileModelVO.setAzureKey(blobName);
		fileModelVO.setAttachSize(blob.getProperties().getLength());
		fileModelVO.setContainer(container);
		fileModelVO.setBusinessId(businessId);
		fileModelVO.setSasToken(BlobHelper.getSasToken(blob, 1, container + blobName, bladeRedis));
		return fileModelVO;
	}

	/**
	 * 判断给定的文件内容类型是否为图片类型。
	 *
	 * @param contentType 文件的内容类型。
	 * @return 如果是图片类型，则返回true；否则返回false。
	 */
	private boolean isImageContentType(String contentType) {
		return IMAGE_CONTENT_TYPE.contains(contentType.toLowerCase());
	}

	/**
	 * 等待所有上传任务完成。
	 *
	 * @param futures 上传任务的未来对象列表，表示异步上传的结果。
	 */
	private void waitForUploadsToComplete(List<Future<?>> futures) {
		for (Future<?> future : futures) {
			try {
				future.get();
			} catch (InterruptedException | ExecutionException e) {
				logger.error("Error occurred during file upload.", e);
			}
		}
	}


	/**
	 * 上传缩略图
	 *
	 * @param type              类型
	 * @param tempMultipartFile 文件
	 * @param blobContainer     blob
	 * <AUTHOR>
	 * @since 2023/9/27 17:15
	 **/
	private void uploadThumbnailImages(int type, MultipartFile tempMultipartFile, CloudBlobContainer blobContainer, FileModelVO fileModelVO) throws IOException {
		// 获取原图的宽度和高度
		BufferedImage bufferedImage = ImageIO.read(tempMultipartFile.getInputStream());
		// 原图宽度
		int originalDrawingWidth = bufferedImage.getWidth();
		// 原图高度
		int originalDrawingHeight = bufferedImage.getHeight();
		// 缩略图宽度
		int thumbnailWidth = THUMBNAIL_WIDTH;
		// 缩略图高度
		int thumbnailHeight = THUMBNAIL_HEIGHT;
		// 计算缩略图高度,宽度
		if (originalDrawingWidth / originalDrawingHeight > thumbnailWidth / thumbnailHeight) {
			thumbnailHeight = originalDrawingHeight * thumbnailWidth / originalDrawingWidth;
		} else {
			thumbnailWidth = originalDrawingWidth * thumbnailHeight / originalDrawingHeight;
		}
		// 上传缩略图
		extracted(type, tempMultipartFile, blobContainer, fileModelVO, thumbnailWidth, thumbnailHeight);
	}

	/**
	 * 缩略图
	 *
	 * @param type              类型
	 * @param tempMultipartFile 文件流
	 * @param blobContainer     容器
	 * @param fileModelVO       文件对象
	 * @param thumbnailWidth    缩略图宽度
	 * @param thumbnailHeight   入参
	 * <AUTHOR>
	 * @since 2024/1/4 16:21
	 **/
	private void extracted(int type, MultipartFile tempMultipartFile, CloudBlobContainer blobContainer, FileModelVO fileModelVO, int thumbnailWidth, int thumbnailHeight) throws IOException {
		//上传缩略图，并上传至AzureStorage
		BufferedImage img = new BufferedImage(thumbnailWidth, thumbnailHeight, BufferedImage.TYPE_INT_RGB);
		img.createGraphics().drawImage(ImageIO.read(tempMultipartFile.getInputStream()).getScaledInstance(thumbnailWidth, thumbnailHeight, Image.SCALE_SMOOTH), 0, 0, null);
		ByteArrayOutputStream thumbnailStream = new ByteArrayOutputStream();
		ImageIO.write(img, "jpg", thumbnailStream);
		InputStream inputStream = null;
		try {
			inputStream = new ByteArrayInputStream(thumbnailStream.toByteArray());
			String thumbnailPreName = BlobHelper.getBlobPreName(type, true).toLowerCase();
			String thumbnailCheckSum = UniqueIdentifierGenerator.generateUniqueId();
			String blobThumbnail = thumbnailPreName + thumbnailCheckSum + ".jpg";
			CloudBlockBlob thumbnailBlob = blobContainer.getBlockBlobReference(blobThumbnail);
			thumbnailBlob.getProperties().setContentType("image/jpeg");
			thumbnailBlob.upload(inputStream, thumbnailStream.toByteArray().length);
			fileModelVO.setThumbnailUrl(thumbnailBlob.getUri().toString());
			fileModelVO.setThumbnailAzureKey(blobThumbnail);
			fileModelVO.setThumbnailSasToken(BlobHelper.getSasToken(thumbnailBlob, 1, blobContainer + blobThumbnail, bladeRedis));
		} catch (Exception e) {
			logger.error("Abnormal uploading thumbnail,error:", e);
			throw new BusinessException("toolkit.azure.thumbnail.upload.error", e.getMessage());
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (Exception e) {
					logger.error("File stream closing exception,error:", e);
				}
			}
		}
	}

	/**
	 * 获取文件下载链接
	 *
	 * @param map 入参
	 * @return R<String>
	 * <AUTHOR>
	 * @since 2023/10/8 16:22
	 **/
	@GetMapping("query/download/url")
	@ApiOperation(value = "获取下载链接", notes = "获取下载链接")
	public R<String> queryDownloadUrl(@RequestParam Map<String, String> map) {
		String container = map.get("container");
		String azureKey = map.get("azureKey");
		if (StringUtils.isAnyBlank(container, azureKey)) {
			throw new BusinessException("toolkit.azure.paramater.cannot.empty");
		}
		try {
			CloudBlobContainer blobContainer = BlobHelper.getBlobContainer(container.toLowerCase(), blobConfig);
			CloudBlockBlob blob = blobContainer.getBlockBlobReference(azureKey);
			// 从缓存获取sasToken
			String soaToken = bladeRedis.get(container + azureKey);
			if (StringUtils.isBlank(soaToken)) {
				soaToken = BlobHelper.getSasToken(blob, 1, container + azureKey, bladeRedis);
			}
			return R.data(blob.getUri().toString() + CommonConstant.SYMBOL_QUESTION_MARK + soaToken);
		} catch (Exception e) {
			logger.error("AzureBlobFileService : downloadFile : exception,", e);
			throw new BusinessException("toolkit.azure.blob.file.download.fail", e.getMessage());
		}
	}


	/**
	 * 删除附件
	 * 注意：如果是图片，则需要同时删除缩略图
	 *
	 * @param map 入参
	 * @return void
	 * <AUTHOR>
	 * @since 2023/9/27 18:45
	 **/
	@GetMapping("/delete")
	@ApiOperation(value = "删除附件", notes = "传入map")
	public R<Boolean> delete(@RequestParam Map<String, String> map) throws Exception {
		String container = map.get("container");
		String azureKey = map.get("azureKey");
		String thumbnailAzureKey = map.get("thumbnailAzureKey");
		if (StringUtils.isAnyBlank(container, azureKey)) {
			throw new BusinessException("toolkit.azure.paramater.cannot.empty");
		}
		CloudBlobContainer blobContainer = BlobHelper.getBlobContainer(container.toLowerCase(), blobConfig);
		// 传入要blob的path
		blobContainer.getBlockBlobReference(azureKey).deleteIfExists();
		// 如果存在缩略图，则删除原件+缩略图
		if (StringUtils.isNotBlank(thumbnailAzureKey)) {
			blobContainer.getBlockBlobReference(thumbnailAzureKey).deleteIfExists();
		}
		return R.data(true);
	}

	/**
	 * 华为obs对象转换系统对象
	 *
	 * @param attachVOList    华为云oss对象
	 * @param fileModelVOList 附件对象
	 * @param businessId      唯一批次id
	 * @param container       入参
	 * <AUTHOR>
	 * @since 2024/3/20 19:26
	 **/
	private void objectConversion(List<AttachVO> attachVOList, List<FileModelVO> fileModelVOList, Long businessId, String container) {
		Optional.ofNullable(attachVOList).orElse(new ArrayList<>()).forEach(a -> {
			FileModelVO fileModelVO = new FileModelVO();
			fileModelVO.setFileUrl(a.getLink());
			fileModelVO.setFileName(a.getName());
			fileModelVO.setContainer(container);
			fileModelVO.setBusinessId(businessId);
			fileModelVO.setAttachSize(a.getAttachSize());
			fileModelVO.setAzureKey(a.getExtension());
			fileModelVO.setSasToken(a.getSasToken());
			fileModelVO.setThumbnailSasToken(a.getThumbnailSasToken());
			fileModelVO.setThumbnailUrl(a.getThumbnailLink());
			fileModelVO.setThumbnailAzureKey(a.getThumbnailExtension());
			fileModelVO.setCheckSum(a.getCheckSum());
			fileModelVOList.add(fileModelVO);
		});

	}
}
