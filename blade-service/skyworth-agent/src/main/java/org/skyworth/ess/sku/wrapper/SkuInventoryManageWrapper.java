/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.sku.entity.SkuInventoryManageEntity;
import org.skyworth.ess.sku.vo.SkuInventoryManageVO;
import java.util.Objects;

/**
 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public class SkuInventoryManageWrapper extends BaseEntityWrapper<SkuInventoryManageEntity, SkuInventoryManageVO>  {

	public static SkuInventoryManageWrapper build() {
		return new SkuInventoryManageWrapper();
 	}

	@Override
	public SkuInventoryManageVO entityVO(SkuInventoryManageEntity skuInventoryManage) {
		SkuInventoryManageVO skuInventoryManageVO = Objects.requireNonNull(BeanUtil.copy(skuInventoryManage, SkuInventoryManageVO.class));

		//User createUser = UserCache.getUser(skuInventoryManage.getCreateUser());
		//User updateUser = UserCache.getUser(skuInventoryManage.getUpdateUser());
		//skuInventoryManageVO.setCreateUserName(createUser.getName());
		//skuInventoryManageVO.setUpdateUserName(updateUser.getName());

		return skuInventoryManageVO;
	}


}
