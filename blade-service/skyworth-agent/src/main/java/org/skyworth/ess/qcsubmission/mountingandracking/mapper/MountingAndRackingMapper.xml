<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.qcsubmission.mountingandracking.mapper.MountingAndRackingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="mountingandrackingResultMap" type="org.skyworth.ess.qcsubmission.mountingandracking.entity.MountingAndRackingEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="mounting_hardware_img_biz_key" property="mountingHardwareImgBizKey"/>
        <result column="confirm_clamps_brackets_img_biz_key" property="confirmClampsBracketsImgBizKey"/>
        <result column="mounting_verify_panel_tilt_img_biz_key" property="mountingVerifyPanelTiltImgBizKey"/>
        <result column="all_roof_joints_img_biz_key" property="allRoofJointsImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectMountingAndRackingPage" resultMap="mountingandrackingResultMap">
        select * from qc_mounting_and_racking where is_deleted = 0
    </select>


    <select id="exportMountingAndRacking" resultType="org.skyworth.ess.qcsubmission.mountingandracking.excel.MountingAndRackingExcel">
        SELECT * FROM qc_mounting_and_racking ${ew.customSqlSegment}
    </select>

</mapper>
