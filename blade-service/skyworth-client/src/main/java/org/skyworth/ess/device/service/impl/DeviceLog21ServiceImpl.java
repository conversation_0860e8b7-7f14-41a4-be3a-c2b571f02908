package org.skyworth.ess.device.service.impl;

import org.skyworth.ess.device.entity.DeviceLog21;
import org.skyworth.ess.device.mapper.DeviceLog21Mapper;
import org.skyworth.ess.device.service.IDeviceLog21Service;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeviceLog21ServiceImpl extends BaseServiceImpl<DeviceLog21Mapper, DeviceLog21> implements IDeviceLog21Service {


    @Override
    public List<DeviceLog21> selectDataByLatestTime() {
        return baseMapper.selectDataByLatestTime();
    }
}
