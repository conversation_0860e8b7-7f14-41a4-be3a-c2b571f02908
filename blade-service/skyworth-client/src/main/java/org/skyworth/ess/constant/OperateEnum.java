package org.skyworth.ess.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-11-14 13:44
 **/
@Getter
@AllArgsConstructor
public enum OperateEnum {
	/**
	 * 新增操作
	 */
	ADD("add", "Add", "新增"),
	/**
	 * 修改操作
	 */
	EDIT("edit", "Edit", "修改"),
	/**
	 * 删除操作
	 */
	DELETE("delete", "Delete", "删除");
	final String code;
	final String enDesc;
	final String cnDesc;

	/**
	 * 匹配枚举值
	 *
	 * @param code 名称
	 * @return BladeUserEnum
	 */
	public static OperateEnum of(String code) {
		if (code == null) {
			return null;
		}
		OperateEnum[] values = OperateEnum.values();
		for (OperateEnum operateEnum : values) {
			if (operateEnum.code.equals(code)) {
				return operateEnum;
			}
		}
		return null;
	}
}
