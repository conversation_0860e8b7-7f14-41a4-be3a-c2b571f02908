/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.springblade.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.vo.AttachmentInfoVO;

import java.util.List;

/**
 * 附件表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
public interface AttachmentInfoMapper extends BaseMapper<AttachmentInfoEntity> {

	/**
	 * 批量查询附件信息
	 *
	 * @param businessIds 入参
	 * @return List<AttachmentInfoVO>
	 * <AUTHOR>
	 * @since 2023/10/7 11:26
	 **/
	List<AttachmentInfoEntity> findByBusinessIds(@Param("ids") List<Long> businessIds);

	/**
	 * 批量修改附件信息
	 * @param attachmentInfoEntityList 入参
	 * <AUTHOR>
	 * @since 2023/10/24 16:47
	 **/
	void updateBatch(@Param("list") List<AttachmentInfoEntity> attachmentInfoEntityList,@Param("userId")Long userId);

}
