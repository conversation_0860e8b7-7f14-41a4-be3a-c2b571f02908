package org.springblade.system.feign;




import org.junit.Test;
import org.junit.runner.RunWith;
import org.springblade.core.context.BladeRunnableWrapper;
import org.springblade.core.tool.api.R;
import org.springblade.system.SystemApplication;
import org.springblade.system.entity.User;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


/**
 * 订单表列表 视图实体类
 * org.springblade.system.feign
 *
 * <AUTHOR>
 * @since 2023/11/6 - 11 - 06
 */

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SystemApplication.class)
class UserClientTest {

	private UserClient userClient1;

	@Test
	void userInfoById() {
		Long id = 1123598821738675201L;
		R<User> user = userClient1.userInfoById(id);
		System.out.println(user.toString());
	}
}
