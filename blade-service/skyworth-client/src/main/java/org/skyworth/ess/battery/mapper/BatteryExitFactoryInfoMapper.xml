<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.battery.mapper.BatteryExitFactoryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BatteryExitFactoryInfoResultMap" type="org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity">
        <result column="id" property="id"/>
        <result column="battery_serial_number" property="batterySerialNumber"/>
        <result column="battery_type" property="batteryType"/>
        <result column="rated_battery_voltage" property="ratedBatteryVoltage"/>
        <result column="rated_battery_capacity" property="ratedBatteryCapacity"/>
        <result column="rated_battery_energy" property="ratedBatteryEnergy"/>
        <result column="single_capacity" property="singleCapacity"/>
        <result column="single_series_paralleling_number" property="singleSeriesParallelingNumber"/>
        <result column="quality_guarantee_year" property="qualityGuaranteeYear"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="activation_date" property="activationDate"/>
        <result column="warranty_start_date" property="warrantyStartDate"/>
        <result column="warranty_deadline" property="warrantyDeadline"/>
        <result column="company" property="company"/>
    </resultMap>


    <select id="selectBatteryExitFactoryInfoPage" resultMap="BatteryExitFactoryInfoResultMap">
        select * from battery_exit_factory_info where is_deleted = 0 order by create_time desc,id desc
    </select>


    <select id="exportBatteryExitFactoryInfo" resultType="org.skyworth.ess.battery.excel.BatteryExitFactoryInfoExcel">
        SELECT * FROM battery_exit_factory_info ${ew.customSqlSegment}
    </select>

    <select id="queryAppBatteryExitFactoryInfo" resultType="org.skyworth.ess.app.vo.AppBatteryExitFactoryInfoVO">
        select d.id as batteryMapDeviceId,c.battery_status as batteryStatus,f.quality_guarantee_year as
        newQualityQuaranteeYear ,
        d.battery_serial_number as batterySerialNumber ,f.rated_battery_energy as ratedBatteryEnergy,
        f.exit_factory_date as exitFactoryDate ,
        f.warranty_start_date as warrantyStartDate,f.quality_guarantee_year as qualityGuaranteeYear
        from battery_map_device d
        left join battery_exit_factory_info f on d.battery_serial_number =f.battery_serial_number and f.is_deleted =0
        left join battery_current_status c on d.plant_id =c.plant_id and d.device_serial_number = c.device_serial_number
        and c.is_deleted =0
        where d.is_deleted =0
        and d.plant_id=#{plantId}
        <if test="deviceSerialNumber!=null and deviceSerialNumber!=''">
        and d.device_serial_number = #{deviceSerialNumber}
        </if>
    </select>

    <update id="deleteLogicBatteryExitFactory">
        update battery_exit_factory_info set is_deleted = 1, update_user = #{updateUser},
        update_user_account = #{updateUserAccount},update_time=now() where id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>

    <select id="queryByBatterySerialNumbers" resultMap="BatteryExitFactoryInfoResultMap">
        SELECT id, battery_serial_number,rated_battery_energy,battery_type,company
        FROM battery_exit_factory_info where is_deleted = 0
        and battery_serial_number in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateBatchBySn" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update battery_exit_factory_info set

            <if test="item.batteryType!=null and item.batteryType!=''">
                battery_type=#{item.batteryType},
            </if>
            <if test="item.ratedBatteryVoltage!=null and item.ratedBatteryVoltage!=''">
                rated_battery_voltage=#{item.ratedBatteryVoltage},
            </if>
            <if test="item.ratedBatteryCapacity!=null and item.ratedBatteryCapacity!=''">
                rated_battery_capacity=#{item.ratedBatteryCapacity},
            </if>
            <if test="item.ratedBatteryEnergy!=null and item.ratedBatteryEnergy!=''">
                rated_battery_energy=#{item.ratedBatteryEnergy},
            </if>
            <if test="item.singleCapacity!=null and item.singleCapacity!=''">
                single_capacity=#{item.singleCapacity},
            </if>
            <if test="item.singleSeriesParallelingNumber!=null and item.singleSeriesParallelingNumber!=''">
                single_series_paralleling_number=#{item.singleSeriesParallelingNumber},
            </if>
            <if test="item.qualityGuaranteeYear!=null and item.qualityGuaranteeYear!=''">
                quality_guarantee_year=#{item.qualityGuaranteeYear},
            </if>
            <if test="item.updateUserAccount!=null and item.updateUserAccount!=''">
                update_user_account=#{item.updateUserAccount},
            </if>
            <if test="item.updateUser!=null and item.updateUser!=''">
                update_user=#{item.updateUser},
            </if>
            <if test="item.status!=null">
                status =#{item.status},
            </if>
            <if test="item.activationDate!=null">
                activation_date =#{item.activationDate},
            </if>
            <if test="item.warrantyStartDate!=null">
                warranty_start_date =#{item.warrantyStartDate},
            </if>
            <if test="item.warrantyDeadline!=null">
                warranty_deadline =#{item.warrantyDeadline},
            </if>
            <if test="item.exitFactoryDate!=null">
                exit_factory_date =#{item.exitFactoryDate},
            </if>
            <if test="item.company!=null">
                company =#{item.company},
            </if>
            update_time=now()
            where battery_serial_number = #{item.batterySerialNumber}
        </foreach>
    </update>

    <select id="queryTotalInstalledPower" resultType="java.math.BigDecimal">
        select ifnull(sum(befi.rated_battery_energy), 0)
        from battery_exit_factory_info befi
        where befi.status = 1
        and befi.is_deleted = 0
    </select>

    <select id="deviceInformation" resultType="org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO">
        select bmd.battery_serial_number as batterySerialNumber,
        bmd.create_time as createTime,
        befi.battery_type as batteryType,
        befi.rated_battery_voltage as ratedBatteryVoltage,
        befi.rated_battery_capacity as ratedBatteryCapacity,
        befi.rated_battery_energy as ratedBatteryEnergy,
        befi.single_capacity as singleCapacity,
        befi.single_series_paralleling_number as singleSeriesParallelingNumber,
        befi.quality_guarantee_year as qualityGuaranteeYear,
        bcs.soh as soh,
        bcs.battery_maximum_cycles_times as batteryMaximumCyclesTimes
        from battery_map_device bmd
        left join battery_exit_factory_info befi on
        bmd.battery_serial_number = befi.battery_serial_number
        and befi.is_deleted = 0
        left join battery_current_status bcs on bmd.plant_id = bcs.plant_id and bcs.is_deleted = 0 and bcs.device_serial_number = bmd.device_serial_number
        where bmd.is_deleted = 0
        and bmd.plant_id = #{qc.plantId}
        and bmd.device_serial_number = #{qc.deviceSerialNumber}
    </select>

    <select id="deviceInformationIsDelete" resultType="org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO">
        select bmd.battery_serial_number as batterySerialNumber,
        bmd.create_time as createTime,
        befi.battery_type as batteryType,
        befi.rated_battery_voltage as ratedBatteryVoltage,
        befi.rated_battery_capacity as ratedBatteryCapacity,
        befi.rated_battery_energy as ratedBatteryEnergy,
        befi.single_capacity as singleCapacity,
        befi.single_series_paralleling_number as singleSeriesParallelingNumber,
        befi.quality_guarantee_year as qualityGuaranteeYear,
        bcs.soh as soh,
        bcs.battery_maximum_cycles_times as batteryMaximumCyclesTimes
        from battery_map_device bmd
        left join battery_exit_factory_info befi on
        bmd.battery_serial_number = befi.battery_serial_number
        and befi.is_deleted = 0
        left join battery_current_status bcs on bmd.plant_id = bcs.plant_id and bcs.is_deleted = 0
        where bmd.is_deleted = 1
        and bmd.plant_id = #{qc.plantId}
        and bmd.device_serial_number = #{qc.deviceSerialNumber}
    </select>
</mapper>
