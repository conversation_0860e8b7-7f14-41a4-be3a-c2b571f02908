/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * wifi棒对应站点表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@TableName("wifi_stick_plant")
@ApiModel(value = "WifiStickPlant对象", description = "wifi棒对应站点表")
@EqualsAndHashCode(callSuper = true)
public class WifiStickPlantEntity extends TenantEntity {

	/**
	 * wifi棒SN
	 */
	@ApiModelProperty(value = "wifi棒SN")
	private String wifiStickSerialNumber;
	/**
	 * wifi棒版本
	 */
	@ApiModelProperty(value = "wifi棒版本")
	private String wifiStickVersion;
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 逆变器/设备SN
	 */
	@ApiModelProperty(value = "逆变器/设备SN")
	private String deviceSerialNumber;
	/**
	 * wifi棒状态（1在线；0离线）
	 */
	@ApiModelProperty(value = "wifi棒状态（1在线；0离线）")
	private String wifiStickStatus;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "心跳时间")
	private String heartBeatTime;

	@ApiModelProperty(value = "是否在通过平台开机过。1否0是")
	private Integer startupByBackstage;

	@ApiModelProperty(value = "该逆变器是否配网成功。1否0是")
	private Integer inverterConfigureNetwork;

	@TableField(exist = false)
	private String timeZone;

	// 设备上报并机标识
	@TableField(exist = false)
	private String parallelModeFunction;
	// 设备是否在线 1在线 0离线
	@TableField(exist = false)
	private String onLineStatus;
}
