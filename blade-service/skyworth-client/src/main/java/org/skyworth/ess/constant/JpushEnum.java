package org.skyworth.ess.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 极光推送语言字典
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum JpushEnum {

	 /**
	 * 电网缺失英语描述
	 * */
	 GridOFFEN("en","Grid is OFF on [${name}]."),
	/**
	 * 电网恢复英语描述
	 * */
	 GridONEN("en","Grid is resumed on [${name}]."),
	/**
	 * 电网缺失德文描述
	 * */
	 GridOFFDE("de","Gitter ist AUS an [${name}]."),
	/**
	 * 电网恢复德文描述
	 * */
	 GridONDE("de","Gitter wird fortgesetzt [${name}].");


	 private final String language;

	 private final String alert;

	 public final static List<String> LANGUAGE_LIST = Arrays.asList("en","de");


}
