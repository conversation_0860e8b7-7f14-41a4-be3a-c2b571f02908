<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.battery.mapper.BatteryCurrentStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="batteryCurrentStatusResultMap" type="org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="battery_soc" property="batterySoc"/>
        <result column="battery_voltage" property="batteryVoltage"/>
        <result column="battery_current" property="batteryCurrent"/>
        <result column="battery_power" property="batteryPower"/>
        <result column="battery_temperature" property="batteryTemperature"/>
        <result column="battery_daily_charge_energy" property="batteryDailyChargeEnergy"/>
        <result column="battery_daily_discharge_energy" property="batteryDailyDischargeEnergy"/>
        <result column="battery_accumulated_charge_energy" property="batteryAccumulatedChargeEnergy"/>
        <result column="battery_accumulated_discharge_energy" property="batteryAccumulatedDischargeEnergy"/>
        <result column="battery_status" property="batteryStatus"/>
        <result column="battery_maximum_cell_voltage" property="batteryMaximumCellVoltage"/>
        <result column="battery_minimum_cell_voltage" property="batteryMinimumCellVoltage"/>
        <result column="battery_maximum_cell_temperature" property="batteryMaximumCellTemperature"/>
        <result column="battery_minimum_cell_temperature" property="batteryMinimumCellTemperature"/>
        <result column="today_energy" property="todayEnergy"/>
        <result column="total_energy" property="totalEnergy"/>
        <result column="l1_phase_voltage_of_ac_couple" property="l1PhaseVoltageOfAcCouple"/>
        <result column="l1_phase_current_of_ac_couple" property="l1PhaseCurrentOfAcCouple"/>
        <result column="l1_phase_power_of_ac_couple" property="l1PhasePowerOfAcCouple"/>
        <result column="l2_phase_voltage_of_ac_couple" property="l2PhaseVoltageOfAcCouple"/>
        <result column="l2_phase_current_of_ac_couple" property="l2PhaseCurrentOfAcCouple"/>
        <result column="l2_phase_power_of_ac_couple" property="l2PhasePowerOfAcCouple"/>
        <result column="l3_phase_voltage_of_ac_couple" property="l3PhaseVoltageOfAcCouple"/>
        <result column="l3_phase_current_of_ac_couple" property="l3PhaseCurrentOfAcCouple"/>
        <result column="l3_phase_power_of_ac_couple" property="l3PhasePowerOfAcCouple"/>
        <result column="frequency_of_ac_couple" property="frequencyOfAcCouple"/>
        <result column="energy_today_of_ac_couple_kwh" property="energyTodayOfAcCoupleKwh"/>
        <result column="energy_total_of_ac_couple" property="energyTotalOfAcCouple"/>
        <result column="energy_today_of_ac_couple_wh" property="energyTodayOfAcCoupleWh"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="soh" property="soh"/>
        <result column="battery_maximum_cycles_times" property="batteryMaximumCyclesTimes"/>
        <result column="pvl_daily_generating_energy_sum" property="pvlDailyGeneratingEnergySum"/>
        <result column="battery_daily_charge_energy_parallel" property="batteryDailyChargeEnergyParallel"/>
        <result column="today_export_energy" property="todayExportEnergy"/>
        <result column="today_import_energy" property="todayImportEnergy"/>
        <result column="battery_accumulated_discharge_energy_parallel" property="batteryAccumulatedDischargeEnergyParallel"/>
        <result column="pvl_accumulated_energy_sum" property="pvlAccumulatedEnergySum"/>
        <result column="daily_energy_of_load_sum" property="dailyEnergyOfLoadSum"/>
        <result column="totally_input_dc_watt_sum" property="totallyInputDcWattSum"/>
        <result column="phase_l1_watt_of_load_sum" property="phaseL1WattOfLoadSum"/>
        <result column="phase_l2_watt_of_load_sum" property="phaseL2WattOfLoadSum"/>
        <result column="phase_l3_watt_of_load_sum" property="phaseL3WattOfLoadSum"/>
        <result column="phase_l1_watt_sum_of_backup" property="phaseL1WattSumOfBackup"/>
        <result column="phase_l2_watt_sum_of_backup" property="phaseL2WattSumOfBackup"/>
        <result column="phase_l3_watt_sum_of_backup" property="phaseL3WattSumOfBackup"/>
        <result column="daily_support_energy_sum_to_backup" property="dailySupportEnergySumToBackup"/>
        <result column="phase_l1_watt_of_grid_sum" property="phaseL1WattOfGridSum"/>
        <result column="phase_l2_watt_of_grid_sum" property="phaseL2WattOfGridSum"/>
        <result column="phase_l3_watt_of_grid_sum" property="phaseL3WattOfGridSum"/>
        <result column="battery_power_sum" property="batteryPowerSum"/>
        <result column="battery_accumulated_charge_energy_parallel" property="batteryAccumulatedChargeEnergyParallel"/>
        <result column="accumulated_energy_of_positive" property="accumulatedEnergyOfPositive"/>
        <result column="accumulated_energy_of_load_sum" property="accumulatedEnergyOfLoadSum"/>
        <result column="battery_daily_discharge_energy_parallel" property="batteryDailyDischargeEnergyParallel"/>
    </resultMap>

    <select id="view" resultMap="batteryCurrentStatusResultMap">
        select *
        from battery_current_status
        where plant_id = #{queryCondition.plantId}
        and device_serial_number = #{queryCondition.deviceSerialNumber}
        and is_deleted = 0
    </select>

    <select id="viewIsDelete" resultMap="batteryCurrentStatusResultMap">
        select *
        from battery_current_status
        where plant_id = #{queryCondition.plantId}
        and device_serial_number = #{queryCondition.deviceSerialNumber}
        and is_deleted = 1
    </select>

    <select id="queryAppBatteryInfo" resultType="org.skyworth.ess.app.vo.AppBatteryCurrentStatusInfo">
        select d.plant_id ,d.device_serial_number ,c.battery_status as batteryStatus,c.battery_voltage as
        totalVoltage,c.battery_current as batteryCurrent,
        c.battery_maximum_cell_voltage as maxSingleBatteryVoltage,
        c.battery_minimum_cell_voltage as minSingleBatteryVoltage,c.battery_maximum_cell_temperature as
        maxSingleCellBatteryTemperature ,c.battery_minimum_cell_temperature as
        minSingleCellBatteryTemperature,f.rated_battery_capacity as ratedVoltage
        from battery_map_device d left join battery_exit_factory_info f on d.battery_serial_number
        =f.battery_serial_number and f.is_deleted =0
        left join battery_current_status c on d.device_serial_number =c.device_serial_number and c.is_deleted =0 and
        c.plant_id = d.plant_id
        where d.is_deleted =0
        and d.plant_id = #{plantId} and d.device_serial_number=#{deviceSerialNumber}
    </select>

    <select id="queryAppBatteryInfoV2" resultType="org.skyworth.ess.app.vo.AppBatteryCurrentStatusInfo">
        select
        bc.number_of_battery as numberOfBattery,
        round(bc.battery_soc,1) as batterySoc,
        round(bc.battery_voltage,1) as batteryVoltage,
        round(bc.battery_current,1) as batteryCurrent,
        round(bc.battery_power,1) as batteryPower,
        round(bc.battery_maximum_cell_temperature,1) as batteryMaximumCellTemperature,
        round(bc.battery_minimum_cell_temperature,1) as batteryMinimumCellTemperature,
        bc.battery_status as batteryStatus
        from
        battery_current_status bc
        where bc.is_deleted =0
        and bc.plant_id = #{plantId} and bc.device_serial_number=#{deviceSerialNumber}
    </select>

    <select id="batchQueryAppBatteryCurrentStatus" resultMap="batteryCurrentStatusResultMap">
        SELECT plant_id, device_serial_number, device_date_time, battery_soc, battery_voltage,
        battery_current, battery_power, battery_temperature, battery_daily_charge_energy,
        battery_daily_discharge_energy,
        battery_accumulated_charge_energy, battery_accumulated_discharge_energy, battery_status,
        battery_maximum_cell_voltage,
        battery_minimum_cell_voltage, battery_maximum_cell_temperature, battery_minimum_cell_temperature,
        today_energy, total_energy
        FROM battery_current_status c
        where c.is_deleted =0
        and c.plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.plantId}
        </foreach>
        order by device_date_time desc
    </select>
    <select id="queryAppBatteryInfoByDay" resultMap="batteryCurrentStatusResultMap">
        select c.today_energy ,c.total_energy ,c.battery_daily_charge_energy ,
        c.battery_accumulated_charge_energy
        from battery_current_status c where c.is_deleted =0
        and c.plant_id = #{plantId} and c.device_serial_number=#{deviceSerialNumber}
        <if test="queryCondition.deviceDateTime != null">
            <![CDATA[ and device_date_time >= #{queryCondition.deviceDateTime} ]]>
        </if>
    </select>

    <update id="batchDeleteLogicByPlantId">
        update battery_current_status set is_deleted=1,update_user_account=#{updateUserAccount},update_time=now() where
        plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="batchDeleteLogicByPlantIdAndSn">
        update battery_current_status set is_deleted=1,update_user_account=#{updateUserAccount},update_time=now() where
        plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber}
    </delete>
</mapper>
