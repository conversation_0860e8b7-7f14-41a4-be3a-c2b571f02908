<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.plant.mapper.WifiStickPlantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="WifiStickPlantResultMap" type="org.skyworth.ess.plant.entity.WifiStickPlantEntity">
        <result column="id" property="id"/>
        <result column="wifi_stick_serial_number" property="wifiStickSerialNumber"/>
        <result column="wifi_stick_version" property="wifiStickVersion"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="wifi_stick_status" property="wifiStickStatus"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="heart_beat_time" property="heartBeatTime"/>
        <result column="startup_by_backstage" property="startupByBackstage"/>
        <result column="inverter_configure_network" property="inverterConfigureNetwork"/>
        <result column="parallel_mode_function" property="parallelModeFunction"/>
        <result column="onLineStatus" property="onLineStatus"/>
    </resultMap>


    <select id="selectWifiStickPlantPage" resultMap="WifiStickPlantResultMap">
        select * from wifi_stick_plant where is_deleted = 0
    </select>


    <select id="exportWifiStickPlant" resultType="org.skyworth.ess.plant.excel.WifiStickPlantExcel">
        SELECT * FROM wifi_stick_plant ${ew.customSqlSegment}
    </select>

    <select id="queryDeviceCompany" resultType="org.skyworth.ess.plant.vo.WifiStickPlantVO">
        select f.company  from wifi_stick_plant s join device_exit_factory_info f on
        s.device_serial_number = f.device_serial_number and f.is_deleted =0
        where s.is_deleted =0 and s.plant_id = #{params.plantId}
    </select>

    <select id="queryDeviceSerialNumberList" resultMap="WifiStickPlantResultMap">
        select s.plant_id,s.device_serial_number,s.create_time, s.startup_by_backstage, s.inverter_configure_network,d.parallel_mode_function ,d21.device_status as onLineStatus
        from wifi_stick_plant s
        left join device_23 d on s.plant_id =d.plant_id and s.device_serial_number = d.device_serial_number and d.is_deleted = 0
        left join device_21 d21 on d21.plant_id =s.plant_id and d21.device_serial_number =s.device_serial_number and d21.is_deleted =0
        where s.is_deleted = 0 and s.plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <update id="batchDeleteLogicByPlantId">
        update wifi_stick_plant set is_deleted=1,update_time=now(), update_user_account=#{updateUserAccount} where plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="queryByDeviceSerialNumberList" resultMap="WifiStickPlantResultMap">
        select s.plant_id,s.device_serial_number  from wifi_stick_plant s where s.is_deleted = 0 and s.device_serial_number in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryOwnerData" resultMap="WifiStickPlantResultMap">
        select s.plant_id,s.device_serial_number  from wifi_stick_plant s where s.is_deleted = 0 and s.create_user = #{createUser}
    </select>
    <update id="updateDataByCondition">
        update wifi_stick_plant set
        <if test="params.isDeleted!=null ">
            is_deleted = #{params.isDeleted},
        </if>
        <if test="params.updateUser!=null ">
            update_user = #{params.updateUser},
        </if>
        <if test="params.updateUserAccount!=null ">
            update_user_account = #{params.updateUserAccount},
        </if>
        update_time=now()
        where is_deleted = 0
        <if test="params.createUser!=null ">
            and create_user = #{params.createUser}
        </if>
    </update>

    <select id="getWifiStickInfo" resultMap="WifiStickPlantResultMap">
        select id, plant_id, device_serial_number, startup_by_backstage, inverter_configure_network
        from wifi_stick_plant where is_deleted=0
        and plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.plantId}
        </foreach>
    </select>

    <select id="getWifiStickInfoAndBatteryInfo" resultType="java.lang.Long">
        select plant_id from wifi_stick_plant wsp where wsp.is_deleted=0 and wsp.plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and wsp.device_serial_number LIKE CONCAT(#{deviceSerialNumber}, '%')
        union
        select plant_id from battery_map_device bmp where bmp.is_deleted=0 and bmp.plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and bmp.battery_serial_number LIKE CONCAT(#{deviceSerialNumber}, '%')
    </select>

    <update id="updateStartupByBackstage">
        update wifi_stick_plant set startup_by_backstage = #{params.startupByBackstage}
        where plant_id = #{params.plantId} and device_serial_number = #{params.deviceSerialNumber}
    </update>

    <delete id="batchDeleteLogicByPlantIdAndSn" >
        update wifi_stick_plant set is_deleted=1,update_time=now(), update_user_account=#{updateUserAccount}
        where plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber}
    </delete>
</mapper>
