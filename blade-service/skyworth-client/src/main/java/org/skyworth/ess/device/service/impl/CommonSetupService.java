package org.skyworth.ess.device.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.device.client.ICommonSetupServiceClient;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@Slf4j
public class CommonSetupService implements ICommonSetupServiceClient {

	@Autowired
	private IAppSetupService appSetupService;
	@Autowired
	private IWifiStickPlantService wifiStickPlantService;
	@Autowired
	private TimeZoneDeviceService timeZoneDeviceService;
	@Autowired
	private BladeRedis bladeRedis;

	@Override
	public R invokeIssueInterface(JSONObject jsonObject) {
		String deviceSn = jsonObject.getString("deviceSn");

		WifiStickPlantEntity stickPlantEntity = wifiStickPlantService.getOne(Wrappers.<WifiStickPlantEntity>query().lambda().eq(WifiStickPlantEntity::getDeviceSerialNumber, deviceSn));
		if (ObjectUtils.isEmpty(stickPlantEntity)) {
			log.warn("The sn of the inverter is not added to the system：{}", deviceSn);
			return R.fail("The sn of the inverter is not added to the system");
		} else {
			Long plantId = stickPlantEntity.getPlantId();

			String dateTime = org.springblade.common.utils.DateUtil.parseLocalDateTimeToStringDate(LocalDateTime.now(), org.springblade.common.utils.DateUtil.PATTERN_DATETIME);
			Map<String, String> timeZoneDeviceMap = timeZoneDeviceService.getMapFromCacheByPlantIdList(List.of(plantId));
			String timeZone = timeZoneDeviceMap.getOrDefault(plantId+"", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
			/* app先使用蓝牙将wifi配网账号密码发给设备，然后设备推送配网成功消息（ server_distribution_network ） 成功后，同时推送type1,3消息，
			 设备还同时会推送拉取时间的消息 （ inverter_get_server_time ）即会进入此方法。
			（此时app还没有配置时区），后端接收配网成功消息后，解析完成type1,3数据后 后端推送消息给app （ success_distribution_net ），
			 app接收此消息后，进入时区选择界面，并调接口保存时区，timeZoneDeviceService 这个表中才有数据，因此上面查询必然为空，下面的方法就是为了等待app 保存时区数据
			*/
			if(StringUtils.isEmpty(timeZone)) {
				checkAppSetTimeZoneInterfaceStatus(plantId);
			}
			JSONObject jsonObjectPush = new JSONObject();
			jsonObjectPush.put("deviceSerialNumber", deviceSn);
			jsonObjectPush.put("plantId", plantId);
			jsonObjectPush.put("issueSetupType", "advancedSetup");
			jsonObjectPush.put("timeZone", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
			jsonObjectPush.put("issueSource", CommonConstant.AUTO_ADJUST);
			JSONArray jsonArray = new JSONArray();
			JSONObject jsonItemContent = new JSONObject();
			jsonItemContent.put("definition", "dateTime");
			jsonItemContent.put("data", dateTime);
			jsonItemContent.put("definitionDesc", "Date and Time(From Hardware)");
			jsonItemContent.put("dataDesc", dateTime);
			jsonArray.add(jsonItemContent);
			jsonObjectPush.put("setupItems", jsonArray);
			AppAdvancedSetup deviceAdvancedSetup = JsonUtil.parse(jsonObjectPush.toJSONString(), AppAdvancedSetup.class);
			return appSetupService.issueSetupByAutoAdjust(deviceAdvancedSetup);
		}
	}

	/**
	 * 校验app设置时区接口是否执行成功
	 * @param plantId 工厂ID
	 */
	private void checkAppSetTimeZoneInterfaceStatus(Long plantId) {
		// 参数化配置：轮询次数和等待时间
		int maxAttempts = 100; // 最大轮询次数
		long sleepTimeMs = 500; // 每次轮询间隔时间（毫秒）

		for (int i = 1; i <= maxAttempts; i++) {
			Boolean exists = bladeRedis.exists(CacheNames.SET_TIMEZONE_SUCCESS_FLAG_KEY + plantId);

			if (Boolean.FALSE.equals(exists)) {
				if (i == maxAttempts) {
					// 超过最大轮询次数，记录错误日志或抛出异常
					log.error("检查时区设置状态失败，ID: {}, 达到最大轮询次数: {}", plantId, maxAttempts);
				}

				try {
					log.info("第 {} 次检查时区设置状态，ID: {}，未找到对应标志，等待 {} 毫秒后重试", i, plantId, sleepTimeMs);
					Thread.sleep(sleepTimeMs);
				} catch (InterruptedException e) {
					// 恢复线程的中断状态
					Thread.currentThread().interrupt();
					log.warn("线程被中断，工厂ID: {}", plantId, e);
				}
			} else {
				log.info("工厂ID: {} 的时区设置状态检查成功", plantId);
				break;
			}
		}
	}


	private String getDeviceTimeAndTimeZoneByPlantId(Long plantId) {
		LocalDateTime issueDeviceTime = LocalDateTime.now();
		// 从设备时区表查询当前设备的设置时区，然后转换成对应时区再下发到设备中
		Map<String, String> timeZoneDeviceMap = timeZoneDeviceService.getMapFromCacheByPlantIdList(List.of(plantId));
		if (ValidationUtil.isNotEmpty(timeZoneDeviceMap)) {
			String timeZone = timeZoneDeviceMap.getOrDefault(plantId+"", CommonConstant.COMMON_DEFAULT_TIME_ZONE);

			// 获取当前的 LocalDateTime
			LocalDateTime localDateTime = LocalDateTime.now();

			// 将 LocalDateTime 转换为当前时区的 ZonedDateTime
			ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());

			// 将 ZonedDateTime 转换为目标时区的 ZonedDateTime
			ZonedDateTime targetZonedDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of(timeZone));

			// 获取目标时区的 LocalDateTime
			issueDeviceTime = targetZonedDateTime.toLocalDateTime();
		}
		// 格式化 LocalDateTime 为标准的时间格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME);
		return issueDeviceTime.format(formatter);
	}
}
