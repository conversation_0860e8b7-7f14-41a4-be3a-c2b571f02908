<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceExitFactoryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="DeviceExitFactoryInfoResultMap" type="org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity">
        <result column="id" property="id"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_type" property="deviceType"/>
        <result column="company" property="company"/>
        <result column="quality_guarantee_year" property="qualityGuaranteeYear"/>
        <result column="exit_factory_date" property="exitFactoryDate"/>
        <result column="new_quality_guarantee_year" property="newQualityGuaranteeYear"/>
        <result column="single_third_phase" property="singleThirdPhase"/>
        <result column="net_type" property="netType"/>
        <result column="power" property="power"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="activation_date" property="activationDate"/>
        <result column="warranty_start_date" property="warrantyStartDate"/>
        <result column="warranty_deadline" property="warrantyDeadline"/>
        <result column="modbus_protocol_version" property="modbusProtocolVersion"/>
        <result column="firmware_batch" property="firmwareBatch"/>
    </resultMap>


    <select id="selectDeviceExitFactoryInfoPage" resultMap="DeviceExitFactoryInfoResultMap">
        select * from device_exit_factory_info where is_deleted = 0 order by create_time desc,id desc
    </select>


    <select id="exportDeviceExitFactoryInfo" resultType="org.skyworth.ess.device.excel.DeviceExitFactoryInfoExcel">
        SELECT * FROM device_exit_factory_info ${ew.customSqlSegment}
    </select>

    <select id="queryDeviceExitFactoryByPlant" resultType="org.skyworth.ess.device.vo.DeviceExitFactoryInfoVO">
        select power from device_exit_factory_info f inner join device_21 d on d.device_serial_number=
        f.device_serial_number
        and d.is_deleted =0 where f.is_deleted = 0 and d.plant_id=#{plantId}
    </select>
    <update id="deleteLogicDeviceExitFactory">
        update device_exit_factory_info set is_deleted = 1, update_user = #{updateUser},
        update_user_account = #{updateUserAccount},update_time=now() where id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>

    <select id="queryByDeviceSerialNumbers" resultMap="DeviceExitFactoryInfoResultMap">
        SELECT id, device_serial_number, device_type, company, quality_guarantee_year, exit_factory_date,
        new_quality_guarantee_year, single_third_phase, net_type, power
        FROM device_exit_factory_info where is_deleted = 0
        and device_serial_number in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateBatchBySn" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update device_exit_factory_info set

            <if test="item.deviceType!=null and item.deviceType!=''">
                device_type=#{item.deviceType},
            </if>
            <if test="item.company!=null and item.company!=''">
                company=#{item.company},
            </if>
            <if test="item.qualityGuaranteeYear!=null and item.qualityGuaranteeYear!=''">
                quality_guarantee_year=#{item.qualityGuaranteeYear},
            </if>
            <if test="item.newQualityGuaranteeYear!=null and item.newQualityGuaranteeYear =='deletePlant'">
                new_quality_guarantee_year=null,
            </if>
            <if test="item.exitFactoryDate!=null ">
                exit_factory_date=#{item.exitFactoryDate},
            </if>
            <if test="item.singleThirdPhase!=null and item.singleThirdPhase!=''">
                single_third_phase=#{item.singleThirdPhase},
            </if>
            <if test="item.netType!=null and item.netType!=''">
                net_type=#{item.netType},
            </if>
            <if test="item.power!=null and item.power!=''">
                power=#{item.power},
            </if>
            <if test="item.updateUserAccount!=null and item.updateUserAccount!=''">
                update_user_account=#{item.updateUserAccount},
            </if>
            <if test="item.updateUser!=null and item.updateUser!=''">
                update_user=#{item.updateUser},
            </if>
            <if test="item.status!=null ">
                status=#{item.status},
            </if>
            <if test="item.activationDate!=null">
                activation_date =#{item.activationDate},
            </if>
            <if test="item.warrantyStartDate!=null">
                warranty_start_date =#{item.warrantyStartDate},
            </if>
            <if test="item.warrantyDeadline!=null">
                warranty_deadline =#{item.warrantyDeadline},
            </if>
            <if test="item.firmwareBatch!=null and item.firmwareBatch!=''">
                firmware_batch =#{item.firmwareBatch},
            </if>
            update_time=now()
            where device_serial_number = #{item.deviceSerialNumber}
        </foreach>
    </update>
    <select id="queryByIds" resultMap="DeviceExitFactoryInfoResultMap">
        SELECT * FROM device_exit_factory_info where is_deleted = 0
        and id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryRatedTotalPowerOfInverter" resultType="java.math.BigDecimal">
        select sum(ifnull(defi.power, 0))
        from device_exit_factory_info defi
        where defi.status = 1
        and defi.is_deleted = 0
    </select>

    <update id="updateNewQualityQuaranteeYear">
        update device_exit_factory_info set new_quality_guarantee_year = null,
        <if test="params.updateUserAccount!=null and params.updateUserAccount!=''">
            update_user_account=#{params.updateUserAccount},
        </if>
        <if test="params.updateUser!=null and params.updateUser!=''">
            update_user=#{params.updateUser},
        </if>
        update_time=now() where is_deleted = 0 and device_serial_number = #{params.deviceSerialNumber}
    </update>

    <select id="getListByDeviceSerialNumberCollect" resultMap="DeviceExitFactoryInfoResultMap">
        SELECT id, device_serial_number, device_type
        FROM device_exit_factory_info
        WHERE is_deleted = 0
        AND device_serial_number IN
        <if test="list != null and list.size() > 0">
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size() == 0">
            ('1')
        </if>
    </select>
</mapper>
