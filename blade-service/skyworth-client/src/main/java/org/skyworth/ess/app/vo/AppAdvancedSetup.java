package org.skyworth.ess.app.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/11/6 11:10:39
 */
@Data
@ApiModel(value = "app高级设置", description = "app高级设置")
public class AppAdvancedSetup implements Serializable {
	private static final long serialVersionUID = 1L;



	@ApiModelProperty(value = "设备sn")
	@NotNull(message = "can not be empty!")
	private String deviceSerialNumber;

	@ApiModelProperty(value = "站点id")
	@NotNull(message = "can not be empty!")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;

	@ApiModelProperty(value = "下发设置类型")
	@NotNull(message = "can not be empty!")
	private String issueSetupType;

	@ApiModelProperty(value = "app当前时区")
	private String timeZone;

	@ApiModelProperty(value = "设置项参数")
	private List<SetupItem> setupItems;

	@ApiModelProperty(value = "设置项下发的来源")
	private String issueSource;

	/**
	 * 后台代码调用该实体类时，请勿直接使用该变量，此变量是作为兼容旧app所用
	 * since 2025-3-11
	 */
	@ApiModelProperty(value = "app当前时区(旧版app)")
	@Deprecated
	private String timeZoneName;

	public void setTimeZoneName(String timeZoneName) {
		this.timeZoneName = timeZoneName;
		if (this.timeZone == null) {
			this.timeZone = timeZoneName;
		}
	}

	@Getter
	public static class SetupItem{
		@ApiModelProperty(value = "设置项")
		private String definition;

		@ApiModelProperty(value = "设置项地址码")
		private Integer address;

		@ApiModelProperty(value = "设置项数据")
		private Object data;

		@ApiModelProperty(value = "设置项寄存器个数")
		private Integer len;

		@ApiModelProperty(value = "设置项描述")
		private String definitionDesc;

		@ApiModelProperty(value = "设置项数据描述")
		private String dataDesc;


		public void setDefinition(String definition) {
			this.definition = definition;
		}

		public void setAddress(Integer address) {
			this.address = address;
		}

		public void setData(Object data) {
			this.data = data;
		}

		public void setLen(Integer len) {
			this.len = len;
		}

		public void setDefinitionDesc(String definitionDesc) {
			this.definitionDesc = definitionDesc;
		}

		public void setDataDesc(String dataDesc) {
			this.dataDesc = dataDesc;
		}
	}
}
