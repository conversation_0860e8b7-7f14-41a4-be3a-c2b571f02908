package org.springblade.common.constant;

import org.springblade.core.tool.api.IResultCode;

import java.util.ArrayList;

/**
 * 国际化
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public enum I18nMsgCode implements IResultCode {
	SKYWORTH_COMMON_ERROR_900000(900000, "System Error", "系统错误","Systemfehler"),
	SKYWORTH_CLIENT_PLANT_100001(100001, "The Plant Name : [{}] is occupied.", "该电站名称[{}]已被占用。","Der Pflanzenname: [{}] ist belegt."),
	SKYWORTH_CLIENT_PLANT_100002(100002, "The Inverter Serial Number : [{}] is occupied.", "该逆变器序列号[{}]已被占用。","Die Seriennummer des Wechselrichters: [{}] ist belegt."),
	SKYWORTH_CLIENT_PLANT_100003(100003, "The Inverter Serial Number : [{}] doesn’t exist.", "该逆变器序列号[{}]不存在。","Die Seriennummer des Wechselrichters: [{}] existiert nicht."),
	SKYWORTH_SYSTEM_USER_100004(100004, "The Password can not be empty.", "密码不能为空。","Das Passwort darf nicht leer sein."),
	SKYWORTH_SYSTEM_USER_100005(100005, "Wrong Email Format.", "邮箱格式错误。","Falsches E-Mail-Format."),
	SKYWORTH_SYSTEM_USER_100006(100006, "Wrong Verification Code", "验证码错误。","Falscher Bestätigungscode"),
	SKYWORTH_SYSTEM_USER_100007(100007, "The User Name : [{}] is occupied.", "用户名[{}]已被占用。","Der Benutzername: [{}] ist belegt."),
	SKYWORTH_CLIENT_PLANT_100008(100008, "Energy Station Name, Country and Detailed Address can not be empty.", "电站名称、国家、详细地址不能为空。","Name, Land und detaillierte Adresse der Energiestation dürfen nicht leer sein."),
	SKYWORTH_SYSTEM_USER_100009(100009, "The New Password and Confirm Password are not the same.", "新密码和确认密码不一致。","Das neue Passwort und das Bestätigungspasswort sind nicht identisch."),
	SKYWORTH_SYSTEM_USER_100010(100010, "The Previous Password is wrong.", "原密码错误。","Das frühere Passwort ist falsch."),
	SKYWORTH_SYSTEM_INVERTER_100011(100011, "Energy Mode setting failed.", "设置能量模式失败。","Die Einstellung des Energiesparmodus ist fehlgeschlagen."),
	SKYWORTH_CLIENT_INVERTER_100012(100012, "Energy Mode setting timed out.", "设置能量模式超时。","Zeitüberschreitung bei der Energiemodus-Einstellung."),
	SKYWORTH_CLIENT_USER_100013(100013, "The Username or Password is wrong.", "用户名或密码错误。","Der Benutzername oder das Passwort ist falsch."),
	SKYWORTH_CLIENT_USER_100014(100014, "Login time expired, please login again.", "登录超时，请重新登录。","Anmeldezeit abgelaufen, bitte melden Sie sich erneut an."),
	SKYWORTH_CLIENT_USER_100015(100015, "The Tenant id is null, please check.", "租户ID为空，请检查。","Die Mandanten-ID ist Null, bitte überprüfen."),
	SKYWORTH_CLIENT_USER_100016(100016, "Failed to login for too many times, please wait", "登录超时，请稍后重试。","Die Anmeldung ist zu oft fehlgeschlagen. Bitte warten Sie."),
	SKYWORTH_CLIENT_INVERTER_100017(100017, "The inverter is not connected to WiFi yet.", "逆变器未连接WiFi，请稍后重试。","Der Wechselrichter ist noch nicht mit WLAN verbunden."),
	SKYWORTH_CLIENT_INVERTER_100018(100018, "The inverter is offline.", "逆变器离线，请检查网络连接。","Der Wechselrichter ist offline."),
	SKYWORTH_CLIENT_INVERTER_100019(100019, "The inverter is alarming.", "逆变器报警，请检查逆变器状态。","Der Wechselrichter sendet einen Alarm."),
	SKYWORTH_SYSTEM_USER_100020(100020, "The login type is wrong.", "登录类型错误。","Der Anmeldetyp ist falsch."),
	SKYWORTH_SYSTEM_USER_100021(100021, "Failed to login in. Please Contact the system admin.", "登录失败，请联系系统管理员。","Die Anmeldung ist fehlgeschlagen. Bitte kontaktieren Sie den Systemadministrator."),
	SKYWORTH_CLIENT_PLANT_100022(100022, "The Energy Station is deleted,please refresh this page.", "电站已删除，请刷新页面。","Die Energiestation wurde gelöscht, bitte aktualisieren Sie diese Seite."),
	SKYWORTH_CLIENT_INVERTER_100023(100023, "The inverter is alarming.", "逆变器报警，请检查逆变器状态。","Der Wechselrichter schlägt Alarm."),
	SKYWORTH_CLIENT_INVERTER_100024(100024, "The inverter setting failed.", "设置逆变器失败。","Die Wechselrichtereinstellung ist fehlgeschlagen."),
	SKYWORTH_CLIENT_INVERTER_100025(100025, "the inverter setting timed out.", "设置逆变器超时。","Die Wechselrichtereinstellung ist abgelaufen."),
	SKYWORTH_CLIENT_PLANT_100026(100026, "The Energy Station id can not be empty.", "电站ID不能为空。","Die Energiestations-ID darf nicht leer sein."),
	SKYWORTH_CLIENT_INVERTER_100027(100027, "Inverter settings failed.", "设置逆变器失败。","Wechselrichtereinstellungen sind fehlgeschlagen."),
	SKYWORTH_CLIENT_PLANT_100028(100028, "The Energy Station has been deleted.","电站已删除，请刷新页面。","Die Energiestation wurde gelöscht."),
	SKYWORTH_SYSTEM_USER_100100(100100, "The phone format is wrong.", "手机格式错误。","Das Telefonformat ist falsch."),
	SKYWORTH_SYSTEM_USER_100101(100101, "The User id can not be empty.", "用户ID不能为空。","Die Benutzer-ID darf nicht leer sein."),
	SKYWORTH_RESOURCE_SMS_100102(100102, "The SMS type can not be empty.", "短信类型不能为空。","Der SMS-Typ darf nicht leer sein."),
	SKYWORTH_RESOURCE_SMS_100103(100103, "The Phone or Dialling Code can not be empty.", "手机或区号不能为空。","Die Telefon- oder Vorwahlnummer darf nicht leer sein."),
	SKYWORTH_SYSTEM_USER_100104(100104, "The User ID can not be empty.", "用户ID不能为空。","Die Benutzer-ID darf nicht leer sein."),
	SKYWORTH_SYSTEM_USER_100105(100105, "The Register Type is wrong.", "注册类型错误。","Der Registertyp ist falsch."),
	SKYWORTH_SYSTEM_USER_100106(100106, "The Password is wrong.", "密码错误。","Das Passwort ist falsch."),
	SKYWORTH_SYSTEM_USER_100107(100107, "The Phone already existed.", "手机已存在。","Das Telefon existierte bereits."),
	SKYWORTH_RESOURCE_SMS_100108(100108, "The Phone is wrong.", "手机号码格式错误。","Das Telefon ist falsch."),
	SKYWORTH_RESOURCE_SMS_100109(100109, "SMS other error.", "短信其他错误。","SMS mit anderen Fehlern gesendet."),
	SKYWORTH_RESOURCE_SMS_100110(100110, "SMS sent. Please wait and check your phone.", "短信发送成功，请等待并检查手机。","SMS wurde gesendet. Bitte warten Sie und überprüfen Sie Ihr Telefon."),
	SKYWORTH_CLIENT_INVERTER_100111(100111, "The battery SN is occupied.", "电池SN已存在。","Die Batterie-Serialnummer ist belegt."),
	SKYWORTH_RESOURCE_SMS_100112(100112, "Phone number format is incorrect, please confirm.", "手机号码格式错误，请确认。","Das Format der Telefonnummer ist falsch. Bitte bestätigen Sie."),
	SKYWORTH_SYSTEM_USER_100113(100113, "The user has not registered, please register before login.", "用户未注册，请先注册。","Der Benutzer ist nicht registriert. Bitte registrieren Sie sich vor dem Login."),
	SKYWORTH_AGENT_SKU_100033(100033, "the sku code exist.", "SKU编码已存在。","Der SKU-Code ist vorhanden."),
	SKYWORTH_AGENT_SKU_100029(100029, "the sku name , quantity can not empty.", "SKU名称、数量不能为空。","Der SKU-Name und die Menge dürfen nicht leer sein."),
	SKYWORTH_AGENT_SKU_100030(100030, "the sku name , device type, company ,standards, unit ,price can not empty.", "SKU名称、设备类型、厂家、规格、单位、单价不能为空。","Der SKU-Name, der Gerätetyp, das Unternehmen, die Standards, die Einheit und der Preis dürfen nicht leer sein."),
	SKYWORTH_SYSTEM_USER_100031(100031, "the user is not exist.", "用户不存在。","Der Benutzer existiert nicht."),
	SKYWORTH_CLIENT_PLANT_100032(100032, "the user had not create plant.", "用户未创建电站。","Der Benutzer hatte keine Anlage angelegt."),
	SKYWORTH_SYSTEM_USER_100133(100133, "the agentNumber can not empty.", "代理商编号不能为空。","Die agentNumber kann nicht leer sein."),
	SKYWORTH_SYSTEM_USER_100134(100134, "The agent code entered is incorrect. Please re-enter it!", "代理商编号输入错误，请重新输入！","Der eingegebene Agentencode ist falsch. Bitte geben Sie ihn erneut ein!"),
	SKYWORTH_SYSTEM_ROLE_100135(100135, "the role can not empty.", "角色不能为空。","Die Rolle darf nicht leer sein."),
	SKYWORTH_SYSTEM_USER_100136(100136, "the agent number is not exits.", "代理商编号不存在。","Die Agentennummer ist nicht vorhanden."),
	SKYWORTH_SYSTEM_USER_100137(100137, "The same phone number exceeds the (3 items/1 HOUR) frequency limit.", "手机号发送验证码次数超过限制，请1小时后再试。","Die gleiche Telefonnummer überschreitet die Häufigkeitsgrenze (3 Elemente/1 STUNDE)."),
	SKYWORTH_SYSTEM_USER_100114(100114, "You have sent SMS verification codes too many times.Please try again 1 hour later.", "您发送短信验证码次数过多，请1小时后再试。","Sie haben zu oft SMS-Bestätigungscodes gesendet. Bitte versuchen Sie es 1 Stunde später erneut."),
	SKYWORTH_CLIENT_DEVICE_100115(100115, "OTA upgrade timed out. Please try it later.", "OTA升级超时，请稍后重试。","Zeitüberschreitung beim OTA-Upgrade. Bitte versuchen Sie es später erneut."),
	SKYWORTH_CLIENT_DEVICE_100116(100116, "The device is offline.", "设备离线，请检查网络连接。","Das Gerät ist offline."),
	SKYWORTH_CLIENT_DEVICE_100117(100117, "The device does not exist.", "设备不存在。","Das Gerät existiert nicht."),
	SKYWORTH_CLIENT_DEVICE_100118(100118, "The device is being upgraded, please try again later.", "设备正在升级，请稍后重试。","Das Gerät wird aktualisiert. Bitte versuchen Sie es später erneut."),
	SKYWORTH_CLIENT_USER_100119(100119, "This account doesn't exist, please register.", "账号不存在，请先注册。","Das Konto existiert nicht. Bitte registrieren Sie sich."),
	SKYWORTH_CLIENT_PLANT_100120(100120, "The plant is not exist", "电站不存在。","Die Anlage existiert nicht."),
	SKYWORTH_CLIENT_PLANT_100121(100121, "Installer Company can not be empty.", "安装单位不能为空。","Die Installateurfirma darf nicht leer sein."),
	SKYWORTH_CLIENT_PLANT_100122(100122, "Installer can not be empty.", "安装人员不能为空。","Das Installationsprogramm darf nicht leer sein."),
	SKYWORTH_CLIENT_DEVICE_100123(100123, "Setup success.", "设置成功。","Erfolgreiche Einrichtung"),
	SKYWORTH_CLIENT_BATTERY_100124(100124, "Adding batteries from other manufacturers will result in a halving of the total warranty. Are you sure you want to add them", "添加电池失败","Das Hinzufügen von Batterien anderer Hersteller führt zu einer Halbierung der Gesamtgarantie. Sind Sie sicher, dass Sie sie hinzufügen möchten"),
	SKYWORTH_CLIENT_BATTERY_100125(100125, "Different types of batteries cannot be mixed", "添加电池失败","Verschiedene Batterietypen können nicht gemischt werden"),
	SKYWORTH_CLIENT_BATTERY_100126(100126, "Please input the correct battery sn", "添加电池失败，请扫描正确的电池SN","Bitte geben Sie die richtige Batterienummer ein"),
	;

	int code;
	String message;

	String messageZh;
	String messageDe;

	I18nMsgCode(int code, String message,String messageZh, String messageDe) {
		this.code = code;
		this.message = message;
		this.messageZh = messageZh;
	    this.messageDe = messageDe;

	}

	@Override
	public String getMessage() {
		return message;
	}

	@Override
	public int getCode() {
		return code;
	}

	public String getMessageZh() {
		return messageZh;
	}

	public String getMessageDe() {
		return messageDe;
	}

	public String autoGetMessage(String language) {
		if (language.equals(CommonConstant.CURRENT_LANGUAGE_ZH)) {
			return messageZh;
		} else if (language.equals(CommonConstant.CURRENT_LANGUAGE_DE)){
			return messageDe;
		}else {
			return message;
		}
	}


	public static void main(String[] args) {
		ArrayList<Object> ens = new ArrayList<>();
		ArrayList<Object> zhs = new ArrayList<>();
		ArrayList<Object> des = new ArrayList<>();
		I18nMsgCode[] values = I18nMsgCode.values();
		for (I18nMsgCode value : values) {
			ens.add(value.getMessage());
			zhs.add(value.getMessageZh());
			des.add(value.getMessageDe());
		}
		System.out.println(ens);
		System.out.println(zhs);
		System.out.println(des);
	}
}
