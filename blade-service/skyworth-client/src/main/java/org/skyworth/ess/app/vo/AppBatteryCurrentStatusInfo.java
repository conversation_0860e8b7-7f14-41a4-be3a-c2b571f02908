package org.skyworth.ess.app.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AppBatteryCurrentStatusInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 额定容量
	 */
	private BigDecimal ratedVoltage;
	/**
	 * 总电压
	 */
	private BigDecimal totalVoltage;
	/**
	 * 电流
	 */
	private BigDecimal batteryCurrent;
	/**
	 * 最高单体电池电压
	 */
	private BigDecimal maxSingleBatteryVoltage;
	/**
	 * 最低单体电池电压
	 */
	private BigDecimal minSingleBatteryVoltage;
	/**
	 * 最高单体电池温度
	 */
	private BigDecimal maxSingleCellBatteryTemperature;
	/**
	 * 最低单体电池温度
	 */
	private BigDecimal minSingleCellBatteryTemperature;
	/**
	 * 状态
	 */
	private String batteryStatus;
	/**
	 * 当日光伏发电量
	 */
	private BigDecimal todayEnergy;
	/**
	 * 光伏总发电量
	 */
	private BigDecimal totalEnergy;
	// 电池包数量 地址码 209A
	private BigDecimal numberOfBattery;
	// 电池实时soc 地址码 2000
	private BigDecimal batterySoc;
	// 总体电压 地址码 2006
	private BigDecimal batteryVoltage;
	// 充放电功率 地址码 2009
	private BigDecimal batteryPower;
	// 最高电芯单体温度 地址码 201C
	private BigDecimal batteryMaximumCellTemperature;
	// 最低电芯单体温度 地址码 201E
	private BigDecimal batteryMinimumCellTemperature;
	// 总容量 计算得到： 电池数量 * 5.12kwh
	private BigDecimal totalRatedCapacity;
	// firmware version
	private String bmsVersion;
}
