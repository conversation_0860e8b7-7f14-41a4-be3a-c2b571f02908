package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.service.IDevice24Service;
import org.skyworth.ess.device.service.IDeviceCurrentStatusService;
import org.skyworth.ess.device.service.IDeviceCustomModeService;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.setItem.entity.SetItemEntity;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 逆变器模式设置子类
 * @create-time 2023/12/1 15:02:19
 */
@Slf4j
public class AppSetupTemplateTimeBaseControl extends AppSetupTemplate {

	private IDevice23Service device23Service = SpringUtil.getBean(IDevice23Service.class);
	private IDevice24Service device24Service = SpringUtil.getBean(IDevice24Service.class);
	private IDeviceCustomModeService deviceCustomModeService = SpringUtil.getBean(IDeviceCustomModeService.class);
	private IWifiStickPlantService wifiStickPlantService = SpringUtil.getBean(IWifiStickPlantService.class);
	private IDeviceCurrentStatusService deviceCurrentStatusService = SpringUtil.getBean(IDeviceCurrentStatusService.class);
	private ISetItemConfigService setItemConfigService = SpringUtil.getBean(ISetItemConfigService.class);


	@Override
	public Map<String, Object> getSetupDataByDb(Long plantId, String deviceSerialNumber) {
		return device24Service.getInverterModeTimeBase(plantId, deviceSerialNumber);
	}

//	@Override
//	public SetItemEntity getSetupConfig(AppSetRequestVO appSetRequestVO) {
//		SetItemEntity setItemEntity = new SetItemEntity();
//		//查询设置项
//		List<SetItemConfigEntity> setItemConfig = setItemConfigService.getSetupConfig(appSetRequestVO);
//		setItemEntity.setSetItemConfigEntityList(setItemConfig);
//		return setItemEntity;
//	}

	@Override
	public void completeConfigItemBySubTemplate(Map<String, Object> setupDataByDb, SetItemEntity setupItemConfig, JSONObject dataObject, AppSetRequestVO appSetRequestVO) {
		if (ObjectUtil.isNotNull(setupDataByDb) && !setupDataByDb.isEmpty()) {
			for (Map.Entry<String, Object> entry : setupDataByDb.entrySet()) {
				super.buildSetupItemInfo(entry, setupItemConfig, dataObject);
			}
		}
	}

	@Override
	public R<String> issueSetupToToolKit(AppAdvancedSetup deviceAdvancedSetup) {
		return null;
	}

	@Override
    protected R<?> getReturnByMqttResult(AppAdvancedSetup deviceAdvancedSetup, R<String> result, Table<String, String, Object> table, List<AppAdvancedSetup.SetupItem> items) {
		return null;
	}

	// 重写
	@Override
	public R issueSetupCommon(AppAdvancedSetup deviceAdvancedSetup) {
		DeviceCustomModeEntity deviceCustomModeEntity;
		List<AppAdvancedSetup.SetupItem> setupItems = deviceAdvancedSetup.getSetupItems();
		HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
		if (ObjectUtil.isNotEmpty(setupItems)) {
			for (AppAdvancedSetup.SetupItem setupItem : setupItems) {
				objectObjectHashMap.put(setupItem.getDefinition(), setupItem.getData());
				//通过Definition获取address
				Integer address = super.getDecimalAddressByDefinition(setupItem.getDefinition());
				setupItem.setAddress(address);
			}
		}
		ObjectMapper objectMapper = new ObjectMapper();
		String jsonString = JSON.toJSONString(objectObjectHashMap);
		try {
			deviceCustomModeEntity = objectMapper.readValue(jsonString, DeviceCustomModeEntity.class);
		} catch (JsonProcessingException e) {
			return R.fail("Save some setupItem that are not in this mode InverterMode!");
		}

		deviceCustomModeEntity.setPlantId(deviceAdvancedSetup.getPlantId());
		deviceCustomModeEntity.setDeviceSerialNumber(deviceAdvancedSetup.getDeviceSerialNumber());

		R<String> r = super.determineDeviceStatus(deviceAdvancedSetup.getPlantId());
		if (r != null) {
			return r;
		}

		// 发送mqtt消息
		R<String> result = device23Service.workModeIssueToDevice(deviceCustomModeEntity);
		log.info("set inverter mode result : {}", result);
		if (result.getCode() == 200) {
			DeviceCustomModeEntity entity = new DeviceCustomModeEntity();
			entity.setPlantId(deviceCustomModeEntity.getPlantId());
			List<DeviceCustomModeEntity> modelist = deviceCustomModeService.list(Condition.getQueryWrapper(entity));
			if (CollectionUtil.isEmpty(modelist)) {
				deviceCustomModeService.save(deviceCustomModeEntity);
			} else {
				DeviceCustomModeEntity dbEntity = modelist.get(0);
				deviceCustomModeEntity.setId(dbEntity.getId());
				deviceCustomModeService.updateCustomMode(deviceCustomModeEntity);
			}
			LambdaQueryWrapper<WifiStickPlantEntity> wifiEq = Wrappers.<WifiStickPlantEntity>query().lambda()
				.eq(WifiStickPlantEntity::getPlantId, deviceCustomModeEntity.getPlantId());
			List<WifiStickPlantEntity> stickPlantEntityList = wifiStickPlantService.list(wifiEq);
			if (!stickPlantEntityList.isEmpty()) {
				LambdaUpdateWrapper<DeviceCurrentStatusEntity> eq = Wrappers.<DeviceCurrentStatusEntity>lambdaUpdate().set(DeviceCurrentStatusEntity::getHybridWorkMode, deviceCustomModeEntity.getHybridWorkMode())
					.eq(DeviceCurrentStatusEntity::getPlantId, deviceCustomModeEntity.getPlantId());
				if (cn.hutool.core.util.ObjectUtil.isNotNull(deviceCustomModeEntity.getHybridWorkMode())) {
					deviceCurrentStatusService.update(eq);
				}

				device23Service.updateByPlantId(deviceCustomModeEntity);

			}
			String currentLanguage = CommonUtil.getCurrentLanguage();
			return R.success(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100123.autoGetMessage(currentLanguage));
		}else if(result.getCode()== I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100012.getCode()){
			String error=result.getMsg();
			List<String> filteredValues = setupItems.parallelStream()
				.filter(item -> BinaryToHexUtils.hexToDecimal(error)==(item.getAddress()))
				.map(AppAdvancedSetup.SetupItem::getDefinition)
				.collect(Collectors.toList());
			return R.fail("The "+filteredValues.get(0)+" setting fail!");
		}
		return result;
	}
}
