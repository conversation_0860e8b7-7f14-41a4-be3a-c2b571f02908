/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.vo.SetItemConfigVO;
import java.util.Objects;

/**
 * APP设置项配置 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public class SetItemConfigWrapper extends BaseEntityWrapper<SetItemConfigEntity, SetItemConfigVO>  {

	public static SetItemConfigWrapper build() {
		return new SetItemConfigWrapper();
 	}

	@Override
	public SetItemConfigVO entityVO(SetItemConfigEntity setItemConfig) {
		SetItemConfigVO setItemConfigVO = Objects.requireNonNull(BeanUtil.copy(setItemConfig, SetItemConfigVO.class));

		//User createUser = UserCache.getUser(setItemConfig.getCreateUser());
		//User updateUser = UserCache.getUser(setItemConfig.getUpdateUser());
		//setItemConfigVO.setCreateUserName(createUser.getName());
		//setItemConfigVO.setUpdateUserName(updateUser.getName());

		return setItemConfigVO;
	}


}
