package org.skyworth.ess.constant;

/**
 * <AUTHOR> - x<PERSON><PERSON>jiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/11/8 13:37:15
 */
public enum SetupItemEnum {
	year("year", "device_24"),
	monthDay("monthDay", "device_24"),
	hourMinute("hourMinute", "device_24"),
	second("second", "device_24"),
	powerDeratingPercentByModbus("powerDeratingPercentByModbus", "device_24"),
	modbusAddress("modbusAddress", "device_24"),
	rS485BaudRate("rS485BaudRate", "device_24"),
	wifiStaSsid("wifiStaSsid", "device_24"),
	wifiStaPassword("wifiStaPassword", "device_24"),
	digitalMeterModbusAddress("digitalMeterModbusAddress", "device_24"),
	digitalMeterType("digitalMeterType", "device_24"),
	powerFlowDirection("powerFlowDirection", "device_24"),
	powerLimitFunction("powerLimitFunction", "device_24"),
	powerLimitCtRatio("powerLimitCtRatio", "device_24"),
	meterLocation("meterLocation", "device_24"),
	maximumFeedInGridPower("maximumFeedInGridPower", "device_24"),
	firstConnectStartTime("firstConnectStartTime", "device_24"),
	reconnectTime("reconnectTime", "device_24"),
	gridFrequencyHighLossLevel1Limit("gridFrequencyHighLossLevel1Limit", "device_24"),
	gridFrequencyLowLossLevel1Limit("gridFrequencyLowLossLevel1Limit", "device_24"),
	gridVoltageHighLossLevel1Limit("gridVoltageHighLossLevel1Limit", "device_24"),
	gridVoltageLowLossLevel1Limit("gridVoltageLowLossLevel1Limit", "device_24"),
	gridFrequencyHighLossLevel1TripTime("gridFrequencyHighLossLevel1TripTime", "device_24"),
	gridFrequencyLowLossLevel1TripTime("gridFrequencyLowLossLevel1TripTime", "device_24"),
	gridVoltageHighLossLevel1TripTime("gridVoltageHighLossLevel1TripTime", "device_24"),
	gridVoltageLowLossLevel1TripTime("gridVoltageLowLossLevel1TripTime", "device_24"),
	gridFrequencyHighLossLevel2Limit("gridFrequencyHighLossLevel2Limit", "device_24"),
	gridFrequencyLowLossLevel2Limit("gridFrequencyLowLossLevel2Limit", "device_24"),
	gridVoltageHighLossLevel2Limit("gridVoltageHighLossLevel2Limit", "device_24"),
	gridVoltageLowLossLevel2Limit("gridVoltageLowLossLevel2Limit", "device_24"),
	gridFrequencyHighLossLevel2TripTime("gridFrequencyHighLossLevel2TripTime", "device_24"),
	gridFrequencyLowLossLevel2TripTime("gridFrequencyLowLossLevel2TripTime", "device_24"),
	gridVoltageHighLossLevel2TripTime("gridVoltageHighLossLevel2TripTime", "device_24"),
	gridVoltageLowLossLevel2TripTime("gridVoltageLowLossLevel2TripTime", "device_24"),
	gridFrequencyHighLevel1Back("gridFrequencyHighLevel1Back", "device_24"),
	gridFrequencyLowLevel1Back("gridFrequencyLowLevel1Back", "device_24"),
	tenMinAverageSustainedVoltage("tenMinAverageSustainedVoltage", "device_24"),
	reconnectSoftOutputPowerPercent("reconnectSoftOutputPowerPercent", "device_24"),
	overFrequencyPowerReductionDroop("overFrequencyPowerReductionDroop", "device_24"),
	insulationResistanceActiveLimit("insulationResistanceActiveLimit", "device_24"),
	gridOverVoltageDeRatingPoint("gridOverVoltageDeRatingPoint", "device_24"),
	gridFrequencyHighLevel1TripTime("gridFrequencyHighLevel1TripTime", "device_24"),
	gridFrequencyLowLevel1TripTime("gridFrequencyLowLevel1TripTime", "device_24"),
	gridOverFrequencyDeRatingStartPoint("gridOverFrequencyDeRatingStartPoint", "device_24"),
	gridOverFrequencyDeRatingEndPoint("gridOverFrequencyDeRatingEndPoint", "device_24"),
	gridVoltageHighLevel1TripTime("gridVoltageHighLevel1TripTime", "device_24"),
	gridVoltageLowLevel1TripTime("gridVoltageLowLevel1TripTime", "device_24"),
	gridVoltageHighLevel1Back("gridVoltageHighLevel1Back", "device_24"),
	gridVoltageLowLevel1Back("gridVoltageLowLevel1Back", "device_24"),
	firstConnectSoftStartOutputPowerPercent("firstConnectSoftStartOutputPowerPercent", "device_24"),
	overVoltageDeratingSettlingTime("overVoltageDeratingSettlingTime", "device_24"),
	connectionAndReconnectionPowerRampRate("connectionAndReconnectionPowerRampRate", "device_24"),
	outputReactivePowerMode("outputReactivePowerMode", "device_24"),
	powerFactorSetting("powerFactorSetting", "device_24"),
	reactiveControlResponseTime("reactiveControlResponseTime", "device_24"),
	curveNode1Percent("curveNode1Percent", "device_24"),
	curveNode2Percent("curveNode2Percent", "device_24"),
	curveNode3Percent("curveNode3Percent", "device_24"),
	curveNode4Percent("curveNode4Percent", "device_24"),
	curveNode1ValueSetting("curveNode1ValueSetting", "device_24"),
	curveNode2ValueSetting("curveNode2ValueSetting", "device_24"),
	curveNode3ValueSetting("curveNode3ValueSetting", "device_24"),
	regulationCode("regulationCode", "device_24"),
	inverterControl("inverterControl", "device_24"),
	factoryReset("factoryReset", "device_24"),
	clearHistoricalInfo("clearHistoricalInfo", "device_24"),
	modbusProtocolVersion("modbusProtocolVersion", "device_24"),
	deviceDateTime("deviceDateTime", "device_24"),


	hybridWorkMode("hybridWorkMode", "device_23"),
	onceEveryday("onceEveryday", "device_23"),
	chargeStartTime1("chargeStartTime1", "device_23"),
	chargeEndTime1("chargeEndTime1", "device_23"),
	dischargeStartTime1("dischargeStartTime1", "device_23"),
	dischargeEndTime1("dischargeEndTime1", "device_23"),
	batteryTypeSelection("batteryTypeSelection", "device_23"),
	commAddress("commAddress", "device_23"),
	batteryAh("batteryAh", "device_23"),
	stopDischargeVoltage("stopDischargeVoltage", "device_23"),
	stopChargeVoltage("stopChargeVoltage", "device_23"),
	gridCharge("gridCharge", "device_23"),
	maximumGridChargerPower("maximumGridChargerPower", "device_23"),
	capacityOfGridChargerEnd("capacityOfGridChargerEnd", "device_23"),
	maximumChargerPower("maximumChargerPower", "device_23"),
	capacityOfChargerEnd("capacityOfChargerEnd", "device_23"),
	maximumDischargerPower("maximumDischargerPower", "device_23"),
	capacityOfDischargerEnd("capacityOfDischargerEnd", "device_23"),
	offGridMode("offGridMode", "device_23"),
	ratedOutputVoltage("ratedOutputVoltage", "device_23"),
	ratedOutputFrequency("ratedOutputFrequency", "device_23"),
	offGridStartUpBatteryCapacity("offGridStartUpBatteryCapacity", "device_23"),
	maximumDischargeCurrent("maximumDischargeCurrent", "device_23"),
	maximumChargerCurrent("maximumChargerCurrent", "device_23"),
	genPort("genPort", "device_23"),
	lithiumBatteryActivationFunction("lithiumBatteryActivationFunction", "device_23"),
	maximumGenChargePower("maximumGenChargePower", "device_23"),
	maximumInputPowerFromGenerator("maximumInputPowerFromGenerator", "device_23"),
	supportNormalLoadFunction("supportNormalLoadFunction", "device_23"),
	parallelModeFunction("parallelModeFunction", "device_23"),
	feedInGridFunction("feedInGridFunction", "device_23"),
	maximumGridForcedChargePower("maximumGridForcedChargePower", "device_23"),
	alwaysOnWithGridFunction("alwaysOnWithGridFunction", "device_23"),
	timeBasedControlEnable("timeBasedControlEnable", "device_23"),
	chargePowerInTime1HighWord("chargePowerInTime1HighWord", "device_23"),
	chargeEndSocInTime1("chargeEndSocInTime1", "device_23"),
	dischargePowerInTime1HighWord("dischargePowerInTime1HighWord", "device_23"),
	dischargeEndSocInTime1("dischargeEndSocInTime1", "device_23"),
	onceEveryday2("onceEveryday2", "device_23"),
	chargeStartTime2("chargeStartTime2", "device_23"),
	chargeEndTime2("chargeEndTime2", "device_23"),
	dischargeStartTime2("dischargeStartTime2", "device_23"),
	dischargeEndTime2("dischargeEndTime2", "device_23"),
	chargePowerInTime2HighWord("chargePowerInTime2HighWord", "device_23"),
	chargeEndSocInTime2("chargeEndSocInTime2", "device_23"),
	dischargePowerInTime2HighWord("dischargePowerInTime2HighWord", "device_23"),
	dischargeEndSocInTime2("dischargeEndSocInTime2", "device_23"),
	onceEveryday3("onceEveryday3", "device_23"),
	chargeStartTime3("chargeStartTime3", "device_23"),
	chargeEndTime3("chargeEndTime3", "device_23"),
	dischargeStartTime3("dischargeStartTime3", "device_23"),
	dischargeEndTime3("dischargeEndTime3", "device_23"),
	chargePowerInTime3HighWord("chargePowerInTime3HighWord", "device_23"),
	chargeEndSocInTime3("chargeEndSocInTime3", "device_23"),
	dischargePowerInTime3HighWord("dischargePowerInTime3HighWord", "device_23"),
	dischargeEndSocInTime3("dischargeEndSocInTime3", "device_23"),


	plantId("plantId", "device_21"),

	deviceSerialNumber("deviceSerialNumber", "device_21"),
	deviceModel("deviceModel", "device_21"),
	masterSoftwareVersion("masterSoftwareVersion", "device_21"),
	masterSoftwareBuildDate("masterSoftwareBuildDate", "device_21"),
	slaveFirmwareVersion("slaveFirmwareVersion", "device_21"),
	slaveFirmwareBuildDate("slaveFirmwareBuildDate", "device_21"),
	mpptNumber("mpptNumber", "device_21"),
	ratedVoltage("ratedVoltage", "device_21"),
	ratedFrequency("ratedFrequency", "device_21"),
	ratedPower("ratedPower", "device_21"),
	gridPhaseNumber("gridPhaseNumber", "device_21"),
	emsFirmwareVersion("emsFirmwareVersion", "device_21"),
	emsFirmwareBuildDate("emsFirmwareBuildDate", "device_21"),
	dcdcFirmwareVersion("dcdcFirmwareVersion", "device_21"),
	dcdcFirmwareBuildDate("dcdcFirmwareBuildDate", "device_21"),
	wifiStickSerialNumber("wifiStickSerialNumber", "device_21"),
	deviceStatus("deviceStatus", "device_21"),
	maximumInputPowerFromGrid("maximumInputPowerFromGrid", "device_23"),
	capacityOfDischargeEndOnGrid("capacityOfDischargeEndOnGrid", "device_23"),
	forceChargeStartSoc("forceChargeStartSoc", "device_23"),
	forceChargeEndSoc("forceChargeEndSoc", "device_23"),
	backupMinimumOutputVoltage("backupMinimumOutputVoltage", "device_23"),
	backupMaximumOutputVoltage("backupMaximumOutputVoltage", "device_23"),
	generatorStartSoc("generatorStartSoc", "device_23"),
	generatorEndSoc("generatorEndSoc", "device_23"),
	generatorDryForceOnOrOff("generatorDryForceOnOrOff", "device_23"),
	arcFaultDetectFunction("arcFaultDetectFunction", "device_24"),
	arcFaultResetStatus("arcFaultResetStatus", "device_24"),
	arcFaultStrengthSettingValue("arcFaultStrengthSettingValue", "device_24"),
	powerDeratingControlMode("powerDeratingControlMode", "device_24"),
	powerSwitching("powerSwitching","device_24"),
	rs485Device("rs485Device","device_23"),
	acCouplingFunction("acCouplingFunction","device_23"),
	// begin 25.3.3
	cospCurveNode1Percent("cospCurveNode1Percent", "device_24"),
	cospCurveNode2Percent("cospCurveNode2Percent", "device_24"),
	cospCurveNode3Percent("cospCurveNode3Percent", "device_24"),
	cospCurveNode4Percent("cospCurveNode4Percent", "device_24"),
	cospCurveNode1Value("cospCurveNode1Value", "device_24"),
	cospCurveNode2Value("cospCurveNode2Value", "device_24"),
	cospCurveNode3Value("cospCurveNode3Value", "device_24"),
	cospCurveNode4Value("cospCurveNode4Value", "device_24"),
	curveNode4ValueSetting("curveNode4ValueSetting", "device_24"),
	curveNode1PercentageSetting("curveNode1PercentageSetting", "device_24"),
	curveNode2PercentageSetting("curveNode2PercentageSetting", "device_24"),
	curveNode3PercentageSetting("curveNode3PercentageSetting", "device_24"),
	curveNode4PercentageSetting("curveNode4PercentageSetting", "device_24"),
	curveNode1ValueSettingPn("curveNode1ValueSettingPn", "device_24"),
	curveNode2ValueSettingPn("curveNode2ValueSettingPn", "device_24"),
	curveNode3ValueSettingPn("curveNode3ValueSettingPn", "device_24"),
	curveNode4ValueSettingPn("curveNode4ValueSettingPn", "device_24"),
	reactivePowerPercent("reactivePowerPercent", "device_24"),
	parallelSystemBatteryConnectType("parallelSystemBatteryConnectType", "device_23")
	// end 25.3.3
	;


	private String definition;


	private String dataTable;

	public String getDefinition() {
		return definition;
	}


	public String getDataTable() {
		return dataTable;
	}

	private SetupItemEnum(String definition, String dataTable) {
		this.definition = definition;
		this.dataTable = dataTable;
	}

	public static SetupItemEnum getInstanceByDefinition(String definition) {

		SetupItemEnum result = null;

		for (SetupItemEnum s : values()) {
			if (s.getDefinition().equals(definition)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
