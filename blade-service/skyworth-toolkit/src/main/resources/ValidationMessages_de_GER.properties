toolkit.azure.blob.file.download.fail=AzureBlobFileService, Datei herunterladen: Ausnahme %s
toolkit.azure.blob.file.record.upload.fail=AzureBlobFileService,upload: Die hochgeladene Aufzeichnung ist fehlgeschlagen
toolkit.azure.paramater.cannot.empty=Die Parameter „container“ und „azureKey“ dürfen nicht leer sein.
toolkit.azure.thumbnail.upload.error=Ungewöhnliches Hochladen der Miniaturansicht, Fehler: %s
toolkit.blob.get.exception=Ausnahme beim Abrufen von BlobContainer, Fehler: %s
toolkit.file.upload.error=Ausnahme beim Hochladen der Datei, Fehler: %s
toolkit.sastoken.get.error=SasToken abrufen, Fehler: %s
