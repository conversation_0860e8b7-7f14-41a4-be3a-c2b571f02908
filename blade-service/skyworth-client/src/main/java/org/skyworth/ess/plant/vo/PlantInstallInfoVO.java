package org.skyworth.ess.plant.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class PlantInstallInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String deviceSerialNumber;
    private List<String> mppt1;
    private List<String> mppt2;
    private List<String> mppt3;
    private List<String> mppt4;
    private List<String> batteryBox;


	@ApiModelProperty(value = "设备/逆变器种类")
	@TableField(exist = false)
	private String inverterKind;

	@TableField(exist = false)
	@ApiModelProperty(value = "电网相数")
	private String gridPhaseNumber;
}
