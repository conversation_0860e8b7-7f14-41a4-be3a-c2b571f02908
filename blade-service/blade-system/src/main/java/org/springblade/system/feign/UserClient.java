/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.vo.UserBatchVO;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserInfo;
import org.springblade.system.entity.UserOauth;
import org.springblade.system.enums.UserEnum;
import org.springblade.system.service.IUserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 用户服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class UserClient implements IUserClient {

	private final IUserService service;

	@Override
	@GetMapping(USER_INFO_BY_ID)
	public R<User> userInfoById(Long userId) {
		return R.data(service.getById(userId));
	}

	@Override
	@GetMapping(USER_INFO_BY_ACCOUNT)
	public R<User> userByAccount(String tenantId, String account) {
		return R.data(service.userByAccount(tenantId, account));
	}

	@Override
	@GetMapping(USER_INFO)
	public R<UserInfo> userInfo(String tenantId, String account) {
		return R.data(service.userInfo(tenantId, account));
	}

	@Override
	@GetMapping(USER_INFO_BY_TYPE)
	public R<UserInfo> userInfo(String tenantId, String account, String userType, String phoneDiallingCode) {
		return R.data(service.userInfo(tenantId, account, UserEnum.of(userType),phoneDiallingCode));
	}

	@Override
	@PostMapping(USER_AUTH_INFO)
	public R<UserInfo> userAuthInfo(@RequestBody UserOauth userOauth) {
		return R.data(service.userInfo(userOauth));
	}

	@Override
	@PostMapping(SAVE_USER)
	public R<Boolean> saveUser(@RequestBody User user) {
		return R.data(service.submit(user));
	}

	@Override
	@PostMapping(REMOVE_USER)
	public R<Boolean> removeUser(String tenantIds) {
		return R.data(service.remove(Wrappers.<User>query().lambda().in(User::getTenantId, Func.toStrList(tenantIds))));
	}
	@Override
	@GetMapping(USER_INFO_BY_LOGIN_NAME)
	public R<UserInfo> userByLoginName(String tenantId, String loginName , String loginType,String phoneDiallingCode) {
		User user = new User();
		R<UserInfo> r = new R();
		if(CommonConstant.LOGIN_TYPE_TELEPHONE.equalsIgnoreCase(loginType)) {
			user.setPhone(loginName);
			user.setPhoneDiallingCode(phoneDiallingCode);
			user.setTenantId(tenantId);
		} else if (CommonConstant.LOGIN_TYPE_EMAIL.equalsIgnoreCase(loginType)) {
			user.setEmail(loginName);
			user.setTenantId(tenantId);
		} else {
			String currentLanguage = CommonUtil.getCurrentLanguage();
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.autoGetMessage(currentLanguage));
			return r;
		}
		return service.userInfoByCondition(user);
	}
	@Override
	@PostMapping(USER_BY_USER_INFO)
	public R<User> userByUserInfo(User user) {
		return service.userByUserInfo(user);
	}

	@Override
	@PostMapping(ALL_USER_INFO)
	public R<List<User>> getAllUsers() {
		return R.data(service.getAllUsers());
	}
	@Override
	@GetMapping(USER_DELETE_BY_USER_ID)
	public R deleteUserById(Long userId) {
		return R.data(service.deleteLogic(Func.toLongList(String.valueOf(userId))));
	}
	@Override
	@PostMapping(USER_LIST)
	public R<List<User>> getUserList(User user) {
		return service.getUserList(user);
	}

	@Override
	@PostMapping(SYNCHRONIZED_USER_LIST)
	@TenantIgnore
	public R synchronousUserList(UserBatchVO<User> userUserBatchVO) {
		return service.synchronousUserList(userUserBatchVO);
	}

	@Override
	@PostMapping(SYNCHRONIZED_USER_LIST_CLIENT)
	@TenantIgnore
	public R synchronousUserListForClient(UserBatchVO<User> userUserBatchVO) {
		return service.synchronousUserListForClient(userUserBatchVO);
	}

	@Override
	@PostMapping(USER_INFO_BY_WEB)
	public R<UserInfo> userInfoByWeb(String tenantId, String account, String userType, String phoneDiallingCode) {
		return R.data(service.userInfo(tenantId, account, UserEnum.of(userType) ,phoneDiallingCode));
	}
}
