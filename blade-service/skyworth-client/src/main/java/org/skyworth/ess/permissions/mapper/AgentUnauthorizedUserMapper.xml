<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.permissions.mapper.AgentUnauthorizedUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentUnauthorizedUserResultMap" type="org.skyworth.ess.permissions.entity.AgentUnauthorizedUserEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="unauthorized_user_id" property="unauthorizedUserId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <update id="removeByPlantId">
        update plant_agent_unauthorized_user set is_deleted = 1
        where plant_id = #{plantId} and is_deleted = 0
    </update>


    <select id="selectAgentUnauthorizedUserPage" resultMap="agentUnauthorizedUserResultMap">
        select * from plant_agent_unauthorized_user where is_deleted = 0
    </select>

</mapper>
