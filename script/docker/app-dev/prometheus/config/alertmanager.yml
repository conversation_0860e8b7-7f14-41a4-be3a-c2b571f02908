global:
  # 在没有报警的情况下声明为已解决的时间
  resolve_timeout: 5m
  # 配置邮件发送信息
  smtp_smarthost: 'smtp.163.com:25'
  # 邮箱地址
  smtp_from: '<EMAIL>'
  # 邮箱地址
  smtp_auth_username: '<EMAIL>'
  # 邮箱授权码，需要自行开启设置，非邮箱密码
  smtp_auth_password: 'xxxxxxxx'
  # 邮箱地址
  smtp_hello: '<EMAIL>'
  smtp_require_tls: false

templates:
  # 告警模板文件
  - "/etc/alertmanager/templates/wechat.tmpl"

route:
  # 接收到告警后到自定义分组
  group_by: ["alertname"]
  # 分组创建后初始化等待时长
  group_wait: 10s
  # 告警信息发送之前的等待时长
  group_interval: 30s
  # 重复报警的间隔时长
  repeat_interval: 5m
  # 默认消息接收
  receiver: "wechat"

receivers:
  # 微信
  - name: "wechat"
    wechat_configs:
      # 是否发送恢复信息
      - send_resolved: true
        # 填写应用 AgentId
        agent_id: "1000002"
        # 填写应用 Secret
        api_secret: "jxxxxxxxxxxxxxxxxxxxc"
        # 填写企业 ID
        corp_id: "wwxxxxxxxxxxx01d"
        # 填写接收消息的群体
        to_user: "@all"
  # 钉钉
  - name: 'dingtalk'
    webhook_configs:
      # prometheus-webhook-dingtalk服务的地址
      - url: http://172.30.0.96:8060/dingtalk/webhook_robot/send
        send_resolved: true
  # 邮件
  - name: 'email'
    email_configs:
      - to: 'your email'
        send_resolved: true
