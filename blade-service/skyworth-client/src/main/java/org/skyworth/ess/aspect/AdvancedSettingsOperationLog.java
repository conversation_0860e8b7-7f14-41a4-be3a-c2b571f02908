/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.aspect;

import org.skyworth.ess.constant.SourceEnum;

import java.lang.annotation.*;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-11-07 17:27
 **/

@Target({ElementType.METHOD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AdvancedSettingsOperationLog {
	 SourceEnum type() default SourceEnum.WEB;
}
