package org.skyworth.ess.app.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class AppReportHeaderV2VO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 当日光伏发电量 1027
	 */
	private String todayEnergy;
	/**
	 * 原始当日光伏发电量，未转换，不带单位  1027
	 */
	private BigDecimal originalTodayEnergy;
	/**
	 * 光伏总发电量 1021
	 */
	private String totalEnergy;
	/**
	 * 电池日放电量 200F
	 */
	private String batteryTodayDischargeEnergy;
	/**
	 * 电池总放电量 2011
	 */
	private String batteryAccumulatedDischargeEnergy;
	/**
	 * 电网今日输入 1332
	 */
	private String todayImportEnergy;
	/**
	 * 电网总输入 1306   accumulated_energy_of_positive
	 */
	private String totalImportEnergy;

	private String hybridWorkMode;
	/**
	 * From PV  Today Energy 0x1027减去Today export Energy 0x1334
	 */
	private String fromPv;
	/**
	 * Today import Energy 0x1332
	 */
	private String fromGrid;
	/**
	 * 原始当日电网输出，未转换，不带单位  1334
	 */
	private BigDecimal originalTodayExportEnergy;
	/**
	 * 当日电网输出，1334
	 */
	private String todayExportEnergy;
}
