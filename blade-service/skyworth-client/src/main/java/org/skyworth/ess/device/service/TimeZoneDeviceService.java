package org.skyworth.ess.device.service;


import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

public interface TimeZoneDeviceService extends BaseService<TimeZoneDevice> {
    List<TimeZoneDevice> getListBySnList(List<String> deviceSnList);

	List<TimeZoneDevice> getListByPlantIdList(List<Long> plantIdList);

	Map<String, String> getMapFromCacheByPlantIdList(List<Long> plantIdList);

    void deleteByPlantId(List<Long> longList);
}
