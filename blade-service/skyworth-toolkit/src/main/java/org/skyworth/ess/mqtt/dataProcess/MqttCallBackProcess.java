package org.skyworth.ess.mqtt.dataProcess;

import java.util.List;

public interface MqttCallBackProcess {

     void publish(String topic, String jsonObject);

     void heartBeatWifi(String result);

     void subscribeMode(String result);

     void subscribeErrorInfo(String result);

	 void obtainUpgradeResults(String result);

	 void obtainUpgradePushResults(List<String> deviceSns);

	 void subscribeDeviceIssueRes(String result);

	 void subscribeDeviceTimeRes(String result);

	void subscribeWifiConnectedRes(String result);

	void distributionNetwork(String topic, String jsonObject);

	void errorInfoToKafka(String topic, String jsonObject);

	void heartBeatWifiToKafka(String topic, String jsonObject);

	void otaOfflineUpgradeException(String result);

	void subscribeInverterGetServerTime(String result);
}
