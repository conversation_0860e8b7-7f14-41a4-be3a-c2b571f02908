package org.skyworth.ess.company.fegin;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.skyworth.ess.company.service.IAgentUserInfoService;
import org.skyworth.ess.company.wrapper.AgentCompanyInfoWrapper;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.skyworth.ess.feign.client.IAgentClientBiz;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@ApiIgnore
@RestController
@AllArgsConstructor
public class AgentClientBiz extends BladeController implements IAgentClientBiz {

	private final IAgentCompanyInfoService agentService;
	private final IAgentUserInfoService agentUserInfoService;
	private final IUserSearchClient userSearchClient;


	@TenantIgnore
	@Override
	@GetMapping(AGENT_COMPANY_INFO)
	public R<List<AgentCompanyVO>> agentCompany(String companyName) {
		// 创建查询条件
		LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.likeRight(StringUtils.isNotBlank(companyName), AgentCompanyInfoEntity::getCompanyName, companyName)
			.eq(AgentCompanyInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		// 执行分页查询
		Page<AgentCompanyInfoEntity> companyInfoPage = agentService.page(new Page<>(1, 100), queryWrapper);
		List<AgentCompanyVO> agentCompanyVOList = AgentCompanyInfoWrapper.build().listVO(companyInfoPage.getRecords());
		return R.data(agentCompanyVOList);
	}

	@Override
	public R<String> agentDeptId(String agentNumber) {
		return null;
	}

	@Override
	public R<AgentCompanyVO> agentNumber(String agentNumber) {
		LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyQueryWrapper = new LambdaQueryWrapper<>();
		agentCompanyQueryWrapper.eq(agentNumber != null, AgentCompanyInfoEntity::getAgentNumber, agentNumber)
			.eq(AgentCompanyInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		AgentCompanyInfoEntity agentCompanyInfoEntity = agentService.getOne(agentCompanyQueryWrapper);
		if (ObjectUtil.isNotNull(agentCompanyInfoEntity)) {
			AgentCompanyVO agentCompanyVO = AgentCompanyInfoWrapper.build().entityVO(agentCompanyInfoEntity);
			return R.data(agentCompanyVO);
		} else {
			return R.fail("The agent code entered is incorrect. Please re-enter it!");
		}
	}

	@Override
	public R<List<JSONObject>> findAgentUserList(String roleCode, Long agentId, String realName) {
		return null;
	}

	@Override
	public R<JSONObject> getQcSubNodeAuditInfo(String taskId) {
		return null;
	}

	@Override
	public R cleanUpAgentUser(List<JSONObject> jsonObjectList) {
		if (CollectionUtils.isNullOrEmpty(jsonObjectList)) {
			return R.fail("The parameter cannot be empty");
		}
		agentUserInfoService.deleteUserByJsonList(jsonObjectList);
		return R.success("success");
	}

	@TenantIgnore
	@Override
	public R<List<AgentCompanyVO>> agentCompanyInfoByIds(List<Long> ids) {
		LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(AgentCompanyInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		if (ValidationUtil.isNotEmpty(ids) && !ids.isEmpty()) {
			queryWrapper.in(AgentCompanyInfoEntity::getDeptId, ids);
		}
		// 执行分页查询
		Page<AgentCompanyInfoEntity> companyInfoPage = agentService.page(new Page<>(1, 100), queryWrapper);
		List<AgentCompanyVO> agentCompanyVOList = AgentCompanyInfoWrapper.build().listVO(companyInfoPage.getRecords());
		return R.data(agentCompanyVOList);
	}

	@Override
	public R<List<AgentCompanyVO>> agentCompanyByDeptId(String deptId, Long userId) {
		return null;
	}

	@TenantIgnore
	@Override
	public R<List<AgentUserVo>> agentUserInfo(Long agentId, Long deptId) {
		// 通过用户类型查询用户id
		List<AgentUserInfoEntity> agentUserInfoEntityList = agentUserInfoService.list(Wrappers.<AgentUserInfoEntity>lambdaQuery().eq(AgentUserInfoEntity::getAgentId, agentId));
		List<Long> userIds = agentUserInfoEntityList.stream().map(AgentUserInfoEntity::getUserId).collect(Collectors.toList());
		List<User> userList = userSearchClient.listByUserIds(userIds).getData();
		List<AgentUserVo> agentUserVos = new ArrayList<>();
		if (CollectionUtils.isNullOrEmpty(userList)) {
			return R.data(agentUserVos);
		}
		userList.forEach(v -> {
			AgentUserVo agentUserVo = new AgentUserVo();
			BeanUtils.copyProperties(v, agentUserVo);
			agentUserVos.add(agentUserVo);
		});
		return R.data(agentUserVos);
	}

	@Override
	public R<Boolean> saveAgentUserInfo(AgentUserInfoEntity agentUserInfoEntity) {
		return R.data(agentUserInfoService.save(agentUserInfoEntity));
	}

	@Override
	public R<Boolean> deleteAgentUserInfo(List<Long> userIdList) {
		return R.data(agentUserInfoService.updateUserInfo4Delete(userIdList));
	}
}
