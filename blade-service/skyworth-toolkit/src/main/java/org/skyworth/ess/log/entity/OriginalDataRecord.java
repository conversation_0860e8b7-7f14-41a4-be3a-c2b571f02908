package org.skyworth.ess.log.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

@Data
@TableName("original_data_record")
@ApiModel(value = "OriginalDataRecord对象", description = "数据异常日志")
@EqualsAndHashCode(callSuper = true)
public class OriginalDataRecord extends TenantEntity{

    @ApiModelProperty(value = "业务类型: 1(业务处理时的异常) 2(原始数据异常)")
    private String type;

    @ApiModelProperty(value = "异常信息")
    private String exceptionInfo;

    @ApiModelProperty(value = "原始数据")
    private String data;


}
