/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.mapper;

import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.vo.Device24VO;
import org.skyworth.ess.device.excel.Device24Excel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Device24 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface Device24Mapper extends BaseMapper<Device24Entity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param Device24
	 * @return
	 */
	List<Device24VO> selectDevice24Page(IPage page, Device24VO Device24);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<Device24Excel> exportDevice24(@Param("ew") Wrapper<Device24Entity> queryWrapper);

    HashMap<String, Object> getAllAdvancedSetup(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber);
    HashMap<String, Object> getAllAdvancedSetupIsDelete(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber);

	int updateSetup(@Param("device24Map") Map<String, Object> device24,@Param("plantId") Long plantId,@Param("deviceSerialNumber") String deviceSerialNumber);

	List<Device24Entity> getDevice24Info(@Param("list")List<Device24Entity> list);

	void updateInverterControl(@Param("params") Device24Entity device24Entity);

	Map<String, Object> getInverterControl(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber);
	Map<String, Object> getInverterControlIsDelete(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber);

	Map<String, Object> getInverterModeTimeBase(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber);
	Map<String, Object> getInverterModeTimeBaseIsDelete(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber);
}
