<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.ota.mapper.DeviceSoftwareVersionInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceSoftwareVersionInfoResultMap"
               type="org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity">
        <result column="id" property="id"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="current_version_number" property="currentVersionNumber"/>
        <result column="big_type" property="bigType"/>
        <result column="small_type" property="smallType"/>
        <result column="upgrade_completion_time" property="upgradeCompletionTime"/>
        <result column="company" property="company"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="latest_released_version" property="latestReleasedVersion"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="retry_count" property="retryCount"/>
    </resultMap>


    <select id="selectDeviceSoftwareVersionInfoPage" resultMap="deviceSoftwareVersionInfoResultMap">
        select *
        from device_software_version_info
        where is_deleted = 0
    </select>


    <select id="exportDeviceSoftwareVersionInfo" resultType="org.skyworth.ess.ota.excel.DeviceSoftwareVersionInfoExcel">
        SELECT *
        FROM device_software_version_info ${ew.customSqlSegment}
    </select>

    <select id="findSerialNumberList" resultType="java.lang.String">
        select *
        from (select dsvi.serial_number
        from device_software_version_info dsvi
        left join device_21 d on
        dsvi.serial_number = d.device_serial_number
        left join plant p on
        d.plant_id = p.id
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        where dsvi.current_version_number != oup.version_number
        and d.is_deleted = 0
        and p.is_deleted = 0
        and d.device_status in(1, 2)
        and dsvi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        and dsvi.status in (2, 3)
        and dsvi.big_type ='inverter'
        union
        select
        dsvi.serial_number
        from
        device_software_version_info dsvi
        left join wifi_stick_plant wsp
        on
        dsvi.serial_number = wsp.device_serial_number
        left join plant p on
        wsp.plant_id = p.id
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        where
        dsvi.current_version_number != oup.version_number
        and wsp.is_deleted = 0
        and p.is_deleted = 0
        and wsp.wifi_stick_status = 1
        and dsvi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        and dsvi.big_type ='wifibom'
        and dsvi.status in (2
        , 3)
        union
        select
        dsvi.serial_number
        from
        device_software_version_info dsvi
        left join battery_current_status wsp
        on
        dsvi.serial_number = wsp.device_serial_number
        left join plant p on
        wsp.plant_id = p.id
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        where
        dsvi.current_version_number != oup.version_number
        and wsp.is_deleted = 0
        and p.is_deleted = 0
        and wsp.battery_status != 5
        and dsvi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        and dsvi.status in (2
        , 3)
        and dsvi.big_type = 'battery'
        ) b
        order by b.serial_number asc
    </select>

    <select id="findDeviceUpgradeListBySn" resultType="org.skyworth.ess.ota.vo.OtaUpdatePackVO">
        select dsvi.id as deviceSoftwareVersionId,
        dsvi.serial_number,
        dsvi.current_version_number,
        oup.big_type,
        oup.small_type,
        oup.version_number,
        oup.company,
        oup.pack_cdn_url,
        oup.whether_to_force_upgrade,
        oup.business_id,
        oup.check_sum
        from device_software_version_info dsvi
        left join device_21 d on dsvi.serial_number = d.device_serial_number
        left join plant p on d.plant_id =p.id
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        where dsvi.current_version_number != oup.version_number
        and d.is_deleted=0 and p.is_deleted=0 and d.device_status in(1,2)
        and dsvi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        and dsvi.status in (2, 3)
        and dsvi.big_type ='inverter'
        <if test="serialNumbers!= null and serialNumbers.size() >0">
            and dsvi.serial_number in
            <foreach collection="serialNumbers" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        union all
        select
        dsvi.id as deviceSoftwareVersionId,
        dsvi.serial_number,
        dsvi.current_version_number,
        oup.big_type,
        oup.small_type,
        oup.version_number,
        oup.company,
        oup.pack_cdn_url,
        oup.whether_to_force_upgrade,
        oup.business_id,
        oup.check_sum
        from
        device_software_version_info dsvi
        left join wifi_stick_plant wsp
        on
        dsvi.serial_number = wsp.device_serial_number
        left join plant p on
        wsp.plant_id = p.id
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        where
        dsvi.current_version_number != oup.version_number
        and wsp.is_deleted = 0
        and p.is_deleted = 0
        and wsp.wifi_stick_status = 1
        and dsvi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        and dsvi.status in (2
        , 3)
        and dsvi.big_type = 'wifibom'
        <if test="serialNumbers!= null and serialNumbers.size() >0">
            and dsvi.serial_number in
            <foreach collection="serialNumbers" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        union all
        select
        dsvi.id as deviceSoftwareVersionId,
        dsvi.serial_number,
        dsvi.current_version_number,
        oup.big_type,
        oup.small_type,
        oup.version_number,
        oup.company,
        oup.pack_cdn_url,
        oup.whether_to_force_upgrade,
        oup.business_id,
        oup.check_sum
        from
        device_software_version_info dsvi
        left join battery_current_status wsp
        on
        dsvi.serial_number = wsp.device_serial_number
        left join plant p on
        wsp.plant_id = p.id
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        where
        dsvi.current_version_number != oup.version_number
        and wsp.is_deleted = 0
        and p.is_deleted = 0
        and wsp.battery_status != 5
        and dsvi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        and dsvi.status in (2
        , 3)
        and dsvi.big_type = 'battery'
        <if test="serialNumbers!= null and serialNumbers.size() >0">
            and dsvi.serial_number in
            <foreach collection="serialNumbers" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="obtainUpgradeResults">
        update
        device_software_version_info s
        left join device_software_upgrade_record a
        on
        s.id = a.device_software_version_id
        set s.current_version_number = if(#{map.code} in(200,-1), s.latest_released_version, s.current_version_number),
        s.upgrade_completion_time = now(),
        s.update_time = now(),
        s.update_user_account = 'system',
        s.status = if(#{map.code} in(200,-1), 2, 3)
        where a.id = #{map.id};
        update
        device_software_upgrade_record s
        set
        s.upgrade_completion_time = now(),
        s.update_time = now(),
        s.update_user_account = 'system',
        s.status = if(#{map.code} in(200,-1), 2, 3),
        s.failure_reason = #{map.message}
        where s.id = #{map.id};
    </update>

    <update id="obtainUpgradePushResults">
        update
        device_software_version_info s
        set s.status = 1,
        s.update_time = now(),
        s.update_user_account = 'system'
        where s.status = 0
        and s.is_deleted = 0
        <if test="serialNumbers!= null and serialNumbers.size() >0">
            and s.serial_number in
            <foreach collection="serialNumbers" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ;
        update
        device_software_upgrade_record s
        set s.status = 1,
        s.update_time = now(),
        s.update_user_account = 'system'
        where s.status = 0
        and s.is_deleted = 0
        <if test="serialNumbers!= null and serialNumbers.size() >0">
            and s.serial_number in
            <foreach collection="serialNumbers" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ;
    </update>

    <update id="processingTimeoutStatus">
        update
        device_software_upgrade_record t
        set t.status = 3,
        t.update_time = now(),
        t.upgrade_completion_time = now(),
        t.update_user_account ='system',
        t.failure_reason = concat('The device has not returned a response for more than 1 hour, and thus status is set
        failed.')
        where t.status in (0, 1)
        and t.big_type in ('inverter', 'battery')
        and t.update_time <![CDATA[ <= ]]> date_sub(now(), interval 1 hour);

        update
        device_software_version_info t
        set t.status = 3,
        t.update_time = now(),
        t.upgrade_completion_time = now(),
        t.update_user_account ='system'
        where t.status in (0, 1)
        and t.big_type in ('inverter', 'battery')
        and t.update_time <![CDATA[ <= ]]> date_sub(now(), interval 1 hour);

        update
        device_software_upgrade_record t
        set t.status = 3,
        t.update_time = now(),
        t.upgrade_completion_time = now(),
        t.update_user_account ='system',
        t.failure_reason = concat('The device has not returned a response for more than 10 minutes, and thus status is
        set failed.')
        where t.status in (0, 1)
        and t.big_type = 'wifibom'
        and t.update_time <![CDATA[ <= ]]> date_sub(now(), interval 10 minute);

        update
        device_software_version_info t
        set t.status = 3,
        t.update_time = now(),
        t.upgrade_completion_time = now(),
        t.update_user_account ='system'
        where t.status in (0, 1)
        and t.big_type = 'wifibom'
        and t.update_time <![CDATA[ <= ]]> date_sub(now(), interval 10 minute);
    </update>

    <update id="updateStatusAndLatestReleasedVersion">
        <foreach collection="list" item="item" separator=";">
            update device_software_version_info t set t.status = 0 ,t.update_time =now(),t.latest_released_version
            =#{item.versionNumber},t.update_user_account ='system'
            where t.id = #{item.deviceSoftwareVersionId}
        </foreach>
    </update>

    <select id="findDeviceUpgradeListByVersionIds" resultType="org.skyworth.ess.ota.vo.OtaUpdatePackVO">
        select dsvi.id as deviceSoftwareVersionId,
        dsvi.serial_number,
        dsvi.current_version_number,
        oup.big_type,
        oup.small_type,
        oup.version_number,
        oup.company,
        oup.pack_cdn_url,
        oup.whether_to_force_upgrade,
        oup.business_id,
        oup.check_sum
        from device_software_version_info dsvi
        left join device_exit_factory_info defi on
        dsvi.serial_number = defi.device_serial_number
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        and ifnull(defi.firmware_batch,-1) = ifnull(oup.firmware_batch,-1)
        and ifnull(defi.device_type,-1) = ifnull(oup.device_type,-1)
        where dsvi.is_deleted = 0
        and defi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        <if test="list!= null and list.size() >0">
            and dsvi.id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="validDeviceIsOnLine" resultType="java.lang.String">
        select GROUP_CONCAT(distinct dsvi.serial_number)
        from device_software_version_info dsvi
        left join device_21 d on dsvi.serial_number = d.device_serial_number
        left join plant p on d.plant_id = p.id
        where d.is_deleted = 0
        and p.is_deleted = 0
        and d.device_status !=1 and dsvi.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="validDeviceIsLatestVersion" resultType="java.lang.Long">
        select
        dsvi.id
        from
        device_software_version_info dsvi
        left join device_exit_factory_info defi on
        dsvi.serial_number = defi.device_serial_number
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        and ifnull(defi.firmware_batch,-1) = ifnull(oup.firmware_batch,-1)
        and ifnull(defi.device_type,-1) = ifnull(oup.device_type,-1)
        where
        dsvi.current_version_number = oup.version_number
        and dsvi.is_deleted = 0
        and oup.is_deleted = 0
        and oup.is_new_version = 1
        and defi.is_deleted = 0
        and dsvi.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        union
        select
        dsvi.id
        from
        device_software_version_info dsvi
        left join device_exit_factory_info defi on
        dsvi.serial_number = defi.device_serial_number and defi.is_deleted = 0
        left join ota_update_pack oup on
        dsvi.big_type = oup.big_type
        and dsvi.small_type = oup.small_type
        and dsvi.company = oup.company
        and oup.is_new_version = 1
        and oup.is_deleted = 0
        and ifnull(defi.firmware_batch,-1) = ifnull(oup.firmware_batch,-1)
        and ifnull(defi.device_type,-1) = ifnull(oup.device_type,-1)
        where
        oup.version_number is null
        and
        dsvi.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        union
        select
        a.id
        from
        device_software_version_info a
        left join device_software_version_info dsvi on
        a.serial_number = dsvi.serial_number
        and dsvi.status in(0, 1)
        and dsvi .is_deleted = 0
        where (a.status in(0,1) or dsvi.serial_number is not null) and a.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="queryPage" resultType="org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity">
        select ds.id,
        ds.serial_number,
        ds.current_version_number,
        ds.big_type,
        ds.small_type ,
        ds.upgrade_completion_time,
        ds.company,
        ds.retry_count,
        ds.latest_released_version,
        ds.create_user_account ,
        ds.update_user_account,
        ds.tenant_id,
        ds.create_user,
        ds.create_dept,
        ds.create_time ,
        ds.update_user,
        ds.update_time,
        ds.status,
        ds.is_deleted,
        defi.firmware_batch,
        defi.device_type
        from
        device_software_version_info ds
        left join device_exit_factory_info defi on
        ds.serial_number = defi.device_serial_number
        where
        ds.is_deleted = 0
        and defi.is_deleted = 0
        <if test="soft.deptId!=null and soft.deptId!= ''">
            and ds.serial_number in(
            select sp.device_serial_number from wifi_stick_plant sp inner join plant p on p.id=sp.plant_id
            where sp.is_deleted = 0
            and (
            find_in_set(p.operation_company_id ,#{soft.deptId}) != 0
            or ( p.create_user =#{createUser} and p.operation_company_id is null )
            or ( p.create_user =#{createUser} and find_in_set(p.operation_company_id ,#{soft.deptId}) = 0 )
            )
            and p.id not in (select plant_id from plant_agent_unauthorized_user where unauthorized_user_id=#{createUser}
            and is_deleted =0 )
            )
        </if>

        <if test="soft.status!=null and soft.status!=''">
            and ds.status=#{soft.status}
        </if>
        <if test="soft.smallType!=null and soft.smallType!=''">
            and ds.small_type=#{soft.smallType}
        </if>
        <if test="soft.bigType!=null and soft.bigType!=''">
            and ds.big_type=#{soft.bigType}
        </if>
        <if test="soft.serialNumber!=null and soft.serialNumber!=''">
            and ds.serial_number=#{soft.serialNumber}
        </if>
        order by update_time desc, id desc
    </select>

    <update id="otaOfflineUpgradeException">
        update
        device_software_upgrade_record t
        set t.status = 3,
        t.update_time = now(),
        t.upgrade_completion_time = now(),
        t.update_user_account = 'system',
        t.failure_reason = 'Device offline operation reporting exception.'
        where t.status in (0, 1)
        and t.big_type = #{json.bigType}
        and t.small_type = #{json.smallType}
        and t.serial_number = #{json.deviceSn};

        update
        device_software_version_info t
        set t.status = 3,
        t.update_time = now(),
        t.upgrade_completion_time = now(),
        t.update_user_account = 'system'
        where t.status in (0, 1)
        and t.big_type = #{json.bigType}
        and t.small_type = #{json.smallType}
        and t.serial_number = #{json.deviceSn};
    </update>

    <update id="batchDeleteByDeviceSn">
        update device_software_version_info
        set is_deleted = 1,update_time = now()
        where serial_number in
        <foreach collection="listDeviceSn" item="deviceSn" open="(" close=")" separator=",">
            #{deviceSn}
        </foreach>
        and is_deleted = 0;
    </update>

    <select id="queryCanNotUpgradedSN" resultType="java.lang.String">
        select
        distinct dsvi.serial_number
        from
        device_software_version_info dsvi
        where
        dsvi.status not in(0, 2, 3)
        and dsvi.is_deleted = 0
        and dsvi.serial_number in
        <foreach collection="listDeviceSn" item="deviceSn" open="(" close=")" separator=",">
            #{deviceSn}
        </foreach>
    </select>
</mapper>
