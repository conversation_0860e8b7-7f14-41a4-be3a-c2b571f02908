/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.vo.Device23VO;

import java.util.Objects;

/**
 * 设备/逆变器23数据 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public class Device23Wrapper extends BaseEntityWrapper<Device23Entity, Device23VO> {

	public static Device23Wrapper build() {
		return new Device23Wrapper();
	}

	@Override
	public Device23VO entityVO(Device23Entity Device23) {
		if (Device23 == null) {
			return null;
		}
		Device23VO Device23VO = Objects.requireNonNull(BeanUtil.copy(Device23, Device23VO.class));

		//User createUser = UserCache.getUser(Device23.getCreateUser());
		//User updateUser = UserCache.getUser(Device23.getUpdateUser());
		//Device23VO.setCreateUserName(createUser.getName());
		//Device23VO.setUpdateUserName(updateUser.getName());

		return Device23VO;
	}


}
