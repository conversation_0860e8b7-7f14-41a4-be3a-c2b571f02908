/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.gateway.filter;

import com.alibaba.nacos.common.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.i18n.util.I18nUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.gateway.props.AuthProperties;
import org.springblade.gateway.provider.AuthProvider;
import org.springblade.gateway.provider.RequestProvider;
import org.springblade.gateway.provider.ResponseProvider;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import org.springblade.core.redis.cache.BladeRedis;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * 鉴权认证
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class AuthFilter implements GlobalFilter, Ordered {
	private final AuthProperties authProperties;
	private final ObjectMapper objectMapper;
	private final JwtProperties jwtProperties;
	private final AntPathMatcher antPathMatcher = new AntPathMatcher();

	private final BladeRedis bladeRedis;

	@Override
	public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

		//校验 Token 放行
		String originalRequestUrl = RequestProvider.getOriginalRequestUrl(exchange);
		String path = exchange.getRequest().getURI().getPath();
		if (isSkip(path) || isSkip(originalRequestUrl)) {
			return chain.filter(exchange);
		}
		//校验 Token 合法性
		ServerHttpResponse resp = exchange.getResponse();
		String headerToken = exchange.getRequest().getHeaders().getFirst(AuthProvider.AUTH_KEY);
		String paramToken = exchange.getRequest().getQueryParams().getFirst(AuthProvider.AUTH_KEY);
		List<String> strings = exchange.getRequest().getHeaders().get("Accept-Language");
		String languageStr = "";
		if(CollectionUtil.isNotEmpty(strings)) {
			languageStr = strings.get(0);
		}
		if (StringUtils.isBlank(headerToken) && StringUtils.isBlank(paramToken)) {
			return unAuth(resp, "缺失令牌,鉴权失败","Missing token, authentication failed",languageStr);
		}
		String auth = StringUtils.isBlank(headerToken) ? paramToken : headerToken;
		String token = JwtUtil.getToken(auth);
		Claims claims = JwtUtil.parseJWT(token);
		if (token == null || claims == null) {
			return unAuth(resp, "请求未授权","Request Unauthorized,please login again",languageStr);
		}
		String userId = String.valueOf(claims.get(TokenConstant.USER_ID));
		Mono<Void> voidMono = this.checkUserChange(resp, claims, languageStr, userId);
		if (Objects.nonNull(voidMono)) {
			return voidMono;
		}

		//判断 Token 状态
		if (jwtProperties.getState()) {
			String tenantId = String.valueOf(claims.get(TokenConstant.TENANT_ID));
			String clientId = String.valueOf(claims.get(TokenConstant.CLIENT_ID));
			String accessToken = JwtUtil.getAccessToken(tenantId, clientId, userId, token);
			if (!token.equalsIgnoreCase(accessToken)) {
				return unAuth(resp, "令牌已失效","Token has expired,please login again",languageStr);
			}
		}
		List<String> timeZoneList = exchange.getRequest().getHeaders().get("Time-Zone");
		if (CollectionUtil.isNotEmpty(timeZoneList)) {
			String timeZone = timeZoneList.get(0);
			bladeRedis.set(CommonConstant.WEB_TIME_ZONE_FOR_USER_PRE + userId, timeZone);
		}
		return chain.filter(exchange);
	}
	private  Mono<Void> checkUserChange(ServerHttpResponse resp,Claims claims,String languageStr,String userId) {
//		 bladeRedis.set(CommonConstant.BLADE_USER_CHANGE_PRE + userId,"true");
		Object value = bladeRedis.get(CommonConstant.BLADE_USER_CHANGE_PRE + userId);
		if(Objects.isNull(value)) {
			return null;
		}
//		throw new BusinessException("gateway.blade.user.change");
		String string = I18nUtil.getI18nByLanguage("gateway.blade.user.change",languageStr);
		resp.setStatusCode(HttpStatus.UNAUTHORIZED);
		resp.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
		String result = "";
		try {
			result = objectMapper.writeValueAsString(ResponseProvider.unAuth(string));
		} catch (JsonProcessingException e) {
			log.error(e.getMessage(), e);
		}
		bladeRedis.del(CommonConstant.BLADE_USER_CHANGE_PRE + userId);
		DataBuffer buffer = resp.bufferFactory().wrap(result.getBytes(StandardCharsets.UTF_8));
		return  resp.writeWith(Flux.just(buffer));
	}
	private boolean isSkip(String path) {
		return AuthProvider.getDefaultSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path))
			|| authProperties.getSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path))
 			|| authProperties.getAuth().stream().anyMatch(auth -> antPathMatcher.match(auth.getPattern(), path))
 			|| authProperties.getBasic().stream().anyMatch(basic -> antPathMatcher.match(basic.getPattern(), path))
			|| authProperties.getSign().stream().anyMatch(sign -> antPathMatcher.match(sign.getPattern(), path));
	}

	private Mono<Void> unAuth(ServerHttpResponse resp, String msg,String enMsg,String languageStr) {
		resp.setStatusCode(HttpStatus.UNAUTHORIZED);
		resp.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
		String result = "";
		try {
			if (languageStr.contains(CommonConstant.CURRENT_LANGUAGE_ZH)) {
				result = objectMapper.writeValueAsString(ResponseProvider.unAuth(msg));
			} else  {
				result = objectMapper.writeValueAsString(ResponseProvider.unAuth(enMsg));
			}
		} catch (JsonProcessingException e) {
			log.error(e.getMessage(), e);
		}
		DataBuffer buffer = resp.bufferFactory().wrap(result.getBytes(StandardCharsets.UTF_8));
		return resp.writeWith(Flux.just(buffer));
	}


	@Override
	public int getOrder() {
		return -100;
	}

}
