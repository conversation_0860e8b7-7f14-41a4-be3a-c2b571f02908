/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer;
import org.skyworth.ess.util.BigDecimalSerializer1Scale;
import org.skyworth.ess.util.BigDecimalSerializer2Scale;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器当前状态 实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@TableName("device_current_status")
@ApiModel(value = "DeviceCurrentStatus对象", description = "设备/逆变器当前状态")
@EqualsAndHashCode(callSuper = true)
public class DeviceCurrentStatusEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 总能量
	 */
	@ApiModelProperty(value = "总能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal totalEnergy;
	/**
	 * 总发电时间（小时）
	 */
	@ApiModelProperty(value = "总发电时间（小时）")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal totalGenerationTime;
	/**
	 * 今日能量
	 */
	@ApiModelProperty(value = "今日能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayEnergy;
	/**
	 * 累计正极能量
	 */
	@ApiModelProperty(value = "累计正极能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal accumulatedEnergyOfPositive;
	/**
	 * 累计负极能量
	 */
	@ApiModelProperty(value = "累计负极能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal accumulatedEnergyOfNegative;
	/**
	 * 累计负载能量
	 */
	@ApiModelProperty(value = "累计负载能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal accumulatedEnergyOfLoad;
	/**
	 * 今日输入能量
	 */
	@ApiModelProperty(value = "今日输入能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayImportEnergy;
	/**
	 * 今日输出能量
	 */
	@ApiModelProperty(value = "今日输出能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayExportEnergy;
	/**
	 * 今日负载能量
	 */
	@ApiModelProperty(value = "今日负载能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal todayLoadEnergy;
	/**
	 * 日均EPS能量
	 */
	@ApiModelProperty(value = "日均EPS能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal dailyEnergyToEps;
	/**
	 * 累计EPS能量
	 */
	@ApiModelProperty(value = "累计EPS能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal accumulatedEnergyToEps;
	/**
	 * 电池SOC
	 */
	@ApiModelProperty(value = "电池SOC")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal batterySoc;
	/**
	 * 电池日均充电能量
	 */
	@ApiModelProperty(value = "电池日均充电能量")
	private BigDecimal batteryDailyChargeEnergy;
	/**
	 * 电池日均放电能量
	 */
	@ApiModelProperty(value = "电池日均放电能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal batteryDailyDischargeEnergy;
	/**
	 * 电池累计充电能量
	 */
	@ApiModelProperty(value = "电池累计充电能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 * 电池累计放电能量
	 */
	@ApiModelProperty(value = "电池累计放电能量")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal batteryAccumulatedDischargeEnergy;
	/**
	 * 逆变器模式
	 */
	@ApiModelProperty(value = "逆变器模式")
	private String inverterMode;
	/**
	 * MPPT1电压
	 */
	@ApiModelProperty(value = "MPPT1电压")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal pv1Voltage;
	/**
	 * MPPT1电流
	 */
	@ApiModelProperty(value = "MPPT1电流")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal pv1Current;
	/**
	 * MPPT1功率
	 */
	@ApiModelProperty(value = "MPPT1功率")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal mppt1Power;
	/**
	 * MPPT2电压
	 */
	@ApiModelProperty(value = "MPPT2电压")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal pv2Voltage;
	/**
	 * MPPT2电流
	 */
	@ApiModelProperty(value = "MPPT2电流")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal pv2Current;
	/**
	 * MPPT2功率
	 */
	@ApiModelProperty(value = "MPPT2功率")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal mppt2Power;
	/**
	 * MPPT3电压
	 */
	@ApiModelProperty(value = "MPPT3电压")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal pv3Voltage;
	/**
	 * MPPT3电流
	 */
	@ApiModelProperty(value = "MPPT3电流")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal pv3Current;
	/**
	 * MPPT3功率
	 */
	@ApiModelProperty(value = "MPPT3功率")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal mppt3Power;
	/**
	 * MPPT4电压
	 */
	@ApiModelProperty(value = "MPPT4电压")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal pv4Voltage;
	/**
	 * MPPT4电流
	 */
	@ApiModelProperty(value = "MPPT4电流")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal pv4Current;
	/**
	 * MPPT4功率
	 */
	@ApiModelProperty(value = "MPPT4功率")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal mppt4Power;
	/**
	 * 电池电压
	 */
	@ApiModelProperty(value = "电池电压")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal batteryVoltage;
	/**
	 * 电池电流
	 */
	@ApiModelProperty(value = "电池电流")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal batteryCurrent;
	/**
	 * 电池功率
	 */
	@ApiModelProperty(value = "电池功率")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal batteryPower;
	/**
	 * 电压phase a
	 */
	@ApiModelProperty(value = "电压phase a")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseAVoltage;
	/**
	 * 电流phase a
	 */
	@ApiModelProperty(value = "电流phase a")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseACurrent;
	/**
	 * 功率phase a
	 */
	@ApiModelProperty(value = "功率phase a")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseAPower;
	/**
	 * 电压phase b
	 */
	@ApiModelProperty(value = "电压phase b")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseBVoltage;
	/**
	 * 电流phase b
	 */
	@ApiModelProperty(value = "电流phase b")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseBCurrent;
	/**
	 * 功率phase b
	 */
	@ApiModelProperty(value = "功率phase b")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseBPower;
	/**
	 * 电压phase c
	 */
	@ApiModelProperty(value = "电压phase c")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseCVoltage;
	/**
	 * 电流phase c
	 */
	@ApiModelProperty(value = "电流phase c")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseCCurrent;
	/**
	 * 功率phase c
	 */
	@ApiModelProperty(value = "功率phase c")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseCPower;
	/**
	 * 电压 r of grid
	 */
	@ApiModelProperty(value = "电压 r of grid")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal l1NPhaseVoltageOfGrid;
	/**
	 * 电流 r of grid
	 */
	@ApiModelProperty(value = "电流 r of grid")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal l1CurrentOfGrid;
	/**
	 * 功率 r of grid
	 */
	@ApiModelProperty(value = "功率 r of grid")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseRWattOfGrid;
	/**
	 * 电压 s of grid
	 */
	@ApiModelProperty(value = "电压 s of grid")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal l2NPhaseVoltageOfGrid;
	/**
	 * 电流 s of grid
	 */
	@ApiModelProperty(value = "电流 s of grid")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal l2CurrentOfGrid;
	/**
	 * 功率 s of grid
	 */
	@ApiModelProperty(value = "功率 s of grid")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseSWattOfGrid;
	/**
	 * 电压 t of grid
	 */
	@ApiModelProperty(value = "电压 t of grid")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal l3NPhaseVoltageOfGrid;
	/**
	 * 电流 t of grid
	 */
	@ApiModelProperty(value = "电流 t of grid")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal l3CurrentOfGrid;
	/**
	 * 功率 t of grid
	 */
	@ApiModelProperty(value = "功率 t of grid")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseTWattOfGrid;
	/**
	 * 电压 r of load
	 */
	@ApiModelProperty(value = "电压 r of load")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal l1NPhaseVoltageOfLoad;
	/**
	 * 电流 r of load
	 */
	@ApiModelProperty(value = "电流 r of load")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal l1CurrentOfLoad;
	/**
	 * 功率 r of load
	 */
	@ApiModelProperty(value = "功率 r of load")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseRWattOfLoad;
	/**
	 * 电压 s of load
	 */
	@ApiModelProperty(value = "电压 s of load")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal l2NPhaseVoltageOfLoad;
	/**
	 * 电流 s of load
	 */
	@ApiModelProperty(value = "电流 s of load")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal l2CurrentOfLoad;
	/**
	 * 功率 s of load
	 */
	@ApiModelProperty(value = "功率 s of load")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseSWattOfLoad;
	/**
	 * 电压 t of load
	 */
	@ApiModelProperty(value = "电压 t of load")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal l3NPhaseVoltageOfLoad;
	/**
	 * 电流 t of load
	 */
	@ApiModelProperty(value = "电流 t of load")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal l3CurrentOfLoad;
	/**
	 * 功率  t of load
	 */
	@ApiModelProperty(value = "功率  t of load")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseTWattOfLoad;
	/**
	 * 电压 r of eps
	 */
	@ApiModelProperty(value = "电压 r of eps")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseRVoltageOfEps;
	/**
	 * 电流  r of eps
	 */
	@ApiModelProperty(value = "电流  r of eps")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseRCurrentOfEps;
	/**
	 * 功率  r of eps
	 */
	@ApiModelProperty(value = "功率  r of eps")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseRWattOfEps;
	/**
	 * 电压  s of eps
	 */
	@ApiModelProperty(value = "电压  s of eps")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseSVoltageOfEps;
	/**
	 * 电流  s of eps
	 */
	@ApiModelProperty(value = "电流  s of eps")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseSCurrentOfEps;
	/**
	 * 功率  s of eps
	 */
	@ApiModelProperty(value = "功率  s of eps")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseSWattOfEps;
	/**
	 * 电压 t of eps
	 */
	@ApiModelProperty(value = "电压 t of eps")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseTVoltageOfEps;
	/**
	 * 电流 t of eps
	 */
	@ApiModelProperty(value = "电流 t of eps")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseTCurrentOfEps;
	/**
	 * 功率  t of eps
	 */
	@ApiModelProperty(value = "功率  t of eps")
	@JsonSerialize(using = BigDecimalSerializer1Scale.class)
	private BigDecimal phaseTWattOfEps;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;


	/**
	 * 运行天数，根据当前日期减创建日期
	 */
	@ApiModelProperty(value = "运行天数")
	@TableField(exist = false)
	private Long runDays;

	@ApiModelProperty(value = "")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal frequencyOfGrid;

	@ApiModelProperty(value = "")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal frequencyOfEps;

	@ApiModelProperty(value = "")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal innerTemperature;

	@ApiModelProperty(value = "")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal numberOfBattery;

	@ApiModelProperty(value = "工作模式")
	private String hybridWorkMode;

	@TableField(exist = false)
	@ApiModelProperty(value = "电网相数")
	private String gridPhaseNumber;
}
