package org.skyworth.ess.fegin;

import lombok.AllArgsConstructor;
import org.skyworth.ess.addressmap.service.IAddressMapDefinitionService;
import org.skyworth.ess.device.client.IAddressMapDefinitionBiz;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class IAddressMapDefinitionFeign implements IAddressMapDefinitionBiz {

    private IAddressMapDefinitionService addressMapDefinitionService;

    @PostMapping(address)
    @Override
    public Map<String, Map<String, String>> getAllAddressMap(@RequestBody Map<String, Object> addressMapDefinition){
          return addressMapDefinitionService.getAllAddressMapByAddress((Integer) addressMapDefinition.get("beginAddressNum"), (Integer) addressMapDefinition.get("endAddressNum"), (String) addressMapDefinition.get("modbusProtocolVersion"));
    }
}
