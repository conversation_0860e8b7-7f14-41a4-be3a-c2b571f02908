package org.skyworth.ess.log.service;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.skyworth.ess.log.mapper.OriginalDataRecordMapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class OriginalDataRecordServiceImpl extends BaseServiceImpl<OriginalDataRecordMapper, OriginalDataRecord> implements OriginalDataRecordService {

	@Override
	@Cached(name = "timeZoneByDeviceSn", expire = 12, timeUnit = TimeUnit.HOURS, cacheType = CacheType.BOTH)
	public Map<String, String> getTimeZone(String deviceSn) {
		return baseMapper.getTimeZone(deviceSn);
	}

	@Override
	public Map<String, Object> getStartupByBackstage(long plantId, String deviceSn) {
		return baseMapper.getStartupByBackstage(plantId, deviceSn);
	}

	@Override
	public int updateWifiAndPlant(long plantId, String deviceSn) {
		return baseMapper.updateWifiAndPlant(plantId, deviceSn);
	}

	@Override
	public List<Map<String, Object>> isExistPlant(long plantId, String deviceSn) {
		return baseMapper.isExistPlant(plantId, deviceSn);
	}

	@Override
	public List<Map<String, Object>> isParallelModeList(List<Long> plantIds) {
		return baseMapper.isParallelModeList(plantIds);
	}
}
