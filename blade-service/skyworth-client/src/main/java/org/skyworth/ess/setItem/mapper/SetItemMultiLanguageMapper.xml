<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.setItem.mapper.SetItemMultiLanguageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="setItemMultiLanguageResultMap" type="org.skyworth.ess.setItem.entity.SetItemMultiLanguageEntity">
        <result column="item_id" property="itemId"/>
        <result column="item_language_type" property="itemLanguageType"/>
        <result column="item_language_name" property="itemLanguageName"/>
    </resultMap>


    <select id="selectSetItemMultiLanguagePage" resultMap="setItemMultiLanguageResultMap">
        select * from device_set_item_multi_language where is_deleted = 0
    </select>


    <select id="exportSetItemMultiLanguage" resultType="org.skyworth.ess.setItem.excel.SetItemMultiLanguageExcel">
        SELECT * FROM device_set_item_multi_language ${ew.customSqlSegment}
    </select>

    <select id="selectListByItemId" resultType="com.alibaba.fastjson.JSONObject">
        select item_id as itemId,item_language_name as itemLanguageName,item_language_type as itemLanguageType
        from device_set_item_multi_language where item_id in
        <foreach collection="longList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteLogicByItemId">
        delete from device_set_item_multi_language where item_id in
        <foreach collection="longList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
