package org.skyworth.ess.device.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.client.IAddressMapDefinitionBiz;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.utils.DictInverterEnum;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

/**
 * <AUTHOR>
 * */
@Slf4j
public class TestBinaryConvertUtils2 {

	private static final Integer binaryNum=4;

	private static final List<String> SPECIAL_CODE = Arrays.asList("1A3B","1A48","2100","2110","2115","211C","2122","2123","2141","2143","2149","212E","214C",
		"30B3","30B5","30B2","6001","6006","6009","30B1","2168","216A","216C","216E","2156","2170","2178","2174","217C","2176","217E","2172","217A","5116","1434","3089","6070","214D");

	public static void main(String[] args) {
		String camelCase = toCamelCase("Energy today of Ac Couple");
		JSONObject result=new JSONObject();
		transferData("FFFE",result,"batteryMinimumCellTemperature","1","S16");
		System.out.println(result);

	}
	public JSONObject getData(String content, String bizType,String version,Statement stmt, ResultSet rs ) throws SQLException {
		JSONObject result=new JSONObject();
		int current=0;
		int len=content.length();
		boolean flag=false;
		while (current<len){
			int begin=current+8;
			String header=content.substring(current,begin);
			String beginAddress=header.substring(0,4);
			//结束长度
			String lenAddress=header.substring(4,8);
			current=current+8;
			//结束地址
			String endAddress=BinaryToHexUtils.HexadecimalAddition(beginAddress,lenAddress);
			//开始地址10进制数据
			int beginAddressNum=BinaryToHexUtils.hexToDecimal(beginAddress);
			//结束地址10进制数据
			int endAddressNum=BinaryToHexUtils.hexToDecimal(endAddress);
			Map<String,Object> params=new HashMap<>();
			params.put("beginAddressNum",beginAddressNum);
			params.put("endAddressNum",endAddressNum);
			params.put("modbusProtocolVersion",version);
			log.info("bizType : {} ,beginAddressNum:{},endAddressNum:{},modbusProtocolVersion:{}",bizType,beginAddressNum,endAddressNum,version);
			Map<String, Map<String,String>> redisMap= queryDataFromDatabase(stmt,rs,beginAddressNum,endAddressNum);//addressMapDefinitionBiz.getAllAddressMap(params);
			log.info("redisMap : {}",redisMap);
            if(ObjectUtil.isNotNull(redisMap) && redisMap.isEmpty()){
				flag=true;
				break;
			}
			String nextAddress="";
			for(Map.Entry<String,Map<String,String>> entry: redisMap.entrySet()){
				String addressCode=entry.getKey();
				//地址码对应数据
				Map<String,String> cacheAddress=entry.getValue();

				String definition=cacheAddress.get("definition");
				Integer num=Integer.parseInt(cacheAddress.get("length"));
				String type=cacheAddress.get("dataType");
				String unit=cacheAddress.get("unit");
				int size=binaryNum*num;

				if(!StringUtils.isEmpty(nextAddress)){
					// 跳过没有使用的地址码,如果db中没有配置，nextAddress 为循环中当前地址码，addressCode 为数据库中 实际需要的当前地址码，
					// 如果 循环中的当前地址 和 db中的下一个地址不相等，则表示 nextAddress 没有配置在db中
					// 不相等，则表示这个地址码没有用到，不用解析具体值，
					// 则用 db 中 应该的当前地址码 减去 循环的 当前地址码，看相差 几个地址码（乘以4为每个地址码为4个字符）
					// current = current + total*4 为 实际需要的当前地址的数据开始位置
                  if(!addressCode.equals(nextAddress)){
					  int total=BinaryToHexUtils.HexadecimalSubtractLen(addressCode,nextAddress);
					  current+=(total*binaryNum);
				  }
				}

				//16进制的数据
				String dataBinary=content.substring(current,current+size);
				log.info("addressCode : {},dataBinary:{},current:{}, size:{}",addressCode,dataBinary,current,size);
				String key=toCamelCase(definition);
				current+=size;
				switch (bizType){
					case Constants.ONE: processLog1(addressCode, dataBinary, result, key, unit, type); break;
					case Constants.SECOND: processLog2(addressCode, dataBinary, result, key, unit, type); break;
					case Constants.THIRD: processLog3(addressCode, dataBinary, result, key, unit,type); break;
					case Constants.FOUR: processLog4(addressCode, dataBinary, result, key, unit,type); break;
					default: break;
				}
				nextAddress=BinaryToHexUtils.HexadecimalAdditionLen(addressCode,num+"");

			}
		}
		if(flag){return null;}

		return result;
	}

	/**
	 * 处理2.1数据
	 * */
	private static void processLog1(String addressCode,String dataBinary, JSONObject result, String key,String unit, String type) {
		if(Constants.MODBUS_PROTOCOL_VERSION.equals(addressCode)){
			result.put(key, dataBinary.substring(0, 2) + "." + dataBinary.substring(2, 4));
		}else if(SPECIAL_CODE.contains(addressCode)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			result.put(key,v+"");
		}else if("1A00".contains(addressCode)||"1A1C".contains(addressCode)||"1A26".contains(addressCode)||"1A60".contains(addressCode)
			||"1A6F".contains(addressCode)||"1A10".equals(addressCode)){
			String v=BinaryToHexUtils.hex16ToAscii(dataBinary);
			v=v.trim();
			result.put(key,v);
		}else {
			if(Constants.U16.equals(type)){
				if(Constants.ASCII.equals(unit)){
					String v=BinaryToHexUtils.hex16ToAscii(dataBinary);
					result.put(key,v);
				}else { //数值
					int v=BinaryToHexUtils.hexToDecimal(dataBinary);
					if(!StringUtils.isEmpty(unit)){
						double resultObj;
						if(isInteger(unit)){
							resultObj= (double)v*(Integer.parseInt(unit));
						}else {
							resultObj=(double)v*(Double.parseDouble(unit));
						}
						result.put(key,resultObj);
					}else {
						result.put(key,(double)v);
					}
				}
			}else { // string类型
				String v=BinaryToHexUtils.hex16ToAscii(dataBinary).trim();
				result.put(key,v);
			}
		}
	}

	/**
	 * 处理2.2数据
	 * */
	private static void processLog2(String addressCode, String dataBinary, JSONObject result, String key,String unit, String type) {
		if(Constants.INVERTER_MODE.equals(addressCode)||Constants.ERROR_CODE.equals(addressCode)||Constants.ERROR_CODE_MESSAGE.equals(addressCode)
			||Constants.ERROR_CODE2.equals(addressCode)||Constants.ERROR_CODE3.equals(addressCode)){
			String value="";
			if(Constants.INVERTER_MODE.equals(addressCode)){
				int code=BinaryToHexUtils.hexToDecimal(dataBinary);
				value= DictInverterEnum.match(code).getMessage();
			}else {
				value=BinaryToHexUtils.hexToBinaryByRepairZero(dataBinary);
			}
			result.put(key,value);
		}else if("104A".equals(addressCode)){
			String data=BinaryToHexUtils.hexToBinaryByRepairZero(dataBinary);
			StringBuilder sb=new StringBuilder(data);
			sb.reverse();
//			result.put(key,sb.substring(4,6));
			StringBuilder sb1 = new StringBuilder(sb.substring(4,6));
			String batteryStatus=sb1.reverse().toString();
			log.info("processLog2 104A data -> {}, reverse : {}，4-6 data : {} , reverse data : {} ",data,sb, sb1, batteryStatus);
			if("01".equals(batteryStatus)){
				//充电
				batteryStatus="1";
			}else if("10".equals(batteryStatus)){
				//放电
				batteryStatus="2";
			}else if("00".equals(batteryStatus)){
				//不存在 离线
				batteryStatus="5";
			}else if("11".equals(batteryStatus)){
				//满充 为 11 更新为在线
				batteryStatus="3";
			} else {
				//其他情况 默认充电
				batteryStatus="1";
			}

			result.put("batteryStatus",batteryStatus);
		} else {
			transferData(dataBinary, result, key, unit, type);
		}
	}

	/**
	 * 处理2.3数据
	 * */
	private static void processLog3(String addressCode, String dataBinary, JSONObject result, String key, String unit,String type) {
		if(SPECIAL_CODE.contains(addressCode)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			result.put(key,v+"");
		}else if("2101".equals(addressCode)||"2106".equals(addressCode)||"210B".equals(addressCode)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			result.put(key,v+"");
		}else if("2111".equals(addressCode)){
			result.put(key, dataBinary);
		}else if("2102".equals(addressCode)||"2103".equals(addressCode)||"2104".equals(addressCode)
			||"2105".equals(addressCode) ||"2107".equals(addressCode) ||"2108".equals(addressCode) ||"2109".equals(addressCode) ||"210A".equals(addressCode)
			||"210C".equals(addressCode) ||"210D".equals(addressCode) ||"210E".equals(addressCode) ||"210F".equals(addressCode)) {
			try {
				int hour = BinaryToHexUtils.hexToDecimal(dataBinary.substring(0, 2));
				int minute = BinaryToHexUtils.hexToDecimal(dataBinary.substring(2, 4));
				String v = (hour<=9?"0"+hour:hour) + ":" + (minute<=9?"0"+minute:minute);
				result.put(key,v);
			}catch (Exception e){
				result.put(key,null);
			}
		}else {
			transferData(dataBinary, result, key, unit, type);
		}
	}


	/**
	 * 处理2.4数据
	 * */
	private static void processLog4(String addressCode, String dataBinary, JSONObject result, String key, String unit,String type) {
		if("3000".equals(addressCode)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			result.put(key,v+"");
		}else if("3001".equals(addressCode)){//月天
            int total=Integer.parseInt(dataBinary,16);
			int high=total>>8;  //高位
			int low=total&(0xff); //低位
			result.put(key,(high<10?"0"+high:high)+"-"+(low<10?"0"+low:low));
		}else if("3003".equals(addressCode)){//秒
			int total=Integer.parseInt(dataBinary,16);
			int v=total>>8;
			result.put(key,(v<10?"0"+v:v)+"");
		}else if("3002".equals(addressCode)){  //小时分钟
			int total=Integer.parseInt(dataBinary,16);
			int high=total>>8;  //高位
			int low=total&(0xff); //低位
			result.put(key,(high<10?"0"+high:high)+":"+(low<10?"0"+low:low));
		}else if(SPECIAL_CODE.contains(addressCode)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			result.put(key,v+"");
		}else if("5101".equals(addressCode)){
			result.put(key,dataBinary);
		} else {
			transferData(dataBinary, result, key, unit, type);
		}
	}

	private static void transferData(String dataBinary, JSONObject result, String key, String unit, String type) {
		//有符号位数字
		if(Constants.S16.equals(type)) {
			short v = BinaryToHexUtils.hexToShortForSymbol(dataBinary);
			// 如果单位为空，不需要转换
			if(StringUtils.isEmpty(unit)) {
				result.put(key, (double) v);
				return;
			}
			double resultObj;
			if (isInteger(unit)) {
				resultObj = (double) v * (Integer.parseInt(unit));
			} else {
				resultObj = (double) v * (Double.parseDouble(unit));
			}
			result.put(key,resultObj);
		} else if(Constants.S32.equals(type)){
			int v=BinaryToHexUtils.hexToDecimalForSymbol(dataBinary);
			if(!StringUtils.isEmpty(unit)) {
				double resultObj;
				if (isInteger(unit)) {
					resultObj = (double) v * (Integer.parseInt(unit));
				} else {
					resultObj = (double) v * (Double.parseDouble(unit));
				}
				result.put(key,resultObj);
			}else {
				result.put(key, (double) v);
			}
		}//无符号数据
		else if(Constants.U16.equals(type)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			if(!StringUtils.isEmpty(unit)){
				double resultObj;
				if(isInteger(unit)){
					resultObj=(double)v*(Integer.parseInt(unit));
				}else {
					resultObj=(double)v*(Double.parseDouble(unit));
				}
				result.put(key,resultObj);
			}else {
				result.put(key, (double) v);
			}
		} else if(Constants.U32.equals(type)){
			long l = Long.parseUnsignedLong(dataBinary,16);
			if(!StringUtils.isEmpty(unit)){
				float resultObj;
				if(isInteger(unit)){
					resultObj=(float)l*(Float.valueOf(unit));
				}else {
					resultObj=(float)l*(Float.valueOf(unit));
				}
				result.put(key,resultObj);
			}else {
				result.put(key, (float) l);
			}
		}//字符串 或者 ASCII
		else if(Constants.STR.equals(type)||Constants.ASCII.equals(type)){
			String v=BinaryToHexUtils.hex16ToAscii(dataBinary).trim();
			result.put(key,v);
		}
	}


	/**
	 * 将字段映射成属性
	 * */
	public static String toCamelCase(String param) {
		if (param == null || param.trim().isEmpty()) {
			return "";
		}
		int len = param.length();
		StringBuilder sb = new StringBuilder(len);
		boolean tmp=false;
		for (int i = 0; i < len; i++) {
			char c = param.charAt(i);
			if(i==0){
				sb.append(Character.toLowerCase(c));
			}else {
				if(tmp){
					sb.append(Character.toUpperCase(c));
					tmp=false;
				}else {
					//是否包含空格
					if (Character.isSpaceChar(c)) {
						tmp = true;
					} else {
						sb.append(c);
					}
				}
			}

		}
		return sb.toString();
	}



	/**
	 * 判断是否是整形
	 * */
	public static boolean isInteger(String str) {
		try {
			Integer.parseInt(str);
			return true;
		} catch (NumberFormatException e) {
			return false;
		}
	}

	private Map<String, Map<String, String>> queryDataFromDatabase(Statement stmt, ResultSet rs , int beginAddress, int endAddress) throws SQLException {
		// 4. 执行查询（替换为你的表名和列）
		String sql = "select * from client_de.address_map_definition where decimal_address >= " + beginAddress + " and decimal_address <=" + endAddress
				+ " and modbus_protocol_version = '0.0.1' order by decimal_address asc";

		rs = stmt.executeQuery(sql);

		Map<String, Map<String, String>> resultMap = new HashMap<>();
		// 5. 处理结果集
		while (rs.next()) {
			String address = rs.getString("address"); // 外层Map的key
			Map<String, String> innerMap = new HashMap<>();
			innerMap.put("id", String.valueOf(rs.getLong("id")));
			innerMap.put("modbusProtocolVersion", rs.getString("modbus_protocol_version"));
			innerMap.put("definition", rs.getString("definition"));
			innerMap.put("setItemDefinition", rs.getString("set_item_definition"));
			innerMap.put("dataType", rs.getString("data_type"));
			innerMap.put("unit", rs.getString("unit"));
			innerMap.put("length", String.valueOf(rs.getInt("length"))); // 假设LENGTH是整数类型
			innerMap.put("decimalAddress", String.valueOf(rs.getInt("decimal_address"))); // 假设DECIMAL_ADDRESS是整数类型
			resultMap.put(address, innerMap);
		}
		return new TreeMap<>(resultMap);
	}

}
