package org.skyworth.ess.device;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.DictErrorCodeEnum;
import org.skyworth.ess.device.entity.DictErrorMessageEnum;
import org.skyworth.ess.utils.*;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class BinaryConvertUtils {


	private static final Integer binaryNum=4;


	public  JSONObject getData(String content,String bizType,Map<String,Map<String,String>> redisMap){
		int len=content.length();
		int current=0;
		boolean flag=false;
		JSONObject result=new JSONObject();
		while(current<len){
			String addressCode=content.substring(current,current+4);
			Map<String,String> cacheAddress=redisMap.get(addressCode);  //到时从redis读取
			if(cacheAddress==null||cacheAddress.isEmpty()){  //缓存中找不到
				flag=true;
				break;
			}else {
				String definition=cacheAddress.get("definition");
				Integer num=Integer.parseInt(cacheAddress.get("length"));
				String type=cacheAddress.get("dataType");
				String unit=cacheAddress.get("unit");  //只针对数字
                int size=binaryNum*num;
				int sum=current+4;
				String dataBinary=content.substring(sum,sum+size);//16进制的数据
				String key=toCamelCase(definition);
				if(Constants.SECOND.equals(bizType)){ //2.2数据
					processLog2(addressCode, dataBinary, result, key, type, unit);
				}
				else if(Constants.ONE.equals(bizType)){//2.1数据
					processLog1(addressCode, result, key, dataBinary, type, unit);
				}
				else {//2.3数据
					processLog3(addressCode, dataBinary, result, key, unit);
				}
				current=sum+size;
			}
		}
        if(flag){
			return null;
		}
		return result;
	}

	/**
	 * 处理2.3数据
	 * */
	private static void processLog3(String addressCode, String dataBinary, JSONObject result, String key, String unit) {
		if(Constants.Hybrid_work_mode.equals(addressCode)||Constants.Battery_type_selection.equals(addressCode)||
			Constants.Grid_charge.equals(addressCode)||Constants.off_grid_mode.equals(addressCode)){
			String v="";
			if(Constants.Hybrid_work_mode.equals(addressCode)){
				v= DictHybridWorkEnum.match(dataBinary).getMessage();
			}else if(Constants.Battery_type_selection.equals(addressCode)){
				v= DictBatteryTypeEnum.match(dataBinary).getMessage();
			}else {
				v= DictGridChargeEnum.match(dataBinary).getMessage();
			}
			result.put(key,v);
		}else if("2101".equals(addressCode)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			String resultObj="";
			if(v==0){
				resultObj="Once";
			}else {
				resultObj="EveryDay";
			}
			result.put(key,resultObj);
		}else if("2111".equals(addressCode)){
			result.put(key, dataBinary);
		}else if("2102".equals(addressCode)||"2103".equals(addressCode)||"2104".equals(addressCode)
			||"2105".equals(addressCode)) {
			String v= dataBinary.substring(0,2)+":"+ dataBinary.substring(2,4);
			result.put(key,v);
		}else {
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			if(!StringUtils.isEmpty(unit)){
				double resultObj;
				if(isInteger(unit)){
					resultObj= v*(Integer.parseInt(unit));
				}else {
					resultObj=v*(Double.parseDouble(unit));
				}
				result.put(key,resultObj);
			}
			result.put(key, (double) v);
		}
	}

	/**
	 * 处理2.1数据
	 * */
	private static void processLog1(String addressCode, JSONObject result, String key, String dataBinary, String type, String unit) {
		if(Constants.MODBUS_PROTOCOL_VERSION.equals(addressCode)){//modbus version
			result.put(key, dataBinary.substring(0, 2) + "." + dataBinary.substring(2, 4));
		}else if(Constants.MPPT_NUMBER.equals(addressCode) || Constants.GRID_PHASE_NUMBER.equals(addressCode)){
			int v=BinaryToHexUtils.hexToDecimal(dataBinary);
			result.put(key,v+"");
		} else {
			if(Constants.U16.equals(type)){
				if(Constants.ASCII.equals(unit)){
					String v=BinaryToHexUtils.hex16ToAscii(dataBinary);
					result.put(key,v);
				}else { //数值
					int v=BinaryToHexUtils.hexToDecimal(dataBinary);
					double resultObj;
					if(!StringUtils.isEmpty(unit)){
						if(isInteger(unit)){
							resultObj= v*(Integer.parseInt(unit));
						}else {
							resultObj=v*(Double.parseDouble(unit));
						}
						result.put(key,resultObj);
					}
					result.put(key,(double)v);

				}
			}else { // string类型
//				StringBuilder sb=new StringBuilder();
//				for(int i=0;i< dataBinary.length();){
//					sb.append(dataBinary, i, i+4).append(" ");
//					i+=4;
//				}
				String v=BinaryToHexUtils.hex16ToAscii(dataBinary);
				result.put(key,v);
			}
		}
	}

	/**
	 * 处理2.2数据
	 * */
	private static void processLog2(String addressCode, String dataBinary, JSONObject result, String key, String type, String unit) {
//		key=convertToUnderscore(key);
		if(Constants.INVERTER_MODE.equals(addressCode)||Constants.ERROR_CODE.equals(addressCode)||Constants.ERROR_CODE_MESSAGE.equals(addressCode)){  //字符串，映射表格数据
			int code=BinaryToHexUtils.hexToDecimal(dataBinary);
			String value="";
			if(Constants.INVERTER_MODE.equals(addressCode)){
				value= DictInverterEnum.match(code).getMessage();
			}else if(Constants.ERROR_CODE.equals(addressCode)){
				value= DictErrorCodeEnum.match(code).getMessage();
			}else {
				value= DictErrorMessageEnum.match(code).getMessage();
			}
			result.put(key,value);
		}else {
			if(Constants.S16.equals(type)||Constants.S32.equals(type)){ //有符号位数字
				String binary=BinaryToHexUtils.hexToBinary(dataBinary); //16进制转2进制
				int v=BinaryToHexUtils.binaryToDecimal(binary); //2进制转10进制
				double resultObj;
				if(isInteger(unit)){
					resultObj= v*(Integer.parseInt(unit));
				}else {
					resultObj=v*(Double.parseDouble(unit));
				}

				if(binary.charAt(0) == '1'){
					if(v>0) {
						resultObj = resultObj * -1;
					}
				}
				result.put(key,resultObj);
			}else { //U16 U32  无符号位数字
				int code=BinaryToHexUtils.hexToDecimal(dataBinary);//16进制转10进制
				if(!StringUtils.isEmpty(unit)){
					double resultObj;
					if(isInteger(unit)){
						resultObj= code*(Integer.parseInt(unit));
					}else {
						resultObj=code*(Double.parseDouble(unit));
					}
					result.put(key,resultObj);
				}
				result.put(key,(double)code);
			}
		}
	}


	/**
	 * 将字段映射成属性
	 * */
	public static String toCamelCase(String param) {
		if (param == null || param.trim().isEmpty()) {
			return "";
		}
		int len = param.length();
		StringBuilder sb = new StringBuilder(len);
		boolean tmp=false;
		for (int i = 0; i < len; i++) {
			char c = param.charAt(i);
			if(i==0){
				sb.append(Character.toLowerCase(c));
			}else {
				if(tmp){
					sb.append(Character.toUpperCase(c));
					tmp=false;
				}else {
					if (Character.isSpaceChar(c)) { //是否包含空格
						tmp = true;
					} else {
						sb.append(c);
					}
				}
			}

		}
		return sb.toString();
	}

	/**
	 * 将驼峰转为下划线
	 * */
	public static String convertToUnderscore(String input) {
		StringBuilder result = new StringBuilder();
		boolean isFirstChar = true;

		for (char c : input.toCharArray()) {
			if (Character.isUpperCase(c)) {
				if (!isFirstChar) {
					result.append('_');
				}
				result.append(Character.toLowerCase(c));
			} else {
				result.append(c);
			}

			isFirstChar = false;
		}

		return result.toString();
	}




	/**
	 * 判断是否是整形
	 * */
	public static boolean isInteger(String str) {
		try {
			Integer.parseInt(str);
			return true;
		} catch (NumberFormatException e) {
			return false;
		}
	}

	/**
	 * 判断是否是double类型
	 * */
	public static boolean isDouble(String str) {
		try {
			Double.parseDouble(str);
			return true;
		} catch (NumberFormatException e) {
			return false;
		}
	}

}
