/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.permissions.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.permissions.entity.AgentUnauthorizedUserEntity;
import org.skyworth.ess.permissions.vo.AgentUnauthorizedUserVO;
import java.util.Objects;

/**
 * 站点挂的代理商下，无权限操作的用户id 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
public class AgentUnauthorizedUserWrapper extends BaseEntityWrapper<AgentUnauthorizedUserEntity, AgentUnauthorizedUserVO>  {

	public static AgentUnauthorizedUserWrapper build() {
		return new AgentUnauthorizedUserWrapper();
 	}

	@Override
	public AgentUnauthorizedUserVO entityVO(AgentUnauthorizedUserEntity agentUnauthorizedUser) {
		AgentUnauthorizedUserVO agentUnauthorizedUserVO = Objects.requireNonNull(BeanUtil.copy(agentUnauthorizedUser, AgentUnauthorizedUserVO.class));

		//User createUser = UserCache.getUser(agentUnauthorizedUser.getCreateUser());
		//User updateUser = UserCache.getUser(agentUnauthorizedUser.getUpdateUser());
		//agentUnauthorizedUserVO.setCreateUserName(createUser.getName());
		//agentUnauthorizedUserVO.setUpdateUserName(updateUser.getName());

		return agentUnauthorizedUserVO;
	}


}
