package org.skyworth.ess.app.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AppReportHeaderVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 当日光伏发电量 1027
	 */
	private String todayEnergy;
	/**
	 * 原始当日光伏发电量，未转换，不带单位  1027  battery_current_status
	 */
	private BigDecimal originalTodayEnergy;
	/**
	 * 光伏总发电量 1021  app展示： from pv total
	 */
	private String totalEnergy;
//    /**
//     * 当日电池储电量 200b
//     */
//    private BigDecimal batteryDailyChargeEnergy;
//    /**
//     * 电池总储电量 200d
//     */
//    private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 * 日报表
	 */
	private AppReportDetailVO dailyReport;
	/**
	 * 月报表
	 */
	private AppReportDetailVO monthlyReport;
	/**
	 * 年报表
	 */
	private AppReportDetailVO annualReport;
	/**
	 * 当日负载用电量 = 光伏发电量-（电池储电量+并入电网的电量）
	 */
//    private BigDecimal batteryDailyConsumptionEnergy;
	/**
	 * 总负载用电量
	 */
//    private BigDecimal batteryTotalConsumptionEnergy;
	/**
	 * 年报表
	 */
	private AppReportDetailVO weekReport;
	/**
	 * 电池日放电量 200F
	 */
	private String batteryTodayDischargeEnergy;
	/**
	 * 电池总放电量 2011
	 */
	private String batteryAccumulatedDischargeEnergy;
	/**
	 * 电网今日输入 1332
	 */
	private String todayImportEnergy;
	/**
	 * 电网今日输入 1332 原始数据  device_current_status
	 */
	private BigDecimal originalTodayImportEnergy;
	/**
	 * 电网总输入 1306   accumulated_energy_of_positive
	 */
	private String totalImportEnergy;

	private String hybridWorkMode;
	/**
	 * From PV  Today Energy 0x1027  饼图  天的数据来源于battery_status  todayEnergy
	 */
	private String fromPv;
	/**
	 * Today import Energy 0x1332  饼图  天的数据来源于battery_status  todayImportEnergy
	 */
	private String fromGrid;
	/**
	 * 原始当日电网输出，未转换，不带单位  1334  馈电量  device_current_status
	 */
	private BigDecimal originalTodayExportEnergy;
	/**
	 * 当日电网输出，1334
	 */
	private String todayExportEnergy;
	private BigDecimal fromPvRatio;
	private BigDecimal fromGridRatio;
	private String pvGridTotal;
	// 3.29版本
	// 发电量Today Energy 0x1027-馈电量0x1334 如果得数为负数，则不用减。
	private String selfConsumed;
	// 馈电量0x1334 如果左边【Self-Consumed】的得数为负数，则馈电量归零
	private String fedToGrid;
	// Today load Energy 0x1336 + Today energy to Backup 0x1360+Battery today charge energy 0x200B - Today import Energy 0x1332
	private String selfSufficiency;
	private BigDecimal selfConsumedRatio;
	private BigDecimal fedToGridRatio;
	// selfConsumed + fedToGrid
	private String selfConsumedAndFedToGridTotal;
	private BigDecimal selfSufficiencyRatio;
	// selfConsumed + fromGrid
	private String selfSufficiencyAndFromGridTotal;
	// 1336 原始值  device_current_status
	private BigDecimal originalTodayLoadEnergy;
	// 1360 原始值 device_current_status
	private BigDecimal originalDailyEnergyToEps;
	// 200B 原始值  device_current_status
	private BigDecimal originalBatteryDailyChargeEnergy;
	//  1514-1515   不取 0x1510~0x1511 energy_today_of_ac_couple_kwh
	private String otherPvToday;
	// 0x1512~0x1513 energy_total_of_ac_couple
	private String otherPvTotal;

	// begin 并机
	// 13E4 from battery
	String batteryDailyDischargeEnergyParallel;
	// 13E6
	String batteryAccumulatedDischargeEnergyParallel;
	// 13D8 PV当日发电 from pv/today
	String pvlDailyGeneratingEnergySum;
	// 13DA pv累计发电
	String pvlAccumulatedEnergySum;
	// 1332 电网当日能量  和单机一样  字段 todayImportEnergy;  from grid
	// 1306 Accumulated energy of positive  字段 totalImportEnergy
	// end 并机

}
