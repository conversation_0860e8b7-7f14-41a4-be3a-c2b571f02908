/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.aspect;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.skyworth.ess.constant.SourceEnum;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.service.IAdvancedSettingsOperationLogService;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Date;
import java.util.Map;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-11-07 17:48
 **/

@Data
@Aspect
@Component
@Slf4j
public class AdvancedSettingsOperationLogAspect {
	/**
	 * 引入日志Service，用于存储数据进数据库
	 */
	private final IAdvancedSettingsOperationLogService operationLogService;
	private static final String PLANT_ID = "plantId";
	private static final String DEVICE_SERIAL_NUMBER = "deviceSerialNumber";
	private static final String SET_UP_ITEMS = "setupItems";
	private static final String DEFINITION_DESC = "definitionDesc";
	private static final String DATA_DESC = "dataDesc";
	private final TimeZoneDeviceService timeZoneDeviceService;

	/**
	 * 配置切入点
	 */
	@Pointcut("@annotation(org.skyworth.ess.aspect.AdvancedSettingsOperationLog)")
	public void logPointCut() {
	}

	/**
	 * 处理完请求后执行此处代码
	 *
	 * @param joinPoint 切点
	 */
	@AfterReturning(pointcut = "@annotation(advancedSettingsOperationLog)", returning = "jsonResult")
	public void doAfterReturning(JoinPoint joinPoint, AdvancedSettingsOperationLog advancedSettingsOperationLog, Object jsonResult) {
		handleLog(joinPoint, advancedSettingsOperationLog, null, jsonResult);
	}

	/**
	 * 如果处理请求时出现异常，在抛出异常后执行此处代码
	 *
	 * @param joinPoint 切点
	 * @param e         异常
	 */
	@AfterThrowing(value = "@annotation(advancedSettingsOperationLog)", throwing = "e")
	public void doAfterThrowing(JoinPoint joinPoint, AdvancedSettingsOperationLog advancedSettingsOperationLog, Exception e) {
		handleLog(joinPoint, advancedSettingsOperationLog, e, null);
	}

	/**
	 * 处理日志数据
	 *
	 * @param joinPoint                    j
	 * @param advancedSettingsOperationLog log
	 * @param e                            e
	 * @param jsonResult                   j
	 */
	protected void handleLog(final JoinPoint joinPoint, AdvancedSettingsOperationLog advancedSettingsOperationLog, final Exception e, Object jsonResult) {
		try {
			// *========获取request请求=========*//
			ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
			assert requestAttributes != null;
			HttpServletRequest request = requestAttributes.getRequest();
			Object[] args = joinPoint.getArgs();
			if (args != null) {
				for (Object o : args) {
					if (o != null && !isFilterObject(o)) {
						try {
							JSONObject jsonObject = (JSONObject) JSON.toJSON(o);
							if (jsonObject.containsKey(SET_UP_ITEMS)) {
								//SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								//DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
								//TimeZoneDevice timeZoneDevice = timeZoneDeviceService.getOne(Condition.getQueryWrapper(new TimeZoneDevice(jsonObject.getString("plantId"))));
								// 遍历设置项，每个设置项作为一条操作记录插入记录表
								JSONArray jsonArray = jsonObject.getJSONArray(SET_UP_ITEMS);
								for (Object object : jsonArray) {
									// 将Object对象转换为JSONObject类型
									JSONObject itemJSONObject = JSON.parseObject(JSON.toJSONString(object));
									if (!itemJSONObject.containsKey("definition")) {
										continue;
									}
									AdvancedSettingsOperationLogEntity advancedSettingsOperationLogEntity = new AdvancedSettingsOperationLogEntity();
									advancedSettingsOperationLogEntity.setSource(advancedSettingsOperationLog.type().name());
									// 参数设置
									setRequestValue(joinPoint, request, advancedSettingsOperationLogEntity, itemJSONObject);
									if (e != null) {
										advancedSettingsOperationLogEntity.setStatus(0);
										int length = e.getMessage().length();
										advancedSettingsOperationLogEntity.setResponseBody(e.getMessage().substring(0, Math.min(length, 512)));
									} else {
										JSONObject j = JSON.parseObject(JSON.toJSONString(jsonResult));
										int code = j.getInteger("code");
										if (ResultCode.SUCCESS.getCode() != code) {
											advancedSettingsOperationLogEntity.setStatus(0);
										}
										String responseJson = j.toJSONString();
										int length = responseJson.length();
										advancedSettingsOperationLogEntity.setResponseBody(responseJson.substring(0, Math.min(length, 512)));
									}
									LocalDateTime localDateTime = LocalDateTime.now();
									String currentUtcTimeZone = DateUtil.getCurrentUtcTimeZone();
									Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
									advancedSettingsOperationLogEntity.setCreateTime(date);
									advancedSettingsOperationLogEntity.setUpdateTime(date);
									advancedSettingsOperationLogEntity.setTimeZone(currentUtcTimeZone);

									if (advancedSettingsOperationLogEntity.getSource().equals(SourceEnum.OTHER.name())) {
										advancedSettingsOperationLogEntity.setCreateUserAccount("System");
									}else{
										BladeUser user = AuthUtil.getUser();
										advancedSettingsOperationLogEntity.setCreateUserAccount(user.getAccount());
									}

									// 将处理好的日至对象存储进数据库
									operationLogService.save(advancedSettingsOperationLogEntity);
								}
							}
						} catch (Exception e1) {
							log.error(e1.getMessage());
						}
					}
				}
			}
		} catch (Exception exp) {
			// 记录本地异常日志
			log.error("==前置通知异常==");
			log.error("异常信息:{}", exp.getMessage());
		}
	}

	public static void main(String[] args) {
		System.out.println(SourceEnum.OTHER.name());
	}


	/**
	 * 设置入参
	 *
	 * @param joinPoint joinPoint
	 * @param request   request
	 * @param logEntity 入参
	 * <AUTHOR>
	 * @since 2023/11/8 10:29
	 **/
	private void setRequestValue(JoinPoint joinPoint, HttpServletRequest request, AdvancedSettingsOperationLogEntity logEntity,JSONObject itemJSONObject) {
		String requestMethod = request.getMethod();
		if (RequestMethod.PUT.name().equals(requestMethod) || RequestMethod.POST.name().equals(requestMethod)) {
			argsArrayToString(joinPoint.getArgs(), logEntity,itemJSONObject);
		} else {
			Map<?, ?> paramsMap = (Map<?, ?>) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
			if (paramsMap != null && !paramsMap.isEmpty()) {
				String parasString = paramsMap.toString();
				logEntity.setRequestBody(parasString.substring(0, Math.min(parasString.length(), 512)));
			}
		}
	}


	/**
	 * 参数解析
	 *
	 * @param paramsArray 参数数组
	 */
	private void argsArrayToString(Object[] paramsArray, AdvancedSettingsOperationLogEntity logEntity,JSONObject itemJSONObject) {
		StringBuilder params = new StringBuilder();
		if (paramsArray != null) {
			for (Object o : paramsArray) {
				if (o != null && !isFilterObject(o)) {
					try {
						JSONObject jsonObject = (JSONObject) JSON.toJSON(o);
						// 设置plantId
						if (jsonObject.containsKey(PLANT_ID)) {
							logEntity.setPlantId(jsonObject.getLong(PLANT_ID));
						}
						// 设置deviceSerialNumber
						if (jsonObject.containsKey(DEVICE_SERIAL_NUMBER)) {
							logEntity.setDeviceSerialNumber(jsonObject.getString(DEVICE_SERIAL_NUMBER));
						}
						if (jsonObject.containsKey(SET_UP_ITEMS)) {
							parsingLogContent(itemJSONObject, params);
						}
					} catch (Exception e) {
						log.error(e.getMessage());
					}
				}
			}
		}
		logEntity.setRequestBody(StringUtils.substring(params.toString().trim(), 0, Math.min(params.length() - 2, 512)));
	}

	/**
	 * @param jsonObject 单个指令
	 * @param params    入参
	 * <AUTHOR>
	 * @since 2023/11/24 11:17
	 **/
	private void parsingLogContent(JSONObject jsonObject, StringBuilder params) {
//		jsonArray.forEach(a -> {
//			JSONObject jsonObject = (JSONObject) JSON.toJSON(a);
		if (ObjectUtil.isNotNull(jsonObject.getString(DEFINITION_DESC)) && ObjectUtil.isNotNull(jsonObject.getString(DATA_DESC))) {
			// 属性描述
			if (jsonObject.containsKey(DEFINITION_DESC)) {
				params.append(jsonObject.getString(DEFINITION_DESC)).append(" -> ");
			}
			// 值描述
			if (jsonObject.containsKey(DATA_DESC)) {
				params.append(jsonObject.getString(DATA_DESC)).append("\\n");
			}
		}
//		});
	}

	@SuppressWarnings("rawtypes")
	public boolean isFilterObject(final Object o) {
		Class<?> clazz = o.getClass();
		if (clazz.isArray()) {
			return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
		} else if (Collection.class.isAssignableFrom(clazz)) {
			Collection collection = (Collection) o;
			for (Object value : collection) {
				return value instanceof MultipartFile;
			}
		} else if (Map.class.isAssignableFrom(clazz)) {
			Map map = (Map) o;
			for (Object value : map.entrySet()) {
				Map.Entry entry = (Map.Entry) value;
				return entry.getValue() instanceof MultipartFile;
			}
		}
		return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
			|| o instanceof BindingResult;
	}

}
