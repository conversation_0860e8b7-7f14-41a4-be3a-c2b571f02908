/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 设备软件版本信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceSoftwareVersionInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * SN号
	 */
	@ColumnWidth(20)
	@ExcelProperty("SN号")
	private String serialNumber;
	/**
	 * 当前版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("当前版本")
	private String currentVersionNumber;
	/**
	 * 大类型（device逆变器、battery电池）
	 */
	@ColumnWidth(20)
	@ExcelProperty("大类型（device逆变器、battery电池）")
	private String bigType;
	/**
	 * 小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）
	 */
	@ColumnWidth(20)
	@ExcelProperty("小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）")
	private String smallType;
	/**
	 * 升级完成时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("升级完成时间")
	private Date upgradeCompletionTime;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
