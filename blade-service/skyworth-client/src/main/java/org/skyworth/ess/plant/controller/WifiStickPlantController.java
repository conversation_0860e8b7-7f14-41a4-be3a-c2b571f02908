/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.excel.WifiStickPlantExcel;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.plant.vo.WifiStickPlantVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * wifi棒对应站点表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-WifiStickPlant/WifiStickPlant")
@Api(value = "wifi棒对应站点表", tags = "wifi棒对应站点表接口")
public class WifiStickPlantController extends BladeController {

	private final IWifiStickPlantService wifiStickPlantService;

	/**
	 * wifi棒对应站点表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入WifiStickPlant")
	public R<IPage<WifiStickPlantVO>> page(WifiStickPlantVO wifiStickPlant, Query query) {
		IPage<WifiStickPlantVO> pages = wifiStickPlantService.selectWifiStickPlantPage(Condition.getPage(query), wifiStickPlant);
		return R.data(pages);
	}

	/**
	 * wifi棒对应站点表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入WifiStickPlant")
	public R save(@Valid @RequestBody WifiStickPlantEntity wifiStickPlant) {
		return R.status(wifiStickPlantService.save(wifiStickPlant));
	}

	/**
	 * wifi棒对应站点表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入WifiStickPlant")
	public R update(@Valid @RequestBody WifiStickPlantEntity wifiStickPlant) {
		return R.status(wifiStickPlantService.updateById(wifiStickPlant));
	}

	/**
	 * wifi棒对应站点表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入WifiStickPlant")
	public R submit(@Valid @RequestBody WifiStickPlantEntity wifiStickPlant) {
		return R.status(wifiStickPlantService.saveOrUpdate(wifiStickPlant));
	}

	/**
	 * wifi棒对应站点表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(wifiStickPlantService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-WifiStickPlant")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入WifiStickPlant")
	public void exportWifiStickPlant(@ApiIgnore @RequestParam Map<String, Object> wifiStickPlant, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<WifiStickPlantEntity> queryWrapper = Condition.getQueryWrapper(wifiStickPlant, WifiStickPlantEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(WifiStickPlant::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(WifiStickPlantEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<WifiStickPlantExcel> list = wifiStickPlantService.exportWifiStickPlant(queryWrapper);
//		ExcelUtil.export(response, "wifi棒对应站点表数据" + DateUtil.time(), "wifi棒对应站点表数据表", list, WifiStickPlantExcel.class);
	}

}
