<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.surveyassignment.orderrelateduser.mapper.OrderRelatedUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="orderRelatedUserResultMap" type="org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="node_type" property="nodeType"/>
        <result column="user_type" property="userType"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="img_biz_key" property="imgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectOrderRelatedUserPage" resultMap="orderRelatedUserResultMap">
        select * from business_order_related_user where is_deleted = 0
    </select>


    <select id="exportOrderRelatedUser" resultType="org.skyworth.ess.surveyassignment.orderrelateduser.excel.OrderRelatedUserExcel">
        SELECT * FROM business_order_related_user ${ew.customSqlSegment}
    </select>

</mapper>
