/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.vo;


import lombok.Data;

import java.io.Serializable;


/**
 * 物理地址映射属性名称 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
public class UserRegistrationVO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * App用户注册数
	 */
	private Integer numberOfAppUserRegistrations = 0;
	/**
	 * 分组唯一键
	 */
	private String groupUniqueKey;

}
