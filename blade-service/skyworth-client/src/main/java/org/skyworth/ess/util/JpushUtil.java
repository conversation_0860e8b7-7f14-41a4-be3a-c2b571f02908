package org.skyworth.ess.util;

import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import com.google.gson.JsonObject;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 极光推送
 *
 * <AUTHOR>
@Component
@RequiredArgsConstructor
public class JpushUtil {

	private static final Logger log = LoggerFactory.getLogger(JpushUtil.class);

	@Value("${blade.jPush.appKey}")
	private String appKey;

	@Value("${blade.jPush.masterSecret}")
	private String masterSecret;

	/**
	 * 极光推送
	 * @param alias 别名
	 */
	public  void sendPush(String alert,String title,JsonObject androidObj,String alias,Map<String,String> iosObj){
	   if(ValidationUtil.isNotEmpty(appKey)&&ValidationUtil.isNotEmpty(masterSecret)){
		   // 初始化JPushClient
		   JPushClient jpushClient = new JPushClient(masterSecret, appKey);
		   // 创建推送payload，使用别名来定位特定的用户
		   PushPayload payload = PushPayload.newBuilder()
			   .setPlatform(Platform.all())
			   .setAudience(Audience.alias(alias))
			   .setNotification(
				   Notification.newBuilder().setAlert(alert)
					   .addPlatformNotification(AndroidNotification.newBuilder().setTitle(title).setIntent(androidObj).build())
					   .addPlatformNotification(IosNotification.newBuilder().addExtras(iosObj).build())
					   .build())
			   .build();

		   try {
			   // 发送推送
			   PushResult result = jpushClient.sendPush(payload);
			   log.info("Push result - " + result.isResultOK());
		   } catch (APIConnectionException | APIRequestException e) {
			   log.error(e.getMessage());
		   }
	   }
	}

	public static void main(String[] args) {
		// 设置你的APP key和master secret
		String appKey = "e62046a593c215fddca52499";
		String masterSecret = "5d65ca4b1b899d462fa67ff8";
		// 创建JPushClient实例
		JPushClient jpushClient = new JPushClient(masterSecret, appKey);

		JsonObject obj=new JsonObject();
		obj.addProperty("url","intent:#Intent;action=android.intent.action.pushaction;component=com.skyworth.energystorage/com.skyworth.module_main.ui.activity.PlantActivity;S.plant_id=1777502163771527170;S.plant_name=10kw;S.deviceSerialNumber=CVGJSTSJA00000234;S.device_model=CVGJST-10kW3P;end");
		Message message= Message.newBuilder().setMsgContent("内容").setContentType("text").build();
		Map<String,String> map=new HashMap<>();
		map.put("plantId","1776856126772482050");
		map.put("plantName","10kw");
		map.put("deviceSerialNumber","CVGJSTSJA00000234");
		map.put("deviceModelType","CVGJST-10kW3P");
		// 创建PushPayload对象
		PushPayload pushPayload = PushPayload.newBuilder()
			.setPlatform(Platform.all())
			.setAudience(Audience.alias("1762366274416939010"))
			.setNotification(Notification.newBuilder().setAlert("测试告警弹窗")
				.addPlatformNotification(
					AndroidNotification.newBuilder().setTitle("EnerConsole").setIntent(obj).build())
				.addPlatformNotification(IosNotification.newBuilder().setAlert("测试告警弹窗").addExtras(map).build())
				.build())

			.build();

		try {
			// 发送推送
			PushResult result = jpushClient.sendPush(pushPayload);
			System.out.println("Got result - " + result);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
