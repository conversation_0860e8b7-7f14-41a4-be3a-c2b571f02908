/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.app.vo.AppBatteryExitFactoryInfoVO;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.excel.BatteryExitFactoryInfoDeleteImportExcel;
import org.skyworth.ess.battery.excel.BatteryExitFactoryInfoExcel;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.springblade.core.mp.base.BaseService;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 电池出厂信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface IBatteryExitFactoryInfoService extends BaseService<BatteryExitFactoryInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param BatteryExitFactoryInfo
	 * @return
	 */
	IPage<BatteryExitFactoryInfoVO> selectBatteryExitFactoryInfoPage(IPage<BatteryExitFactoryInfoVO> page, BatteryExitFactoryInfoVO BatteryExitFactoryInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<BatteryExitFactoryInfoExcel> exportBatteryExitFactoryInfo(Wrapper<BatteryExitFactoryInfoEntity> queryWrapper);

	List<AppBatteryExitFactoryInfoVO> queryAppBatteryExitFactoryInfo(Long plantId,String deviceSerialNumber);

	boolean deleteLogicBatteryExitFactory(List<Long> ids);

	String importAddExcel(List<BatteryExitFactoryInfoExcel> data, Boolean isCovered);

	String importModifyExcel(List<BatteryExitFactoryInfoExcel> data, Boolean isCovered);
	int batchUpdate(List<String> serialNumberList, int batteryUseStatus);

	BigDecimal queryTotalInstalledPower();

	List<BatteryExitFactoryInfoVO> deviceInformation(QueryCondition queryCondition);

	List<BatteryExitFactoryInfoVO> deviceInformationIsDelete(QueryCondition queryCondition);

	int updateForScanBattery(String serialNumber, int batteryUseStatus);

	BatteryExitFactoryInfoEntity getModelAndProtocolBySn(BatteryExitFactoryInfoEntity batteryExitFactoryInfoEntity);

	List<BatteryExitFactoryInfoEntity> queryByBatterySerialNumbers(List<String> deviceSerialNumbers);

	long count(String serialNumber) ;

    boolean addBatteryExitFactoryInfo(@Valid BatteryExitFactoryInfoEntity batteryExitFactoryInfo);

	String importDeleteExcel(List<BatteryExitFactoryInfoDeleteImportExcel> data, Boolean isCovered);
}
