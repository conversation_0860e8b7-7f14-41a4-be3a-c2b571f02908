package org.skyworth.ess.plant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PlantChangeUserVO{

	@ApiModelProperty(value = "站点id")
	private Long plantId;

//	@ApiModelProperty(value = "更新人账户")
//	private String updateAccountName;

	@ApiModelProperty(value = "更新人电话")
	private String updatePhoneNumber;

	@ApiModelProperty(value = "电话验证码")
	private String phoneVerifyCode;

	@ApiModelProperty(value = "更新人邮箱")
	private String updateEmail;

	@ApiModelProperty(value = "邮箱验证码")
	private String emailVerifyCode;

	@ApiModelProperty(value = "区号")
	private String phoneDiallingCode;

	@ApiModelProperty(value = "密码")
	private String password;

//	@ApiModelProperty(value = "是否确认更改") //信息与数据库不一致是否更改，默认为false。第一次提交抛出异常后会提示是否允许修改，允许的话再次请求传进来的就是true
//	private Boolean confirmSign;
}
