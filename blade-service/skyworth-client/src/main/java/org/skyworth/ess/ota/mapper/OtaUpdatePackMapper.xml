<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.ota.mapper.OtaUpdatePackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="otaUpdatePackResultMap" type="org.skyworth.ess.ota.entity.OtaUpdatePackEntity">
        <result column="id" property="id"/>
        <result column="big_type" property="bigType"/>
        <result column="small_type" property="smallType"/>
        <result column="version_number" property="versionNumber"/>
        <result column="company" property="company"/>
        <result column="pack_cdn_url" property="packCdnUrl"/>
        <result column="is_new_version" property="isNewVersion"/>
        <result column="whether_to_force_upgrade" property="whetherToForceUpgrade"/>
        <result column="check_sum" property="checkSum"/>
        <result column="business_id" property="businessId"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectOtaUpdatePackPage" resultMap="otaUpdatePackResultMap">
        select * from ota_update_pack where is_deleted = 0
    </select>


    <select id="exportOtaUpdatePack" resultType="org.skyworth.ess.ota.excel.OtaUpdatePackExcel">
        SELECT * FROM ota_update_pack ${ew.customSqlSegment}
    </select>

</mapper>
