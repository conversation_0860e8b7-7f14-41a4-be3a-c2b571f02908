/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.operationlog.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.vo.AdvancedSettingsOperationLogVO;

import java.util.Objects;

/**
 * 高级设置操作日志 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public class AdvancedSettingsOperationLogWrapper extends BaseEntityWrapper<AdvancedSettingsOperationLogEntity, AdvancedSettingsOperationLogVO> {

	public static AdvancedSettingsOperationLogWrapper build() {
		return new AdvancedSettingsOperationLogWrapper();
	}

	@Override
	public AdvancedSettingsOperationLogVO entityVO(AdvancedSettingsOperationLogEntity advancedSettingsOperationLog) {
		AdvancedSettingsOperationLogVO advancedSettingsOperationLogVO = Objects.requireNonNull(BeanUtil.copy(advancedSettingsOperationLog, AdvancedSettingsOperationLogVO.class));

		//User createUser = UserCache.getUser(advancedSettingsOperationLog.getCreateUser());
		//User updateUser = UserCache.getUser(advancedSettingsOperationLog.getUpdateUser());
		//advancedSettingsOperationLogVO.setCreateUserName(createUser.getName());
		//advancedSettingsOperationLogVO.setUpdateUserName(updateUser.getName());

		return advancedSettingsOperationLogVO;
	}


}
