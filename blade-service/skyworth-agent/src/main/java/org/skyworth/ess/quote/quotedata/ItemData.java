package org.skyworth.ess.quote.quotedata;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ItemData {

    private Integer sort;
    private String item;
    private String description;
    private Long quantity;
    private BigDecimal unitPrice;
    private BigDecimal amount;

    public ItemData() {
    }

    public ItemData(Integer sort, String item, String description, Long quantity, BigDecimal unitPrice, BigDecimal amount) {
        this.sort = sort;
        this.item = item;
        this.description = description;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.amount = amount;
    }

}
