package org.skyworth.ess.company.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 代理商整体完整信息
 *
 * @description:代理商整体信息
 * @author: SDT50545
 * @since: 2023-11-09 17:34
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "AgentEntity对象", description = "代理商信息")
public class AgentEntity extends SkyWorthFileEntity {

	@ApiModelProperty(value = "代理商公司信息")
	@Valid
	@NotNull(message = "{agent.companyInfo.notNull}")
	private AgentCompanyInfoEntity companyInfo;

	@ApiModelProperty(value = "代理商附件信息")
	private List<AgentFileInfoEntity> fileInfoEntityList;

	@ApiModelProperty(value = "代理商下的施工人员")
	private List<AgentUserInfoEntity> constructorList;

	@ApiModelProperty(value = "代理商下的电工")
	private List<AgentUserInfoEntity> electricianList;

	@ApiModelProperty(value = "代理商下的交付经理")
	private List<AgentUserInfoEntity> rolloutManagerDistributorList;

}
