/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.excel.OtaUpdatePackExcel;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * OTA升级包 服务类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface IOtaUpdatePackService extends BaseService<OtaUpdatePackEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param queryWrapper
	 * @return
	 */
	IPage<OtaUpdatePackEntity> pageList(IPage<OtaUpdatePackEntity> page, QueryWrapper<OtaUpdatePackEntity> queryWrapper);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<OtaUpdatePackExcel> exportOtaUpdatePack(Wrapper<OtaUpdatePackEntity> queryWrapper);

	/**
	 * 保存ota数据并处理删除的附件信息
	 *
	 * @param otaUpdatePackEntity 入参
	 * @return void
	 * <AUTHOR>
	 * @since 2023/9/28 14:26
	 **/
	Boolean saveAndDealWithAttachment(OtaUpdatePackEntity otaUpdatePackEntity);

	/**
	 * 修改操作
	 *
	 * @param otaUpdatePackEntity 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/9/28 15:50
	 **/
	Boolean updateAndDealWithAttachment(OtaUpdatePackEntity otaUpdatePackEntity);

	/**
	 * 查询详情
	 *
	 * @param otaUpdatePack 入参
	 * @return OtaUpdatePackVO
	 * <AUTHOR>
	 * @since 2023/9/28 16:50
	 **/
	OtaUpdatePackVO detail(OtaUpdatePackEntity otaUpdatePack);
}
