/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;


import cn.hutool.json.JSONObject;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.constant.AgentUserTypeEnum;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.skyworth.ess.feign.IPotableClient;
import org.skyworth.ess.feign.client.IAgentClientBiz;
import org.skyworth.ess.plant.feign.IPlantClient;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.EmailCacheNamesEnum;
import org.springblade.common.constant.*;
import org.springblade.common.mail.SendMail;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.common.vo.UserBatchVO;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.BladeTenantProperties;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.system.cache.DictCache;
import org.springblade.system.cache.ParamCache;
import org.springblade.system.cache.SysCache;
import org.springblade.system.cache.UserCache;
import org.springblade.system.entity.*;
import org.springblade.system.enums.DictEnum;
import org.springblade.system.enums.UserEnum;
import org.springblade.system.excel.UserExcel;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.mapper.UserMapper;
import org.springblade.system.service.*;
import org.springblade.system.vo.UserInfoMappingVO;
import org.springblade.system.vo.UserInfoVO;
import org.springblade.system.vo.UserVO;
import org.springblade.system.wrapper.UserWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.springblade.common.constant.CommonConstant.DEFAULT_PARAM_PASSWORD;
import static org.springblade.core.cache.constant.CacheConstant.USER_CACHE;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserService {
	private static final String GUEST_NAME = "guest";


	private final IUserDeptService userDeptService;
	private final IUserOauthService userOauthService;
	private final ISysClient sysClient;
	private final BladeTenantProperties tenantProperties;
	private final BladeRedis bladeRedis;
	private final IRoleService roleService;
	private final SendMail sendMail;
	private final IDictBizClient dictBizClient;
	private final IAgentClientBiz iAgentClient;
	private final IUserAppService userAppServiceImpl;
	private final IPotableClient potableClient;
	private final IUserMappingService userMappingService;
	private final IDeptService deptService;
	private final IPlantClient iPlantClient;

	public static final String UPDATE_TYPE_EMAIL = "2";
	public static final String UPDATE_TYPE_PHONE = "1";

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(User user) {
		if (StringUtil.isBlank(user.getPassword())) {
			//-
			throw new ServiceException("the password can not empty");
		}
		if (Func.isNotEmpty(user.getPassword())) {
			String password = StringEscapeUtils.unescapeHtml4(user.getPassword());
			user.setPassword(DigestUtil.hex(password));
		}
		Long userCount;
		// 如果是后端分配用户，不用校验手机、邮箱验证码等信息
		if (CommonConstant.USER_FROM_ALLOCATION.equals(user.getUserFrom())) {
			// web页面分配用户
			userCount = this.allocationUserByWeb(user);

		} else if (CommonConstant.USER_FROM_REGISTER_WEB.equals(user.getUserFrom())) {
			// web页面注册[欧洲版注册，和手机版相同了]
			userCount = this.registerUser(user);
		} else {
			// 手动注册用户区分不同的注册场景校验格式，并设置tenantId
			user.setUserFrom("register");
			userCount = this.registerUser(user);
		}
		this.judgeMaxTenant(user.getTenantId());

		if (userCount > 0L && Func.isEmpty(user.getId()) && user.getRegisterType().equals(CommonConstant.REGISTER_PHONE)) {
			throw new BusinessException("system.user.phone.is.exits");
		}
		if (userCount > 0L && Func.isEmpty(user.getId()) && user.getRegisterType().equals(CommonConstant.REGISTER_EMAIL)) {
			throw new BusinessException("system.user.email.is.exits");
		}
		if (StringUtil.isNotBlank(user.getFirstName()) || StringUtil.isNotBlank(user.getLastName())) {
			user.setRealName(user.getFirstName().trim() + " " + user.getLastName().trim());
		}
		save(user);
		// 代理商保存部门
		submitUserDept(user);
		if (CommonConstant.PORTABLE_TENANT_ID.equals(user.getTenantId()) && CommonConstant.APP_TYPE_PORTABLE.equals(user.getAppType())) {
			UserApp userApp = new UserApp();
			userApp.setUserId(user.getId());
			userAppServiceImpl.saveUserApp(userApp);
		}
		// 同步代理商人员至设备端
		this.synchronizeUser4Register(user);
		return true;
	}

	private void synchronizeUser4Register(User user) {
		if (!CommonConstant.AGENT_TENANT_ID.equals(user.getTenantId()) && !CommonConstant.APP_TYPE_AGENT.equals(user.getAppType())) {
			log.info("submit user synchronizeUser not to client");
			return;
		}
		log.info("submit user synchronizeUser to client");
		User clientUserIgnoreTenant = this.getUserIgnoreTenant(user.getPhone(), user.getPhoneDiallingCode(), CommonConstant.CLIENT_TENANT_ID);
		List<String> clientRoleCode4Agent = RoleCodeEnum.getClientRoleCode4Agent();
		clientRoleCode4Agent.add(RoleCodeEnum.CLIENT_COMMON_ROLE.getRoleCode());
		List<Role> roleIgnoreTenant = roleService.getRoleIgnoreTenant(clientRoleCode4Agent, CommonConstant.CLIENT_TENANT_ID);
		List<Role> roleList = new ArrayList<>();
		if (StringUtil.isNotBlank(user.getRoleCode())) {
			String clientRoleCodeByAgentRoleCode = RoleCodeEnum.getClientRoleCodeByAgentRoleCode(user.getRoleCode());
			roleList = roleIgnoreTenant.stream().filter(p -> p.getRoleCode().equals(clientRoleCodeByAgentRoleCode)).collect(Collectors.toList());
		} else {
			// 界面分配的账号没有角色编码
			Role roleById = roleService.getById(user.getRoleId());
			String clientRoleCodeByAgentRoleCode = RoleCodeEnum.getClientRoleCodeByAgentRoleCode(roleById.getRoleCode());
			roleList = roleIgnoreTenant.stream().filter(p -> p.getRoleCode().equals(clientRoleCodeByAgentRoleCode)).collect(Collectors.toList());
		}

		// 如果设备中存在此账号，则更新 原有密码、账号、邮箱、名字、追加epc角色、追加部门 信息
		if (Objects.nonNull(clientUserIgnoreTenant)) {
			log.info("submit user client had user");
			String sourceRoleIds = clientUserIgnoreTenant.getRoleId();
			Set<String> sourceRoleIdSet = new HashSet<>();
			if (StringUtil.isNotBlank(sourceRoleIds)) {
				String[] split = sourceRoleIds.split(",");
				sourceRoleIdSet = Arrays.stream(split)
					.collect(Collectors.toSet());
			}
			if (CollectionUtil.isNotEmpty(roleList)) {
				sourceRoleIdSet.add(String.valueOf(roleList.get(BizConstant.NUMBER_ZERO).getId()));
			}
			String deptIds = clientUserIgnoreTenant.getDeptId();
			Set<String> userDept = new HashSet<>();
			String agentDeptId = user.getDeptId();
			Set<String> clientUserDept = new HashSet<>();
			if (StringUtil.isNotBlank(agentDeptId)) {
				// web端注册 可能存在多个部门id
				String[] agentDeptIdArray = agentDeptId.split(",");
				Set<String> agentDept = Arrays.stream(agentDeptIdArray)
					.collect(Collectors.toSet());
				clientUserDept.addAll(agentDept);
			}
			if (StringUtil.isNotBlank(deptIds)) {
				String[] userDeptIdArray = deptIds.split(",");
				userDept = Arrays.stream(userDeptIdArray)
					.collect(Collectors.toSet());
				clientUserDept.addAll(userDept);
			}
			User updateUser = this.getUpdateUser(user, clientUserIgnoreTenant.getId(), Joiner.on(",").join(sourceRoleIdSet), Joiner.on(",").join(clientUserDept));
			baseMapper.updateUserIgnoreTenant(updateUser);
			UserMappingEntity insetUserMappingEntity = new UserMappingEntity();
			insetUserMappingEntity.setSourceUserId(user.getId());
			insetUserMappingEntity.setMappingUserId(clientUserIgnoreTenant.getId());
			insetUserMappingEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			insetUserMappingEntity.setCreateUser(user.getId());
			userMappingService.save(insetUserMappingEntity);
			this.saveAnget4manager(user);
			// 标识人员修改过信息，需重新登录
			userTokenLogOutById(clientUserIgnoreTenant.getId());
		} else {
			log.info("submit user client had no user");
			User insertUser = new User();
			BeanUtil.copy(user, insertUser);
			List<String> clientCommonRole = roleIgnoreTenant.stream().filter(p -> RoleCodeEnum.CLIENT_COMMON_ROLE.getRoleCode().equals(p.getRoleCode()))
				.map(p -> String.valueOf(p.getId())).collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(roleList)) {
				clientCommonRole.add(String.valueOf(roleList.get(BizConstant.NUMBER_ZERO).getId()));
			}
			String roleIds = Joiner.on(",").join(clientCommonRole);
			insertUser.setRoleId(roleIds);
			insertUser.setId(null);
			insertUser.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			insertUser.setUserFrom("synchronizeAgent");
			this.save(insertUser);
			UserMappingEntity insetUserMappingEntity = new UserMappingEntity();
			insetUserMappingEntity.setSourceUserId(user.getId());
			insetUserMappingEntity.setMappingUserId(insertUser.getId());
			insetUserMappingEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			insetUserMappingEntity.setCreateUser(user.getId());
			userMappingService.save(insetUserMappingEntity);
			// 写入代理商人员表
			this.saveAnget4manager(user);
		}

	}

	private void saveAnget4manager(User user) {
		// 写入代理商人员表
		if (RoleCodeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode().equals(user.getRoleCode())) {
			AgentUserInfoEntity insertAgentUserInfo = new AgentUserInfoEntity();
			insertAgentUserInfo.setUserType(AgentUserTypeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getCode());
			insertAgentUserInfo.setAgentId(user.getAgentId());
			insertAgentUserInfo.setUserId(user.getId());
			insertAgentUserInfo.setCreateUser(user.getId());
			insertAgentUserInfo.setTenantId(CommonConstant.AGENT_TENANT_ID);
			R<Boolean> booleanR = iAgentClient.saveAgentUserInfo(insertAgentUserInfo);
			Boolean data = booleanR.getData();
			log.info("data : {}", data);
		}
	}

	private User getUserIgnoreTenant(String phone, String phoneDiallingCode, String tenantId) {
		User queryUser = new User();
		queryUser.setTenantId(tenantId);
		queryUser.setPhone(phone);
		queryUser.setPhoneDiallingCode(phoneDiallingCode);
		return baseMapper.getUserIgnoreTenant(queryUser);
	}

	private User getUpdateUser(User sourceUser, Long targetUserId, String targetRoleIds, String targetDeptIds) {
		User updateUser = new User();
		updateUser.setId(targetUserId);
		updateUser.setPassword(sourceUser.getPassword());
		updateUser.setEmail(sourceUser.getEmail());
		updateUser.setRealName(sourceUser.getRealName());
		updateUser.setFirstName(sourceUser.getFirstName().trim());
		updateUser.setLastName(sourceUser.getLastName().trim());
		updateUser.setDeptId(targetDeptIds);
		updateUser.setSex(sourceUser.getSex());
		updateUser.setUserFrom(sourceUser.getUserFrom());
		updateUser.setAccount(sourceUser.getAccount());
		updateUser.setPhone(sourceUser.getPhone());
		updateUser.setPhoneDiallingCode(sourceUser.getPhoneDiallingCode());
		updateUser.setRoleId(targetRoleIds);
		updateUser.setName(sourceUser.getPhone());
		updateUser.setUpdateTime(new Date());
		return updateUser;
	}

	private Long registerUserByWeb(User user) {
		if (user.getRegisterType().equals(CommonConstant.REGISTER_PHONE)) {
			user.setAccount(this.phoneRegister(user, user.getVerificationCode()));
		} else {
			user.setAccount(this.emailRegister(user, user.getVerificationCode()));
		}

		if (CommonConstant.CLIENT_TENANT_ID.equals(user.getTenantId())) {
			String roleId = roleService.getRoleIds(CommonConstant.CLIENT_TENANT_ID, "client_user");
			user.setRoleId(roleId);
			user.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			user.setRealName(user.getFirstName().trim() + " " + user.getLastName().trim());
			user.setName(user.getAccount());
			log.info("user register by device web");
		}
		return this.getUserCount(user);
	}

	private Long allocationUserByWeb(User user) {
		Long userCount;
		String trimmed = user.getPhoneDiallingCode().trim();
		if (!trimmed.contains(CommonConstant.SIGN_ADD)) {
			trimmed = CommonConstant.SIGN_ADD + trimmed;
			user.setPhoneDiallingCode(trimmed);
		}
		userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, user.getTenantId()).eq(User::getAccount, user.getAccount()));
		Long phUserCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, user.getTenantId()).eq(User::getPhone, user.getPhone())
			.eq(User::getPhoneDiallingCode, user.getPhoneDiallingCode()));
		Long emailUserCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, user.getTenantId()).eq(User::getEmail, user.getEmail()));
		if (phUserCount > 0L && Func.isEmpty(user.getId())) {
			throw new BusinessException("system.user.phone.is.exits");
		}
		if (userCount > 0L && Func.isEmpty(user.getId())) {
			throw new BusinessException("system.user.account.is.exits");
		}
		if (Func.isNotEmpty(user.getEmail()) && emailUserCount > 0L && Func.isEmpty(user.getId())) {
			throw new BusinessException("system.user.email.is.exits");
		}
		return userCount;
	}

	private void judgeMaxTenant(String tenantId) {
		Tenant tenant = SysCache.getTenant(tenantId);
		if (Func.isNotEmpty(tenant)) {
			Integer accountNumber = tenant.getAccountNumber();
			if (tenantProperties.getLicense()) {
				String licenseKey = tenant.getLicenseKey();
				String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
				accountNumber = JsonUtil.parse(decrypt, Tenant.class).getAccountNumber();
			}
			Long tenantCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId));
			if (accountNumber != null && accountNumber > 0 && accountNumber <= tenantCount) {
				throw new BusinessException("system.user.tenant.quota.max");
			}
		}
	}

	private Long registerUser(User user) {
		//如果app 邮箱注册，则校验验证码【欧洲版开始，app和web注册都要校验】
		String code = user.getVerificationCode();
		if (CommonConstant.REGISTER_PHONE.equals(user.getRegisterType())) {
			user.setAccount(this.phoneRegister(user, code));
		} else if (CommonConstant.REGISTER_EMAIL.equals(user.getRegisterType())){
			user.setAccount(this.emailRegister(user, code));
		}else{
			user.setAccount(user.getAccount());
		}

		// 设置角色和租户id
		if (StringUtil.isBlank(user.getTenantId())) {
			// 设置角色 设备端app注册时，没有角色，并且没有区分是设备端app
			if (StringUtil.isBlank(user.getRoleId()) && StringUtil.isBlank(user.getAppType())) {
				String roleId = roleService.getRoleIds(CommonConstant.CLIENT_TENANT_ID, "client_user");
				user.setRoleId(roleId);
				user.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				log.info("user register by device app");
			} else {
				//代理商和安装商 会选择角色，且需要传值app类型为 agent
				if (CommonConstant.APP_TYPE_AGENT.equals(user.getAppType())) {
					user.setTenantId(CommonConstant.AGENT_TENANT_ID);
				}
				log.info("user register by agent app");
			}
		}
		// 传了tenantId情况
		if (CommonConstant.PORTABLE_TENANT_ID.equals(user.getTenantId()) && CommonConstant.APP_TYPE_PORTABLE.equals(user.getAppType())) {
			String roleId = roleService.getRoleIds(CommonConstant.PORTABLE_TENANT_ID, "portable_user");
			user.setRoleId(roleId);
		}
		//欧洲版本开始，client邮箱注册会传租户和app类型
		if (CommonConstant.CLIENT_TENANT_ID.equals(user.getTenantId()) && CommonConstant.APP_TYPE_CLIENT.equals(user.getAppType())) {
			String roleId = roleService.getRoleIds(CommonConstant.CLIENT_TENANT_ID, "client_user");
			user.setRoleId(roleId);
			user.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		}
		// solpwr普通用户注册默认为solpwr租户和solpwr角色
		if (CommonConstant.SOLPWR_TENANT_ID.equals(user.getTenantId()) && CommonConstant.APP_TYPE_SOLPWR.equals(user.getAppType())) {
			String roleId = roleService.getRoleIds(CommonConstant.SOLPWR_TENANT_ID, "solpwr_user");
			user.setRoleId(roleId);
			user.setTenantId(CommonConstant.SOLPWR_TENANT_ID);
		}
		// 如果是代理商注册，需要设置对应的人员部门
		setAgentDeptId(user);
		this.setDefault(user);
		return getUserCount(user);
	}

	private Long getUserCount(User user) {
		Long userCount;
		if (CommonConstant.REGISTER_PHONE.equals(user.getRegisterType())) {
			userCount = baseMapper.selectCount(Wrappers.<User>query().lambda()
				.eq(User::getTenantId, user.getTenantId())
				.eq(User::getPhone, user.getPhone())
				.eq(User::getPhoneDiallingCode, user.getPhoneDiallingCode()));
			String getTemplateLanguage = CommonUtil.getCurrentLanguage();
			if (user.getPhoneDiallingCode().contains("86")) {
				getTemplateLanguage = CommonConstant.CURRENT_LANGUAGE_ZH;
			}
			String smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.TWO.getSmsType(), getTemplateLanguage);
			bladeRedis.del(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + user.getPhone());
		} else if (CommonConstant.REGISTER_EMAIL.equals(user.getRegisterType())) {
			userCount = baseMapper.selectCount(Wrappers.<User>query().lambda()
				.eq(User::getTenantId, user.getTenantId())
				.eq(User::getEmail, user.getEmail()));
			bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_REGISTER_CAPTCHA_SUFFIX.getCacheNameSuffix() + user.getEmail());
		} else {
			userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, user.getTenantId()).eq(User::getAccount, user.getAccount()));
		}
		return userCount;
	}


	private String emailRegister(User user, String code) {
		String key;
		String account;
		Pattern pattern = Pattern.compile(RegexUtil.EMAIL);
		key = user.getAccount() == null ? user.getEmail() : user.getAccount();
		if (!pattern.matcher(key).matches()) {
			//-
			throw new ServiceException("theEmailIsError");
		}
		account = key;//(user.getFirstName() == null?"":user.getFirstName()) + (user.getLastName()== null?"":user.getLastName()) +
		// 获取验证码
		String redisCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_REGISTER_CAPTCHA_SUFFIX.getCacheNameSuffix() + key);
		// 判断验证码
		if (code == null || !StringUtil.equalsIgnoreCase(redisCode, code)) {
			log.info("the verifiaction code : {}, redisCode ： {}  error ", code, redisCode);
			throw new BusinessException("system.user.the.verification.code.is.error");
		}
		bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_REGISTER_CAPTCHA_SUFFIX.getCacheNameSuffix() + key);
		return account;
	}

	@NotNull
	private String phoneRegister(User user, String code) {
		String trimmed = "";
		String key;
		String account;
		Pattern pattern = Pattern.compile(CommonConstant.PHONE_FORMAT);
		key = user.getPhone();
		if (!pattern.matcher(key).matches()) {
			//-
			throw new ServiceException("thePhoneIsError");
		}
		account = key;//(user.getFirstName() == null?"":user.getFirstName()) + (user.getLastName()== null?"":user.getLastName()) +
		String phoneDiallingCode = user.getPhoneDiallingCode();
		if (StringUtil.isBlank(phoneDiallingCode)) {
			//-
			throw new ServiceException("thePhoneIsError");
		}
		String phone = "";
		trimmed = phoneDiallingCode.trim();
		if (!trimmed.contains(CommonConstant.SIGN_ADD)) {
			trimmed = CommonConstant.SIGN_ADD + trimmed;
			user.setPhoneDiallingCode(trimmed);
		}
		phone = trimmed + key;
		// 获取验证码  5808022 为云片网登录 模板id
//				R r = smsClient.validateMessage(SmsConstant.SMS_CODE_4_REGISTERING_OR_DELETION, smsId, code, phone);
		String getTemplateLanguage = CommonUtil.getCurrentLanguage();
		if (user.getPhoneDiallingCode().contains("86")) {
			getTemplateLanguage = CommonConstant.CURRENT_LANGUAGE_ZH;
		}
		String smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.TWO.getSmsType(), getTemplateLanguage);
		String smsCode = bladeRedis.get(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		if (StringUtil.isBlank(smsCode) || StringUtil.isBlank(code)) {
			throw new BusinessException("system.user.the.verification.code.is.error");
		}
		if (!StringUtil.equalsIgnoreCase(code, smsCode)) {
			throw new BusinessException("system.user.the.verification.code.is.error");
		}
		return account;
	}

	private void setDefault(User user) {
		String firstName = Objects.requireNonNullElse(user.getFirstName(), "");
		String lastName = Objects.requireNonNullElse(user.getLastName(), "");
		user.setRealName(firstName.trim() + " " + lastName.trim());
		user.setName(user.getAccount());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateUser(User user) {
		String tenantId = user.getTenantId();
		List<User> userList = this.list(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, tenantId)
				.eq(User::getId, user.getId())
		);
		if (CommonConstant.USER_FROM_ALLOCATION.equals(user.getUserFrom())) {
			String trimmed = user.getPhoneDiallingCode().trim();
			if (!trimmed.contains(CommonConstant.SIGN_ADD)) {
				trimmed = CommonConstant.SIGN_ADD + trimmed;
				user.setPhoneDiallingCode(trimmed);
			}
			Long phUserCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, user.getTenantId()).eq(User::getPhone, user.getPhone())
				.eq(User::getPhoneDiallingCode, user.getPhoneDiallingCode())
				.ne(User::getId, user.getId()));
			if (phUserCount != 0L) {
				throw new BusinessException("system.user.phone.is.exits");
			}
		}
		// web端可以修改账号，导致userList查出来为空
		if (!userList.isEmpty() && userList.size() > 1) {
			throw new BusinessException("system.user.account.is.exist", user.getAccount());
		} else if (userList.isEmpty()) {
			throw new BusinessException("system.user.account.not.exist", user.getAccount());
		} else {
			// 获取修改前用户信息
			User userBefore = userList.get(0);
			if (!userBefore.getId().equals(user.getId())) {
				throw new BusinessException("system.user.account.abnormal", user.getAccount());
			}
			// 代理商用户删除角色
			if (CommonConstant.AGENT_TENANT_ID.equals(tenantId) || CommonConstant.CLIENT_TENANT_ID.equals(tenantId)) {
				cleanAgentUsers(user, userBefore);
			}
		}
		if (CommonConstant.CLIENT_TENANT_ID.equals(tenantId)) {
			if (StringUtil.isNotBlank(user.getDeptId())) {
				user.setDeptId(user.getDeptId() + "," + user.getAgentDeptIdInClient());
			} else {
				user.setDeptId(user.getAgentDeptIdInClient());
			}
			if (StringUtil.isNotBlank(user.getRoleId())) {
				String[] split = user.getRoleId().split(",");
				Set<String> roleIdSet = Arrays.stream(split)
					.collect(Collectors.toSet());
				String agentRoleIdInClient = user.getAgentRoleIdInClient() == null ? "" : user.getAgentRoleIdInClient();
				String[] agentRole = agentRoleIdInClient.split(",");
				Set<String> agentRoleSet = Arrays.stream(agentRole)
					.collect(Collectors.toSet());
				roleIdSet.addAll(agentRoleSet);
				user.setRoleId(Joiner.on(",").join(roleIdSet));
			} else {
				user.setRoleId(user.getAgentRoleIdInClient());
			}
		}
		// 判断用户是否
		updateUserInfo(user);
		submitUserDept(user);
		this.synchronizeUser4Update(user.getId(), tenantId);
		return true;
	}

	/**
	 * 清理代理商下用户
	 *
	 * @param user
	 * @param userBefore 入参
	 * @return void
	 * <AUTHOR>
	 * @since 2024/1/16 15:24
	 **/
	private void cleanAgentUsers(User user, User userBefore) {
		String roleIdBefore = userBefore.getRoleId();
		String roleIdAfter = user.getRoleId();
		if (StringUtil.isNotBlank(roleIdBefore)) {
			List<Role> roleList = roleService.getRoleList(roleIdBefore);
			Long electricianRoleId = Optional.ofNullable(roleList).orElse(new ArrayList<>()).stream().filter(a ->
				RoleCodeEnum.ELECTRICIAN.getRoleCode().equals(a.getRoleCode())).map(Role::getId).findFirst().orElse(null);
			Long technicianRoleId = Optional.ofNullable(roleList).orElse(new ArrayList<>()).stream().filter(a -> RoleCodeEnum.SITE_TECHNICIAN.getRoleCode().equals(a.getRoleCode())).map(Role::getId).
				findFirst().orElse(null);
			Long rolloutManagerRoleId = Optional.ofNullable(roleList).orElse(new ArrayList<>()).stream().filter(a -> RoleCodeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode().equals(a.getRoleCode())).map(Role::getId).
				findFirst().orElse(null);
			if (electricianRoleId != null || technicianRoleId != null || rolloutManagerRoleId != null) {
				List<String> deleteUserType = new ArrayList<>();
				if (electricianRoleId != null && !roleIdAfter.contains(electricianRoleId + "")) {
					deleteUserType.add("electrician");
				}
				if (technicianRoleId != null && !roleIdAfter.contains(technicianRoleId + "")) {
					deleteUserType.add("constructor");
				}
				if (rolloutManagerRoleId != null && !roleIdAfter.contains(rolloutManagerRoleId + "")) {
					deleteUserType.add("rolloutManager");
				}
				if (CollectionUtil.isNotEmpty(deleteUserType)) {
					JSONObject jsonObject = new JSONObject();
					jsonObject.set("userId", user.getId());
					jsonObject.set("userType", Joiner.on(",").join(deleteUserType));
					iAgentClient.cleanUpAgentUser(Collections.singletonList(jsonObject));
				}
			}
		}
	}

	@Override
	public boolean updateUserInfo(User user) {
		//校验该邮箱号是否被注册过账号，注册过则不能更新
		Long userCountEmail = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getEmail, user.getEmail())
			.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			.eq(User::getTenantId, user.getTenantId())
			.ne(User::getId, user.getId()));
		if (userCountEmail != 0L) {
			throw new BusinessException("system.user.email.is.exits");
		}
		//校验该手机号是否被注册过账号，注册过则不能更新
		Long userCountPhone = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getPhone, user.getPhone())
			.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			.eq(User::getTenantId, user.getTenantId())
			.eq(User::getPhoneDiallingCode, user.getPhoneDiallingCode())
			.ne(User::getId, user.getId()));
		if (userCountPhone != 0L) {
			throw new BusinessException("system.user.phone.is.exits");
		}
		user.setPassword(null);
		if (StringUtil.isNotBlank(user.getFirstName()) || StringUtil.isNotBlank(user.getLastName())) {
			user.setRealName(user.getFirstName() + " " + user.getLastName());
		}
		// 标识人员修改过信息，需重新登录
		userTokenLogOutById(user.getId());
		return updateById(user);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R appUpdatePhone(User user) {
		R r = new R();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (user.getId() == null) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100101.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100101.autoGetMessage(currentLanguage));
			return r;
		}
		if (!CommonConstant.REGISTER_PHONE.equals(user.getRegisterType())) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100105.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100105.autoGetMessage(currentLanguage));
			return r;
		}
		String tenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();

		if (StringUtil.isBlank(user.getPassword())) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.autoGetMessage(currentLanguage));
			return r;
		}
		User dbUser = getById(user.getId());
		if (!dbUser.getPassword().equals(DigestUtil.sha1Hex(user.getPassword()))) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100106.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100106.autoGetMessage(currentLanguage));
			return r;
		}
		Pattern pattern = Pattern.compile(CommonConstant.PHONE_FORMAT);
		if (!pattern.matcher(user.getPhone()).matches()) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100100.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100100.autoGetMessage(currentLanguage));
			return r;
		}
		String phoneDiallingCode = user.getPhoneDiallingCode();
		if (StringUtil.isBlank(phoneDiallingCode)) {
			r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.autoGetMessage(currentLanguage));
			return r;
		}
		String trim = phoneDiallingCode.trim();
		String phone = "";
		if (!trim.contains(CommonConstant.SIGN_ADD)) {
			trim = CommonConstant.SIGN_ADD + trim;
			user.setPhoneDiallingCode(trim);
		}
		phone = trim + user.getPhone();
		String code = user.getVerificationCode();
		Long userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId)
			.eq(User::getPhone, user.getPhone())
			.eq(User::getPhoneDiallingCode, trim));
		if (userCount > 0L) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100107.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100107.autoGetMessage(currentLanguage));
			return r;
		}
		// 获取验证码
//		R r1 = smsClient.validateMessage(SmsConstant.SMS_CODE_4_RETRIEVING_PASSWORD, user.getSmsId(), code, phone);
		String getTemplateLanguage = CommonUtil.getCurrentLanguage();
		if (phoneDiallingCode.contains("86")) {
			getTemplateLanguage = CommonConstant.CURRENT_LANGUAGE_ZH;
		}
		String smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.FOUR.getSmsType(), getTemplateLanguage);
		String smsCode = bladeRedis.get(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		if (StringUtil.isBlank(smsCode) || StringUtil.isBlank(code)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
			return r;
		}
		if (!StringUtil.equalsIgnoreCase(code, smsCode)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
			return r;
		}
		if (StringUtil.isNotBlank(user.getFirstName()) || StringUtil.isNotBlank(user.getLastName())) {
			user.setRealName(user.getFirstName().trim() + " " + user.getLastName().trim());
		}
		// 保存账号和手机一致
		user.setAccount(user.getPhone());
		user.setName(user.getPhone());
		CacheUtil.clear(USER_CACHE);
		user.setPassword(null);
		this.updateById(user);
		if (CommonConstant.CLIENT_TENANT_ID.equals(tenantId) || CommonConstant.AGENT_TENANT_ID.equals(tenantId)) {
			this.synchronizeUser4Update(user.getId(), tenantId);
		}
		// app端修改手机不踢出登录
		bladeRedis.del(CommonConstant.BLADE_USER_CHANGE_PRE + user.getId());
		bladeRedis.del(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		return R.data("操作成功");
	}

	private void synchronizeUser4Update(Long sourceUserId, String sourceTenantId) {
		log.info("synchronizeUser4Update userId : {} ,sourceTenantId : {}", sourceUserId, sourceTenantId);
//		User sourceDbUser = this.getById(sourceUserId);
		// 如果修改用户为 epc发起，则默认查设备端 是否存在此人员
		String tenantId = CommonConstant.CLIENT_TENANT_ID;
		String targetRoleIds = null;

		// 如果修改用户为设备端发起，则查询 epc端是否存在存在此人员
		if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId)) {
			tenantId = CommonConstant.AGENT_TENANT_ID;
		}
		log.info("synchronizeUser4Update targetTenantId : {}", tenantId);
//		User userIgnoreTenant = this.getUserIgnoreTenant(sourceDbUser.getPhone(), sourceDbUser.getPhoneDiallingCode(), tenantId);
		// 根据用户id 查询 mapping 对应的 人员 id
		UserMappingEntity userMappingIgnoreTenant = this.getUserMappingEntity(sourceUserId, sourceTenantId);
		if (userMappingIgnoreTenant == null) {
			return;
		}
		User sourceDbUser = this.getUserIgnoreTenant(sourceUserId);
		Long targetUserId = null;
		// 获取目标用户信息id
		if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId)) {
			// 如果发起方是设备端 ，则 目标 取epc的人员id
			targetUserId = userMappingIgnoreTenant.getSourceUserId();
		} else {
			targetUserId = userMappingIgnoreTenant.getMappingUserId();
		}
		log.info("synchronizeUser4Update targetUserId : {} ", targetUserId);
		User targetDbUser = this.getUserIgnoreTenant(targetUserId);
		log.info("synchronizeUser4Update sourceDbUser: {} , targetDbUser : {}", sourceDbUser, targetDbUser);
		// 当 2边手机号不一致时，
		if (!sourceDbUser.getPhone().equals(targetDbUser.getPhone()) || !sourceDbUser.getPhoneDiallingCode().equals(targetDbUser.getPhoneDiallingCode())) {
			log.info("synchronizeUser4Update modify phone  ");
			// 修改目标租户下时，需要校验修改后的手机号是否存在，如果存在，则不允许修改 ,使用修改后的手机号查目标端的用户信息
			Long phUserCount = baseMapper.selectCount(Wrappers.<User>query().lambda()
				.eq(User::getTenantId, targetDbUser.getTenantId())
				.eq(User::getPhone, sourceDbUser.getPhone())
				.eq(User::getPhoneDiallingCode, sourceDbUser.getPhoneDiallingCode()));
			log.info("synchronizeUser4Update phone count: {}", phUserCount);
			if (phUserCount > 0L) {
				log.info("synchronizeUser4Update phone exits in another : {} ", phUserCount);
				throw new BusinessException("system.user.phone.is.exits.to.another.tenant");
			}
		}

		Set<String> targetDeptSet = new HashSet<>();
		// 如果 设备端发起更新，那么不需要同步更新epc端的角色，保留设备端自身的角色即可
		// 如果 epc端发起更新，需要将epc的角色 转换成 设备端角色
		if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId)) {
			targetRoleIds = targetDbUser.getRoleId();
			// 如果client端修改，不需要修改agent下的部门，更新成原来的即可
			String agentDeptId = targetDbUser.getDeptId() == null ? "" : targetDbUser.getDeptId();
			String[] split = agentDeptId.split(",");
			targetDeptSet = Arrays.stream(split)
				.collect(Collectors.toSet());

			log.info("synchronizeUser4Update old targetRoleIds : {} ", targetRoleIds);
		} else {
			// epc发起更新： epc 的角色 id 转换为 设备端角色id
			String roleIds = sourceDbUser.getRoleId();
			String sourceRoleIds = sourceDbUser.getRoleId();
			log.info("synchronizeUser4Update old sourceRoleIds : {} ", sourceRoleIds);
			// 需要保留 设备端原有的角色
			String targetDbRoleId = targetDbUser.getRoleId();
			Set<String> targetRoleIdSet = new HashSet<>();
			if (StringUtil.isNotBlank(targetDbRoleId)) {
				String[] split = targetDbRoleId.split(",");
				targetRoleIdSet = Arrays.stream(split).filter(p -> !"".equals(p))
					.collect(Collectors.toSet());
				// 先删除 epc 在client中对应的角色，在下面统一以epc的最新为准
				List<Role> roleIgnoreTenant = roleService.getRoleIgnoreTenant(RoleCodeEnum.getClientRoleCode4Agent(), CommonConstant.CLIENT_TENANT_ID);
				Set<String> clientRoleId = roleIgnoreTenant.stream().map(p -> String.valueOf(p.getId())).collect(Collectors.toSet());
				targetRoleIdSet.removeAll(clientRoleId);
			}
			if (StringUtil.isNotBlank(roleIds)) {
				// 查询epc 的角色 得到角色编码
				List<Role> roleList = roleService.getRoleList(roleIds);
				Set<String> agentRoleCodeList = roleList.stream().map(Role::getRoleCode).collect(Collectors.toSet());
				// 将epc的角色编码 转成 设备角色编码
				List<String> clientRoleCodeByAgentRoleCode = RoleCodeEnum.getClientRoleCodeByAgentRoleCode(agentRoleCodeList);
				clientRoleCodeByAgentRoleCode.add(RoleCodeEnum.CLIENT_COMMON_ROLE.getRoleCode());
				// 得到设备端的角色 id
				List<Role> roleIgnoreTenant = roleService.getRoleIgnoreTenant(clientRoleCodeByAgentRoleCode, CommonConstant.CLIENT_TENANT_ID);
				targetRoleIdSet.addAll(roleIgnoreTenant.stream().map(p -> String.valueOf(p.getId())).collect(Collectors.toSet()));

			}
			targetRoleIds = Joiner.on(",").join(targetRoleIdSet);
			// 如果 agent发起修改部门，则先得到 client的部门
			String clientDeptId = targetDbUser.getDeptId();//client
			if (StringUtil.isNotBlank(clientDeptId)) {
				String[] clientDeptIdArray = clientDeptId.split(",");
				targetDeptSet = Arrays.stream(clientDeptIdArray).filter(p -> !"".equals(p))
					.collect(Collectors.toSet());
				// 得到 agent 所有的部门
				LambdaQueryWrapper<Dept> queryAgentDept = Wrappers.<Dept>query().lambda();
				queryAgentDept.eq(Dept::getTenantId, CommonConstant.AGENT_TENANT_ID);
				List<Dept> agentAllDeptList = deptService.list(queryAgentDept);
				Set<String> agentDeptIdSet = agentAllDeptList.stream().map(p -> {
					return String.valueOf(p.getId());
				}).filter(Objects::nonNull).collect(Collectors.toSet());
				// 移除原来agent下所有部门id，以agent人员上最新的为准
				targetDeptSet.removeAll(agentDeptIdSet);
			}
			Set<String> agentDept = new HashSet<>();
			String agentDeptId = sourceDbUser.getDeptId() == null ? "" : sourceDbUser.getDeptId();
			if (StringUtil.isNotBlank(agentDeptId)) {
				String[] agentDeptIdArray = agentDeptId.split(",");
				agentDept = Arrays.stream(agentDeptIdArray).filter(p -> !"".equals(p))
					.collect(Collectors.toSet());
				targetDeptSet.addAll(agentDept);
			}
		}
		// 如果epc 或者 设备端 存在 对应的人员
		User updateUser = this.getUpdateUser(sourceDbUser, targetDbUser.getId(), targetRoleIds, Joiner.on(",").join(targetDeptSet));
		baseMapper.updateUserIgnoreTenant(updateUser);
		// 标识人员修改过信息，需重新登录
		userTokenLogOutById(sourceUserId);
		userTokenLogOutById(targetUserId);
	}

	private UserMappingEntity getUserMappingEntity(Long sourceUserId, String sourceTenantId) {
		UserMappingEntity queryUserMapping = new UserMappingEntity();
		if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId)) {
			queryUserMapping.setMappingUserId(sourceUserId);
		} else {
			queryUserMapping.setSourceUserId(sourceUserId);
		}
		UserMappingEntity userMappingIgnoreTenant = userMappingService.getUserMappingIgnoreTenant(queryUserMapping);
		// 只在设备端注册过
		if (userMappingIgnoreTenant == null) {
			log.info("synchronizeUser4Update the user only in client ,sourceTenantId: {}, userId : {} ", sourceTenantId, sourceUserId);
			return null;
		}
		return userMappingIgnoreTenant;
	}

	private User getUserIgnoreTenant(Long sourceUserId) {
		User sourceQueryUser = new User();
		sourceQueryUser.setId(sourceUserId);
		return baseMapper.getUserIgnoreTenant(sourceQueryUser);
	}

	@Override
	public R appUpdateUser(User user) {
		R r = new R();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		User dbUser = getById(user.getId());
		// 欧洲版更新用户时，需要校验密码
		if (StringUtil.isNotBlank(user.getPassword())) {
			if (!dbUser.getPassword().equals(DigestUtil.sha1Hex(user.getPassword()))) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100106.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100106.autoGetMessage(currentLanguage));
				return r;
			}
		}

		// 更新邮箱，校验验证码[如果没有UpdateType字段则为旧版app，直接让他更新邮箱即可，不需要校验验证码]
		if (ValidationUtil.isEmpty(user.getUpdateType()) ||
			(ValidationUtil.isNotEmpty(user.getUpdateType()) && StringUtil.equals(user.getUpdateType(), UPDATE_TYPE_EMAIL))) {
			//校验该邮箱号是否被注册过账号，注册过则不能更新
			Long userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getEmail, user.getEmail())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
				.ne(User::getId, user.getId()));
			if (userCount != 0L) {
				throw new BusinessException("system.user.email.is.exits");
			}
			//兼容旧版app无UpdateType字段
			if (ValidationUtil.isNotEmpty(user.getUpdateType())) {
				String emailVerifyCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_BIND_CAPTCHA_SUFFIX.getCacheNameSuffix() + user.getEmail());
				String verificationCode = user.getVerificationCode();
				if (verificationCode == null || !StringUtil.equalsIgnoreCase(emailVerifyCode, verificationCode)) {
					throw new BusinessException("system.user.the.verification.code.is.error");
				}
				bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_BIND_CAPTCHA_SUFFIX.getCacheNameSuffix() + user.getEmail());
			}

			if (Boolean.TRUE.equals(user.getUnBind())) {
				// 如果邮箱号=账号，则把账号替换成手机号
				if (dbUser.getAccount().equals(user.getEmail()) && StringUtil.isNotBlank(dbUser.getPhone())) {
					user.setAccount(dbUser.getPhone());
				}
				user.setEmail("");
			}
			user.setPhone(null);
			user.setPhoneDiallingCode(null);
		}
		// 更新手机号，校验验证码【app不能在个人信息中填写手机号】
		if (ValidationUtil.isNotEmpty(user.getUpdateType()) && StringUtil.equals(user.getUpdateType(), UPDATE_TYPE_PHONE)) {
			String phone = user.getPhone();
			String phoneDiallingCode = user.getPhoneDiallingCode();
			String trim = phoneDiallingCode.trim();
			String finalPhone = "";
			if (!trim.contains("+")) {
				finalPhone = "+" + trim + phone;
				trim = "+" + trim;
				user.setPhoneDiallingCode(trim);
			} else {
				finalPhone = trim + phone;
			}
			//校验该手机号是否被注册过账号，注册过则不能更新
			Long userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getPhone, user.getPhone())
				.eq(User::getPhoneDiallingCode, user.getPhoneDiallingCode())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
				.ne(User::getId, user.getId()));
			if (userCount != 0L) {
				throw new BusinessException("system.user.phone.is.exits");
			}
			String getTemplateLanguage = CommonUtil.getCurrentLanguage();
			if (phoneDiallingCode.contains("86")) {
				getTemplateLanguage = CommonConstant.CURRENT_LANGUAGE_ZH;
			}
			String smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.FOUR.getSmsType(), getTemplateLanguage);
			String smsCode = bladeRedis.get(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + finalPhone);

			String verificationCode = user.getVerificationCode();
			if (StringUtil.isBlank(smsCode) || StringUtil.isBlank(verificationCode)) {
				throw new BusinessException("system.user.the.verification.code.is.error");
			}
			if (!StringUtil.equalsIgnoreCase(verificationCode, smsCode)) {
				throw new BusinessException("system.user.the.verification.code.is.error");
			}
			bladeRedis.del(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + finalPhone);

			if (Boolean.TRUE.equals(user.getUnBind())) {
				// 如果手机号=账号，则把账号替换成邮箱号
				if (dbUser.getAccount().equals(user.getPhone()) && StringUtil.isNotBlank(dbUser.getEmail())) {
					user.setAccount(dbUser.getEmail());
				}
				user.setPhone("");
				user.setPhoneDiallingCode("");
			}
			user.setEmail(null);
		}


		if (StringUtil.isNotBlank(user.getFirstName()) || StringUtil.isNotBlank(user.getLastName())) {
			user.setRealName(user.getFirstName().trim() + " " + user.getLastName().trim());
		}
		// 基本信息不能修改以下内容
		user.setPassword(null);
		if (Boolean.FALSE.equals(user.getUnBind())) {
			user.setAccount(null);
		}
		this.updateById(user);
		String tenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();
		if (CommonConstant.CLIENT_TENANT_ID.equals(tenantId) || CommonConstant.AGENT_TENANT_ID.equals(tenantId)) {
			this.synchronizeUser4Update(user.getId(), tenantId);
			// 便携更改用户信息后 调用了 刷新token接口，能重新获取用户信息
			bladeRedis.del(CommonConstant.BLADE_USER_CHANGE_PRE + user.getId());
		}
		// app端修改用户基本信息不退出登录
		bladeRedis.del(CommonConstant.BLADE_USER_CHANGE_PRE + user.getId());
		if (CommonConstant.PORTABLE_TENANT_ID.equals(user.getTenantId())) {
			UserAppExt userAppExt = new UserAppExt();
			BeanUtil.copy(user, userAppExt);
			userAppExt.setUserId(user.getId());
			userAppServiceImpl.updateUserAppByUserId(userAppExt);
			com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.toJSONString(userAppExt));
			potableClient.maintenanceDeviceLocation(json);
		}
		return R.data("操作成功");
	}

	private boolean submitUserDept(User user) {
		// app端注册没有部门信息，代理商注册时会根据代理编号查找相应部门
		if (StringUtils.isEmpty(user.getDeptId())) {
			return true;
		}
		List<Long> deptIdList = Func.toLongList(user.getDeptId());
		List<UserDept> userDeptList = new ArrayList<>();
		deptIdList.forEach(deptId -> {
			UserDept userDept = new UserDept();
			userDept.setUserId(user.getId());
			userDept.setDeptId(deptId);
			userDeptList.add(userDept);
		});
		userDeptService.remove(Wrappers.<UserDept>update().lambda().eq(UserDept::getUserId, user.getId()));
		return userDeptService.saveBatch(userDeptList);
	}

	@Override
	public IPage<User> selectUserPage(IPage<User> page, User user, Long deptId, String tenantId) {
		List<Long> deptIdList = SysCache.getDeptChildIds(deptId);
		return page.setRecords(baseMapper.selectUserPage(page, user, deptIdList, tenantId));
	}

	@Override
	public IPage<UserVO> selectUserSearch(UserVO user, Query query) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		String tenantId = AuthUtil.getTenantId();
		if (StringUtil.isNotBlank(tenantId)) {
			queryWrapper.eq(User::getTenantId, tenantId);
		}
		if (StringUtil.isNotBlank(user.getName())) {
			queryWrapper.like(User::getName, user.getName());
		}
		if (StringUtil.isNotBlank(user.getDeptName())) {
			String deptIds = SysCache.getDeptIdsByFuzzy(AuthUtil.getTenantId(), user.getDeptName());
			if (StringUtil.isNotBlank(deptIds)) {
				queryWrapper.and(wrapper -> {
					List<String> ids = Func.toStrList(deptIds);
					ids.forEach(id -> wrapper.like(User::getDeptId, id).or());
				});
			}
		}
		if (StringUtil.isNotBlank(user.getPostName())) {
			String postIds = SysCache.getPostIdsByFuzzy(AuthUtil.getTenantId(), user.getPostName());
			if (StringUtil.isNotBlank(postIds)) {
				queryWrapper.and(wrapper -> {
					List<String> ids = Func.toStrList(postIds);
					ids.forEach(id -> wrapper.like(User::getPostId, id).or());
				});
			}
		}
		IPage<User> pages = this.page(Condition.getPage(query), queryWrapper);
		return UserWrapper.build().pageVO(pages);
	}

	@Override
	public User userByAccount(String tenantId, String account) {
		return baseMapper.selectOne(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId).eq(User::getAccount, account).eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED));
	}

	@Override
	public UserInfo userInfo(Long userId) {
		User user = baseMapper.selectById(userId);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account) {
		User user = baseMapper.getUser(tenantId, account);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, UserEnum userEnum) {
		User user = baseMapper.getUser(tenantId, account);
		return buildUserInfo(user, userEnum);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, UserEnum userEnum, String phoneDiallingCode) {
		User user = baseMapper.getUserWithPhoneDiallingCode(tenantId, account, phoneDiallingCode);
		return buildUserInfo(user, userEnum);
	}

	private UserInfo buildUserInfo(User user) {
		return buildUserInfo(user, UserEnum.WEB);
	}

	private UserInfo buildUserInfo(User user, UserEnum userEnum) {
		if (ObjectUtil.isEmpty(user)) {
			return null;
		}
		UserInfo userInfo = new UserInfo();
		userInfo.setUser(user);
		Kv detail = getUserDetail(user, userEnum);
		this.getRoles(user, userInfo, detail);
		userInfo.setDetail(detail);
		return userInfo;
	}

	@NotNull
	private static Kv getUserDetail(User user, UserEnum userEnum) {
		// 根据每个用户平台，建立对应的detail表，通过查询将结果集写入到detail字段
		Kv detail = Kv.create().set("type", userEnum.getName());
		if (userEnum == UserEnum.WEB) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		} else if (userEnum == UserEnum.APP) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
				detail.set("countryCode", query.getCountryCode());
				detail.set("provinceCode", query.getProvinceCode());
				detail.set("cityCode", query.getCityCode());
				detail.set("countyCode", query.getCountyCode());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		}
		detail.set("firstName", user.getFirstName());
		detail.set("lastName", user.getLastName());
		detail.set("sex", user.getSex());
		detail.set("email", user.getEmail());
		detail.set("phone", user.getPhone());
		detail.set("phoneDiallingCode", user.getPhoneDiallingCode());
		return detail;
	}

	private UserInfo getUserExtAndRoleList(User user) {
		if (ObjectUtil.isEmpty(user)) {
			return null;
		}
		UserInfo userInfo = new UserInfo();
		userInfo.setUser(user);
		Kv detail = getUserDetail(user, UserEnum.APP);
		this.getRoles(user, userInfo, detail);
		return userInfo;
	}

	private void getRoles(User user, UserInfo userInfo, Kv detail) {
		R<List<Role>> result = sysClient.getRoleList(user.getRoleId());
		if (result.getCode() == 200) {
			List<Role> roleAlias = result.getData();
			detail.set("roleList", roleAlias);
			userInfo.setDetail(detail);
			if (CollectionUtil.isNotEmpty(roleAlias)) {
				List<String> roleNameList = roleAlias.stream().map(Role::getRoleAlias).collect(Collectors.toList());
				userInfo.setRoles(roleNameList);
				Boolean present = roleAlias.stream().anyMatch(p -> CommonConstant.ROLE_TYPE_INNER.equals(p.getRoleType()));
				detail.set(CommonConstant.USER_ROLE_INNER_FLAG, present);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo userInfo(UserOauth userOauth) {
		UserOauth uo = userOauthService.getOne(Wrappers.<UserOauth>query().lambda().eq(UserOauth::getUuid, userOauth.getUuid()).eq(UserOauth::getSource, userOauth.getSource()));
		UserInfo userInfo;
		if (Func.isNotEmpty(uo) && Func.isNotEmpty(uo.getUserId())) {
			userInfo = this.userInfo(uo.getUserId());
			userInfo.setOauthId(Func.toStr(uo.getId()));
		} else {
			userInfo = new UserInfo();
			if (Func.isEmpty(uo)) {
				userOauthService.save(userOauth);
				userInfo.setOauthId(Func.toStr(userOauth.getId()));
			} else {
				userInfo.setOauthId(Func.toStr(uo.getId()));
			}
			User user = new User();
			user.setAccount(userOauth.getUsername());
			user.setTenantId(userOauth.getTenantId());
			userInfo.setUser(user);
			userInfo.setRoles(Collections.singletonList(GUEST_NAME));
		}
		return userInfo;
	}

	@Override
	public boolean grant(String userIds, String roleIds) {
		User user = new User();
		user.setRoleId(roleIds);
		List<Long> longList = Func.toLongList(userIds);
		// web端角色配置需重新登录
		if (CollectionUtil.isNotEmpty(longList)) {
			longList.forEach(this::userTokenLogOutById);
		}
		return this.update(user, Wrappers.<User>update().lambda().in(User::getId, Func.toLongList(userIds)));
	}

	@Override
	public boolean resetPassword(String userIds) {
		User user = new User();
		user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD));
		user.setUpdateTime(DateUtil.now());
		List<Long> longList = Func.toLongList(userIds);
		// web端重置密码需重新登录
		if (CollectionUtil.isNotEmpty(longList)) {
			longList.forEach(this::userTokenLogOutById);
		}
		return this.update(user, Wrappers.<User>update().lambda().in(User::getId, Func.toLongList(userIds)));
	}

	@Override
	public boolean updatePassword(Long userId, String oldPassword, String newPassword, String newPassword1) {
		User user = getById(userId);
		if (!newPassword.equals(newPassword1)) {
			throw new BusinessException("system.user.enter.confirm.password");
		}
		if (!user.getPassword().equals(DigestUtil.hex(oldPassword))) {
			throw new BusinessException("system.user.original.password.incorrect");
		}
		userTokenLogOutById(userId);
		return this.update(Wrappers.<User>update().lambda().set(User::getPassword, DigestUtil.hex(newPassword)).eq(User::getId, userId));
	}

	private void userTokenLogOutById(Long userId) {
		bladeRedis.setEx(CommonConstant.BLADE_USER_CHANGE_PRE + userId, BizConstant.NUMBER_ONE, Duration.ofDays(BizConstant.NUMBER_FOUR));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<String> appUpdatePassword(Long userId, String oldPassword, String newPassword, String newPassword1) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		User user = getById(userId);
		R<String> r = new R<String>();
		if (!newPassword.equals(newPassword1)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100009.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100009.autoGetMessage(currentLanguage));
			return r;
		}
		if (!user.getPassword().equals(DigestUtil.hex(oldPassword))) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100010.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100010.autoGetMessage(currentLanguage));
			return r;
		}
		this.update(Wrappers.<User>update().lambda().set(User::getPassword, DigestUtil.hex(newPassword)).eq(User::getId, userId));
		String tenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();
		if (CommonConstant.CLIENT_TENANT_ID.equals(tenantId) || CommonConstant.AGENT_TENANT_ID.equals(tenantId)) {
			this.synchronizeUser4Update(user.getId(), tenantId);
		} else {
			userTokenLogOutById(user.getId());
		}
		return R.success("操作成功");
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R forgetPassword(UserInfoVO userInfoVO) {
		HttpServletRequest request = WebUtil.getRequest();
		String currentLanguage = request.getHeader("Accept-Language");
		if (ValidationUtil.isNotEmpty(currentLanguage)) {
			Locale locale = Locale.lookup(Locale.LanguageRange.parse(currentLanguage), List.of(Locale.getAvailableLocales()));
			// 获取语言类型
			currentLanguage = locale.getLanguage();
		} else {
			currentLanguage = "en";
		}
//		String currentLanguage = CommonUtil.getCurrentLanguage();
		String key;
		R r = new R();
		if (StringUtils.isEmpty(userInfoVO.getNewPassword())) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.autoGetMessage(currentLanguage));
			return r;
		}
		String newPassword = StringEscapeUtils.unescapeHtml4(userInfoVO.getNewPassword());
		String code = userInfoVO.getVerificationCode();
		// 邮箱注册
		if ("0".equals(userInfoVO.getType())) {
			key = userInfoVO.getUserAccount();
			String redisCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_FORGET_PASSWORD_CAPTCHA_SUFFIX.getCacheNameSuffix() + key);
			// 判断验证码
			if (code == null || !StringUtil.equalsIgnoreCase(redisCode, code)) {
				throw new BusinessException("system.user.the.verification.code.is.error");
			}
			bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_FORGET_PASSWORD_CAPTCHA_SUFFIX.getCacheNameSuffix() + key);
		} else {
			String phoneDiallingCode = userInfoVO.getPhoneDiallingCode();
			if (StringUtil.isBlank(phoneDiallingCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.autoGetMessage(currentLanguage));
				return r;
			}
			String trim = phoneDiallingCode.trim();
			String phone = "";
			if (!trim.contains(CommonConstant.SIGN_ADD)) {
				trim = CommonConstant.SIGN_ADD + trim;
				userInfoVO.setPhoneDiallingCode(trim);
			}
			phone = trim + userInfoVO.getPhone();
			// 获取验证码
			// 忘记密码 发送短信前需要校验手机号是否存在，不存在则报错
			if (StringUtil.isNotBlank(userInfoVO.getTenantId())) {
				Long userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, userInfoVO.getTenantId())
					.eq(User::getPhone, userInfoVO.getPhone())
					.eq(User::getPhoneDiallingCode, trim));
				if (userCount == 0L) {
					r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100108.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100108.autoGetMessage(currentLanguage));
					return r;
				}
			}
			String getTemplateLanguage = CommonUtil.getCurrentLanguage();
			if (phoneDiallingCode.contains("86")) {
				getTemplateLanguage = CommonConstant.CURRENT_LANGUAGE_ZH;
			}
			String smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.THREE.getSmsType(), getTemplateLanguage);
			String smsCode = bladeRedis.get(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
			if (StringUtil.isBlank(smsCode) || StringUtil.isBlank(code)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			if (!StringUtil.equalsIgnoreCase(code, smsCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			userInfoVO.setUserAccount(userInfoVO.getPhone());
			bladeRedis.del(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		}
		// 代理商端 修改密码需要加密
		if (CommonConstant.APP_TYPE_AGENT.equals(userInfoVO.getAppType())) {
			userInfoVO.setNewPassword(DigestUtil.hex(newPassword));
		} else {
			// 设备端原来前端传过来的密码没有进行md5加密
			userInfoVO.setNewPassword(DigestUtil.hex(newPassword));
		}
		baseMapper.updateUser(userInfoVO);
		log.info("update password ");
		User queryUser = new User();
		queryUser.setPhone(userInfoVO.getPhone());
		queryUser.setPhoneDiallingCode(userInfoVO.getPhoneDiallingCode());
		List<User> users = baseMapper.userInfoByCondition(queryUser);
		if (CollectionUtil.isNotEmpty(users)) {
			log.info("update password get userInfo ");
			String tenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();
			if (CommonConstant.CLIENT_TENANT_ID.equals(tenantId) || CommonConstant.AGENT_TENANT_ID.equals(tenantId)) {
				this.synchronizeUser4Update(users.get(0).getId(), tenantId);
			} else {
				userTokenLogOutById(users.get(0).getId());
			}
		}
		// 修改密码需要退出登录
		userTokenLogOutById(users.get(0).getId());
		return R.data("操作成功");
	}

	@Override
	public boolean removeUser(String userIds) {
		if (Func.contains(Func.toLongArray(userIds), AuthUtil.getUserId())) {
			throw new BusinessException("system.user.account.connot.delete");
		}
		boolean result = false;
		String sourceTenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();
		if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId) || CommonConstant.AGENT_TENANT_ID.equals(sourceTenantId)) {
			boolean flag = this.synchronizeUser4Delete(Func.toLongList(userIds), sourceTenantId);
			if (!flag) {
				// 历史数据，没有同步的，只要删除原始数据即可
				deleteLogic(Func.toLongList(userIds));
			}
			result = true;
		} else {
			result = deleteLogic(Func.toLongList(userIds));
		}
		// 删除代理商用户信息
		String tenantId = AuthUtil.getTenantId();
		if (CommonConstant.AGENT_TENANT_ID.equals(tenantId) || CommonConstant.CLIENT_TENANT_ID.equals(tenantId)) {
			List<JSONObject> agentList = new ArrayList<>();
			List<String> deleteUserType = new ArrayList<>();
			deleteUserType.add("electrician");
			deleteUserType.add("constructor");
			deleteUserType.add("rolloutManager");
			Arrays.stream(userIds.split(",")).forEach(a -> {
				JSONObject jsonObject = new JSONObject();
				jsonObject.set("userId", a);
				jsonObject.set("userType", Joiner.on(",").join(deleteUserType));
				agentList.add(jsonObject);
			});
			iAgentClient.cleanUpAgentUser(agentList);
		}
		List<Long> longList = Func.toLongList(userIds);
		if (CollectionUtil.isNotEmpty(longList)) {
			// 后台删除用户需要重新登录
			longList.forEach(this::userTokenLogOutById);
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void importUser(List<UserExcel> data, Boolean isCovered) {
		data.forEach(userExcel -> {
			User user = Objects.requireNonNull(BeanUtil.copy(userExcel, User.class));
			// 设置用户平台
			user.setUserType(Func.toInt(DictCache.getKey(DictEnum.USER_TYPE, userExcel.getUserTypeName()), 1));
			// 设置部门ID
			user.setDeptId(Func.toStrWithEmpty(SysCache.getDeptIds(userExcel.getTenantId(), userExcel.getDeptName()), StringPool.EMPTY));
			// 设置岗位ID
			user.setPostId(Func.toStrWithEmpty(SysCache.getPostIds(userExcel.getTenantId(), userExcel.getPostName()), StringPool.EMPTY));
			// 设置角色ID
			user.setRoleId(Func.toStrWithEmpty(SysCache.getRoleIds(userExcel.getTenantId(), userExcel.getRoleName()), StringPool.EMPTY));
			// 设置租户ID
			if (!AuthUtil.isAdministrator() || StringUtil.isBlank(user.getTenantId())) {
				user.setTenantId(AuthUtil.getTenantId());
			}
			// 覆盖数据
			if (isCovered) {
				// 查询用户是否存在
				User oldUser = UserCache.getUser(userExcel.getTenantId(), userExcel.getAccount());
				if (oldUser != null && oldUser.getId() != null) {
					user.setId(oldUser.getId());
					this.updateUser(user);
					return;
				}
			}
			// 获取默认密码配置
			String initPassword = ParamCache.getValue(DEFAULT_PARAM_PASSWORD);
			user.setPassword(initPassword);
			this.submit(user);
		});
	}

	@Override
	public List<UserExcel> exportUser(Wrapper<User> queryWrapper) {
		List<UserExcel> userList = baseMapper.exportUser(queryWrapper);
		userList.forEach(user -> {
			user.setUserTypeName(DictCache.getValue(DictEnum.USER_TYPE, user.getUserType()));
			user.setRoleName(StringUtil.join(SysCache.getRoleNames(user.getRoleId())));
			user.setDeptName(StringUtil.join(SysCache.getDeptNames(user.getDeptId())));
			user.setPostName(StringUtil.join(SysCache.getPostNames(user.getPostId())));
		});
		return userList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean registerGuest(User user, Long oauthId) {
		Tenant tenant = SysCache.getTenant(user.getTenantId());
		if (tenant == null || tenant.getId() == null) {
			throw new BusinessException("system.user.tenant.info.error");
		}
		UserOauth userOauth = userOauthService.getById(oauthId);
		if (userOauth == null || userOauth.getId() == null) {
			throw new BusinessException("system.user.third.login.error");
		}
		user.setRealName(user.getFirstName().trim() + " " + user.getLastName().trim());
		user.setAvatar(userOauth.getAvatar());
		user.setRoleId(StringPool.MINUS_ONE);
		user.setDeptId(StringPool.MINUS_ONE);
		user.setPostId(StringPool.MINUS_ONE);
		boolean userTemp = this.submit(user);
		userOauth.setUserId(user.getId());
		userOauth.setTenantId(user.getTenantId());
		boolean oauthTemp = userOauthService.updateById(userOauth);
		return (userTemp && oauthTemp);
	}

	@Override
	public boolean updatePlatform(Long userId, Integer userType, String userExt) {
		if (userType.equals(UserEnum.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userWeb.setId(query.getId());
			}
			userWeb.setUserId(userId);
			userWeb.setUserExt(userExt);
			return userWeb.insertOrUpdate();
		} else if (userType.equals(UserEnum.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userApp.setId(query.getId());
			}
			userApp.setUserId(userId);
			userApp.setUserExt(userExt);
			return userApp.insertOrUpdate();
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userOther.setId(query.getId());
			}
			userOther.setUserId(userId);
			userOther.setUserExt(userExt);
			return userOther.insertOrUpdate();
		}
	}

	@Override
	public UserVO platformDetail(User user) {
		User detail = baseMapper.selectOne(Condition.getQueryWrapper(user));
		UserVO userVO = UserWrapper.build().entityVO(detail);
		if (userVO.getUserType().equals(UserEnum.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else if (userVO.getUserType().equals(UserEnum.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		}
		return userVO;
	}

	@Override
	public R<UserInfo> userInfoByCondition(User user) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<User> users = baseMapper.userInfoByCondition(user);
		R<UserInfo> r = new R<>();
		if ((CollectionUtil.isNotEmpty(users) && users.size() > 1) || users.isEmpty()) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.autoGetMessage(currentLanguage));
			return r;
		}
		return R.data(this.getUserExtAndRoleList(users.get(0)));
	}


	@Override
	public List<User> findUserInfoByRoleName(User user) {
		List<User> userList = new LinkedList<>();
		LambdaQueryWrapper<Role> roleLambdaQueryWrapper = new LambdaQueryWrapper<>();
		roleLambdaQueryWrapper.eq(Role::getRoleName, user.getRoleName()).eq(Role::getIsDeleted, 0);
		//根据角色名称模糊查询角色信息
		Role role = roleService.getOne(roleLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(role)) {
			return userList;
		}
		// 获取当前用户租户
		BladeUser currencyUser = AuthUtil.getUser();
		String tenantId = currencyUser.getTenantId();
		//根据角色id模糊查询用户信息
		LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
		userLambdaQueryWrapper.like(User::getRoleId, role.getId()).eq(User::getIsDeleted, 0).eq(User::getTenantId, tenantId);
		if (StringUtil.isNotBlank(user.getRealName())) {
			userLambdaQueryWrapper.likeRight(User::getRealName, user.getRealName());
		}
		if (StringUtil.isNotBlank(user.getPhone())) {
			userLambdaQueryWrapper.likeRight(User::getPhone, user.getPhone());
		}
		return this.list(userLambdaQueryWrapper);
	}





	private void setAgentDeptId(User user) {
		// 非代理商app注册不执行下面
		if (!CommonConstant.APP_TYPE_AGENT.equals(user.getAppType())) {
			return;
		}
		if (RoleCodeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode().equals(user.getRoleCode())) {
			// 代理商交付经理角色
			if (StringUtil.isBlank(user.getAgentNumber())) {
				throw new BusinessException("system.agent.agentNumber.can.not.empty");
			}
			//获取代理商部门id
			R<AgentCompanyVO> agentCompany = iAgentClient.agentNumber(user.getAgentNumber());
			if (!agentCompany.isSuccess()) {
				throw new BusinessException("system.agent.agentNumber.is.not.exits");
			}
			AgentCompanyVO data = agentCompany.getData();
			if (ObjectUtils.isNotEmpty(data)) {
				user.setDeptId(String.valueOf(data.getDeptId()));
				// 用于注册时将人员写入代理商人员表
				user.setAgentId(data.getId());
			}
		}
	}


	@Override
	public R<User> userByUserInfo(User user) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<User> users = baseMapper.userInfoByCondition(user);
		R r = new R();
		if (users == null || users.isEmpty()) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100031.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100031.autoGetMessage(currentLanguage));
			return r;
		}
		return R.data(users.get(0));
	}

	@Override
	public List<User> getAllUsers() {
		LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
		userLambdaQueryWrapper.eq(User::getIsDeleted, 0);
		return this.list(userLambdaQueryWrapper);
	}


	/**
	 * @Description: 根据用户id查询该用户所具有的角色信息
	 * @Param: [user]
	 * @Return: java.util.List<org.springblade.system.entity.Role>
	 * @Author: baixu
	 * @Date: 2023/11/24 10:19
	 **/
	@Override
	public R<List<Role>> findUserRoleInfoByUserId(String userId) {
		LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
		userLambdaQueryWrapper.eq(User::getId, userId).eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED).select(User::getRoleId);
		User userInfo = this.getOne(userLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(userInfo) || StringUtil.isBlank(userInfo.getRoleId())) {
			return R.data(Collections.emptyList());
		}
		//根据角色id查询角色信息
		List<String> roleIdList = Arrays.asList(userInfo.getRoleId().split(","));
		LambdaQueryWrapper<Role> roleLambdaQueryWrapper = new LambdaQueryWrapper<>();
		// 获取当前用户租户
		BladeUser currencyUser = AuthUtil.getUser();
		String tenantId = currencyUser.getTenantId();
		roleLambdaQueryWrapper.in(Role::getId, roleIdList).eq(Role::getIsDeleted, BladeConstant.DB_NOT_DELETED).eq(Role::getTenantId, tenantId);
		return R.data(roleService.list(roleLambdaQueryWrapper));
	}

	@Override
	public R<List<User>> getUserList(User user) {
		return R.data(baseMapper.userInfoByCondition(user));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<String> appendRole(List<UserInfoVO> roleList) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<String> r = new R<>();
		if (CollectionUtil.isEmpty(roleList)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_ROLE_100135.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_ROLE_100135.autoGetMessage(currentLanguage));
			return r;
		}
		Long deptId = null;
		// 是否存在代理商交付经理
		boolean existRole = roleList.stream().anyMatch(p -> RoleCodeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode().equals(p.getRoleCode()));
		if (existRole) {
			List<UserInfoVO> agentNumberList = roleList.stream().filter(p -> StringUtil.isNotBlank(p.getAgentNumber())).collect(Collectors.toList());
			if (CollectionUtil.isEmpty(agentNumberList)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100133.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100133.autoGetMessage(currentLanguage));
				return r;
			}
			UserInfoVO user = agentNumberList.get(CommonConstant.NOT_SEALED_ID);
			//获取代理商部门id
			R<AgentCompanyVO> agentCompany = iAgentClient.agentNumber(user.getAgentNumber());
			if (!agentCompany.isSuccess()) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100136.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100136.autoGetMessage(currentLanguage));
				return r;
			}
			AgentCompanyVO data = agentCompany.getData();
			if (ObjectUtils.isNotEmpty(data)) {
				deptId = data.getDeptId();
			}
		}

		List<Long> roleIdList = roleList.stream().map(UserInfoVO::getRoleId).collect(Collectors.toList());
		String roleIds = Joiner.on(",").join(roleIdList);
		BladeUser user = AuthUtil.getUser();
		baseMapper.appendRole(roleIds, user.getUserId(), deptId);
		log.info("appendRole synchronizeUser4Update ");
		String tenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();
		if (CommonConstant.CLIENT_TENANT_ID.equals(tenantId) || CommonConstant.AGENT_TENANT_ID.equals(tenantId)) {
			this.synchronizeUser4Update(user.getUserId(), tenantId);
		} else {
			userTokenLogOutById(user.getUserId());
		}
		return R.data("操作成功");
	}

	@Override
	public R<String> phoneDeleteUser(UserInfoVO userInfoVO) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		log.info("phoneDeleteUser info: {} ", userInfoVO);
		log.debug("phoneDeleteUser debug: {} ", userInfoVO);
		R<String> r = new R<String>();
		String phoneDiallingCode = userInfoVO.getPhoneDiallingCode();
		if (StringUtil.isBlank(phoneDiallingCode)) {
			r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.autoGetMessage(currentLanguage));
			return r;
		}
		String trim = phoneDiallingCode.trim();
		String phone = "";
		if (!trim.contains(CommonConstant.SIGN_ADD)) {
			trim = CommonConstant.SIGN_ADD + trim;
		}
		phone = phone + trim + userInfoVO.getPhone();
		String code = userInfoVO.getVerificationCode();
		String getTemplateLanguage = CommonUtil.getCurrentLanguage();
		if (phoneDiallingCode.contains("86")) {
			getTemplateLanguage = CommonConstant.CURRENT_LANGUAGE_ZH;
		}
		String smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.FIVE.getSmsType(), getTemplateLanguage);
		String smsCode = bladeRedis.get(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		log.info("phoneDeleteUser get redis VerificationCode : {}", smsCode);
		if (StringUtil.isBlank(smsCode) || StringUtil.isBlank(code)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
			return r;
		}
		if (!StringUtil.equalsIgnoreCase(code, smsCode)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
			return r;
		}
		BladeUser user = AuthUtil.getUser();
		String sourceTenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();
		if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId) || CommonConstant.AGENT_TENANT_ID.equals(sourceTenantId)) {
			boolean flag = this.synchronizeUser4Delete(Lists.newArrayList(AuthUtil.getUserId()), sourceTenantId);
			if (!flag) {
				// 历史数据，没有同步的，只要删除原始数据即可
				deleteLogic(Func.toLongList(String.valueOf(user.getUserId())));
			}
		} else {
			this.deleteLogic(Func.toLongList(String.valueOf(user.getUserId())));
		}
		if (CommonConstant.APP_TYPE_AGENT.equals(userInfoVO.getAppType()) || CommonConstant.APP_TYPE_CLIENT.equals(userInfoVO.getAppType())) {
			// 删除对应业务数据
			bladeRedis.lPush(CommonConstant.PHONE_DELETE_USER_COMMON_KEY + CommonConstant.APP_TYPE_AGENT + StringPool.COLON, user.getUserId());
			// 删除代理商
			List<String> deleteUserType = new ArrayList<>();
			deleteUserType.add("electrician");
			deleteUserType.add("constructor");
			deleteUserType.add("rolloutManager");
			JSONObject jsonObject = new JSONObject();
			jsonObject.set("userId", user.getUserId());
			jsonObject.set("userType", Joiner.on(",").join(deleteUserType));
			iAgentClient.cleanUpAgentUser(Collections.singletonList(jsonObject));
		} else if (CommonConstant.APP_TYPE_PORTABLE.equals(userInfoVO.getAppType())) {
			// 删除对应业务数据
			bladeRedis.lPush(CommonConstant.PHONE_DELETE_USER_COMMON_KEY + CommonConstant.APP_TYPE_PORTABLE + StringPool.COLON, user.getUserId());
		}
		bladeRedis.del(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		return R.data("操作成功");
	}

	private boolean synchronizeUser4Delete(List<Long> sourceUserIdList, String sourceTenantId) {
		// 得到人员映射表数据
		List<UserMappingEntity> userMappingIgnoreTenantList = this.getUserMappingEntityList(sourceUserIdList, sourceTenantId);
		if (userMappingIgnoreTenantList == null) {
			return false;
		}
		if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId)) {
			// 设备端注销用户 只删除设备端数据，不影响epc
			log.info("synchronizeUser4Delete in client");
		}
		if (CommonConstant.AGENT_TENANT_ID.equals(sourceTenantId)) {
			log.info("synchronizeUser4Delete in agent");
			LambdaQueryWrapper<Dept> queryDept = Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, CommonConstant.AGENT_TENANT_ID);
			List<Dept> agentDeptlist = deptService.list(queryDept);
			Set<String> agentDeptIdSet = agentDeptlist.stream().map(p -> String.valueOf(p.getId())).collect(Collectors.toSet());
			List<String> clientRoleCode = RoleCodeEnum.getClientRoleCode4Agent();
			// 得到设备端的角色 id
			List<Role> roleIgnoreTenant = roleService.getRoleIgnoreTenant(clientRoleCode, CommonConstant.CLIENT_TENANT_ID);
			Set<String> clientRoleIdSet = roleIgnoreTenant.stream().map(p -> String.valueOf(p.getId())).collect(Collectors.toSet());
			// 根据查询epc人员id 查询 设备人员信息，获取角色、部门信息
			List<UserInfoMappingVO> clientDbUser = baseMapper.batchGetClientUserByEpcUserIgnoreTenant(sourceUserIdList);
			List<User> updateClientUserList = new ArrayList<>();
			for (UserInfoMappingVO vo : clientDbUser) {
				User updateUser = new User();
				BeanUtil.copy(vo, updateUser);
				updateUser.setId(vo.getId());
				String roleIds = vo.getRoleId();
				Set<String> userRole = new HashSet<>();
				if (StringUtil.isNotBlank(roleIds)) {
					String[] roleIdArray = roleIds.split(",");
					userRole = Arrays.stream(roleIdArray)
						.collect(Collectors.toSet());
					userRole.removeAll(clientRoleIdSet);
				}
				updateUser.setRoleId(Joiner.on(",").join(userRole));
				Set<String> userDept = new HashSet<>();
				String deptIds = vo.getDeptId();
				// 移除 agent 对应的 部门id，只保留client的部门id
				if (StringUtil.isNotBlank(deptIds)) {
					String[] userDeptIdArray = deptIds.split(",");
					userDept = Arrays.stream(userDeptIdArray)
						.collect(Collectors.toSet());
					userDept.removeAll(agentDeptIdSet);
				}
				updateUser.setDeptId((Joiner.on(",").join(userDept)));
				updateClientUserList.add(updateUser);
			}
			if (CollectionUtil.isNotEmpty(updateClientUserList)) {
				baseMapper.batchUpdateUserIgnoreTenant(updateClientUserList);
			}
			for (User clientUser : updateClientUserList) {
				// 标识人员修改过信息，需重新登录
				userTokenLogOutById(clientUser.getId());
			}
		}
		// 设备端注销用户 只删除设备端数据，不影响epc
		this.deleteLogic(sourceUserIdList);
		if (CollectionUtil.isNotEmpty(userMappingIgnoreTenantList)) {
			// 删除用户映射表
			List<Long> userMappingIdList = userMappingIgnoreTenantList.stream()
				.map(UserMappingEntity::getId).collect(Collectors.toList());
			userMappingService.batchDeleteUserMappingIgnoreTenant(userMappingIdList, CommonConstant.CLIENT_TENANT_ID);
		}
		// 删除代理商下人员 和 站点下的运维人员， （设备端注销时，直接删除了设备端的人员，可以不用处理站点的数据）
		if (CommonConstant.AGENT_TENANT_ID.equals(sourceTenantId)) {
			iAgentClient.deleteAgentUserInfo(sourceUserIdList);
			List<Long> userMappingIdList = userMappingIgnoreTenantList.stream()
				.map(UserMappingEntity::getMappingUserId).collect(Collectors.toList());
			iPlantClient.cleanPlantOperationUserId(userMappingIdList);
		}
		return true;
	}

	private List<UserMappingEntity> getUserMappingEntityList(List<Long> sourceUserIdList, String sourceTenantId) {
		List<UserMappingEntity> queryUserMappingList = new ArrayList<>();
		for (Long sourceUserId : sourceUserIdList) {
			UserMappingEntity queryUserMapping = new UserMappingEntity();
			if (CommonConstant.CLIENT_TENANT_ID.equals(sourceTenantId)) {
				queryUserMapping.setMappingUserId(sourceUserId);
			} else {
				queryUserMapping.setSourceUserId(sourceUserId);
			}
			queryUserMappingList.add(queryUserMapping);
		}
		List<UserMappingEntity> userMappingIgnoreTenant = userMappingService.batchGetUserMappingIgnoreTenant(queryUserMappingList);
		// 只在设备端注册过
		if (userMappingIgnoreTenant == null || userMappingIgnoreTenant.isEmpty()) {
			log.info("synchronizeUser4Delete getUserMappingEntityList the user only in client ");
			return null;
		}
		return userMappingIgnoreTenant;
	}

	@Override
	public IPage<User> roleUser(Query query, String roleId) {
		IPage<User> page = Condition.getPage(query);
		BladeUser user = AuthUtil.getUser();
		List<User> users = baseMapper.roleUser(page, roleId, user.getTenantId());
		page.setRecords(users);
		return page;
	}

	@Override
	public R synchronousUserList(UserBatchVO<User> userUserBatchVO) {
		List<User> addUserList = userUserBatchVO.getAddList();
		List<User> deleteUserList = userUserBatchVO.getDeleteList();
		BladeUser user = AuthUtil.getUser();
		Long updatedUserId = user.getUserId();
		// 解绑映射用户的角色和部门信息
		if (CollectionUtil.isNotEmpty(deleteUserList)) {
			baseMapper.deleteUserListInfo(deleteUserList, updatedUserId);
			// 判断是否是代理商交付经理角色，代理商交付经理角色，需要清理自身
			List<User> userRollManagerList = deleteUserList.stream().filter(a -> RoleCodeEnum.CLIENT_ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode().equalsIgnoreCase(a.getRoleCode())).collect(Collectors.toList());
			// 解绑原用户的部门信息
			if (CollectionUtil.isNotEmpty(userRollManagerList)) {
				baseMapper.deleteAgentRollOutManager(userRollManagerList, updatedUserId);
			}
		}
		// 绑定映射用户角色和部门信息
		if (CollectionUtil.isNotEmpty(addUserList)) {
			baseMapper.updateUserListInfo(addUserList, updatedUserId);
		}
		// 用户信息变化，需要清除当前登录信息
		if (CollectionUtils.isNotEmpty(addUserList) || CollectionUtils.isNotEmpty(deleteUserList)) {
			List<User> allUserList = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(addUserList)) {
				allUserList.addAll(addUserList);
			}
			if (CollectionUtils.isNotEmpty(deleteUserList)) {
				allUserList.addAll(deleteUserList);
			}
			List<Long> sourceUserIds = allUserList.stream().map(User::getId).distinct().collect(Collectors.toList());
			List<UserMappingEntity> userMappingList = userMappingService.list(Wrappers.<UserMappingEntity>lambdaQuery().in(UserMappingEntity::getSourceUserId, sourceUserIds));
			userMappingList.forEach(a -> {
				userTokenLogOutById(a.getMappingUserId());
				userTokenLogOutById(a.getSourceUserId());
			});
		}
		return R.success("success");
	}

	@Override
	public R appUpdateEmail(User user) {
		R r = new R();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (user.getId() == null) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100101.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100101.autoGetMessage(currentLanguage));
			return r;
		}
		if (!CommonConstant.REGISTER_EMAIL.equals(user.getRegisterType())) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100105.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100105.autoGetMessage(currentLanguage));
			return r;
		}
		String tenantId = AuthUtil.getTenantId() == null ? CommonConstant.CLIENT_TENANT_ID : AuthUtil.getTenantId();

		if (StringUtil.isBlank(user.getPassword())) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.autoGetMessage(currentLanguage));
			return r;
		}
		User dbUser = getById(user.getId());
		if (!dbUser.getPassword().equals(DigestUtil.sha1Hex(user.getPassword()))) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100106.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100106.autoGetMessage(currentLanguage));
			return r;
		}
		Pattern pattern = Pattern.compile(RegexUtil.EMAIL);
		if (!pattern.matcher(user.getEmail()).matches()) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100005.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100005.autoGetMessage(currentLanguage));
			return r;
		}
		String code = user.getVerificationCode();
		Long userCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getAccount, user.getEmail())
			.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			.ne(User::getId, user.getId()));
		if (userCount != 0L) {
			throw new BusinessException("system.user.email.is.exits");
		}
		String redisCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_BIND_CAPTCHA_SUFFIX.getCacheNameSuffix() + user.getEmail());
		if (StringUtil.isBlank(redisCode) || StringUtil.isBlank(code)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
			return r;
		}
		if (!StringUtil.equalsIgnoreCase(code, redisCode)) {
			r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
			return r;
		}
		if (StringUtil.isNotBlank(user.getFirstName()) || StringUtil.isNotBlank(user.getLastName())) {
			user.setRealName(user.getFirstName().trim() + " " + user.getLastName().trim());
		}
		// 保存账号和邮箱一致
		user.setAccount(user.getEmail());
		user.setName(user.getEmail());
		CacheUtil.clear(USER_CACHE);
		user.setPassword(null);
		this.updateById(user);
		if (CommonConstant.CLIENT_TENANT_ID.equals(tenantId) || CommonConstant.AGENT_TENANT_ID.equals(tenantId)) {
			this.synchronizeUser4Update(user.getId(), tenantId);
		}
		// app端修改邮箱不踢出登录
		bladeRedis.del(CommonConstant.BLADE_USER_CHANGE_PRE + user.getId());
		bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_BIND_CAPTCHA_SUFFIX.getCacheNameSuffix() + user.getEmail());
		return R.data("操作成功");
	}

	@Override
	public R synchronousUserListForClient(UserBatchVO<User> userUserBatchVO) {
		List<User> addUserList = userUserBatchVO.getAddList();
		List<User> deleteUserList = userUserBatchVO.getDeleteList();
		BladeUser user = AuthUtil.getUser();
		Long updatedUserId = user.getUserId();
		// 解绑映射用户的角色和部门信息
		if (CollectionUtil.isNotEmpty(deleteUserList)) {
			baseMapper.deleteUserListInfoForClient(deleteUserList, updatedUserId);
		}
		// 绑定映射用户角色和部门信息
		if (CollectionUtil.isNotEmpty(addUserList)) {
			baseMapper.insertUserListInfoForClient(addUserList.stream().peek(u -> u.setCreateUser(updatedUserId)).collect(Collectors.toList()));
		}
		// 用户信息变化，需要清除当前登录信息
		if (CollectionUtils.isNotEmpty(addUserList) || CollectionUtils.isNotEmpty(deleteUserList)) {
			List<User> allUserList = new ArrayList<>();
			allUserList.addAll(addUserList);
			allUserList.addAll(deleteUserList);
			List<Long> userIdsList = allUserList.stream().map(User::getId).distinct().collect(Collectors.toList());
			userIdsList.forEach(this::userTokenLogOutById);
		}
		return R.success("success");
	}

	public R<User> userFind(String areaCode, String phoneNumber, String email) {
		User user = null;

		if(phoneNumber != null && !phoneNumber.isBlank()){
			user = baseMapper.userFind(areaCode, phoneNumber, "");
		}
		if(user == null && email != null && !email.isBlank()){
			user = baseMapper.userFind(areaCode, "", email);
		}
		return R.data(user);
	}

//	public boolean updatePlantUser(User user){
//		return baseMapper.updatePlantUser(user);
//	}

//	public boolean isEmailExist(String email){
//		return baseMapper.isEmailExist(email);
//	}
}
