package org.skyworth.ess.utils;

import java.util.Objects;

public enum DictGridChargeEnum {
	used_mode("0000","Disable"),
	Feed_in("0001","Enable");

	private String code;
	private String message;

	private DictGridChargeEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictGridChargeEnum match(String key) {

		DictGridChargeEnum result = null;

		for (DictGridChargeEnum s : values()) {
			if (Objects.equals(s.getCode(), key)) {
				result = s;
				break;
			}
		}

		return result;
	}

}
