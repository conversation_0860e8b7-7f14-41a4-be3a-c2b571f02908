/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 设备软件版本升级记录表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Data
@TableName("device_software_upgrade_record")
@ApiModel(value = "SoftwareUpgradeRecord对象", description = "设备软件版本升级记录表")
@EqualsAndHashCode(callSuper = true)
public class SoftwareUpgradeRecordEntity extends SkyWorthEntity {

	/**
	 * 设备软件版本信息表id
	 */
	@ApiModelProperty(value = "设备软件版本信息表id")
	private Long deviceSoftwareVersionId;
	/**
	 * SN号
	 */
	@ApiModelProperty(value = "SN号")
	private String serialNumber;
	/**
	 * 升级完成时间
	 */
	@ApiModelProperty(value = "升级完成时间")
	private Date upgradeCompletionTime;
	/**
	 * 当前版本
	 */
	@ApiModelProperty(value = "当前版本")
	private String currentVersionNumber;
	/**
	 * 升级版本
	 */
	@ApiModelProperty(value = "升级版本")
	private String latestReleasedVersion;

	/**
	 * 失败原因
	 */
	@ApiModelProperty(value = "失败原因")
	private String failureReason;

	/**
	 * 大类型（device逆变器、battery电池）
	 */
	@ApiModelProperty(value = "大类型（device逆变器、battery电池）")
	private String bigType;
	/**
	 * 小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）
	 */
	@ApiModelProperty(value = "小类（逆变器：master主机软件版本、slave从机固件版本、EMS固件版本、dc固件版本）")
	private String smallType;
	/**
	 * 公司
	 */
	@ApiModelProperty(value = "公司")
	private String company;


	@TableField(exist = false)
	@ApiModelProperty(value = "默认时区")
	private String timeZone;

	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty(value = "站点id")
	@NotNull(message = "can not be empty!")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@TableField(exist = false)
	private Long plantId;

}
