/*
 * Copyright (c) 2023. skyworth All rights reserved
 */

package org.skyworth.ess.ota;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.microsoft.azure.storage.StorageException;
import com.microsoft.azure.storage.blob.CloudBlobContainer;
import com.microsoft.azure.storage.blob.CloudBlockBlob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.skyworth.ess.config.BlobConfig;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.enums.QosEnum;
import org.skyworth.ess.mqtt.MqttClientFactory;
import org.skyworth.ess.ota.feign.IDistributeUpgradeClient;
import org.skyworth.ess.utils.BlobHelper;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.net.URISyntaxException;
import java.security.InvalidKeyException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备软件版本升级地址下发
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-09-22 14:19
 **/
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@Slf4j
public class PublishDeviceUpgrade implements IDistributeUpgradeClient {
	private MqttClientFactory mqttClientFactory;
	private final BlobConfig blobConfig;
	private final BladeRedis bladeRedis;

	@PostMapping(OTA_UPGRADE)
	@Override
	public R<String> deviceUpgrade(@RequestBody List<JSONObject> distributeUpgradeVOList) {
		try {
			for (JSONObject jsonObject : distributeUpgradeVOList) {
				mqttClientFactory.getClient().publish(Constants.OTA_DEVICE_UPGRADE, StringEscapeUtils.unescapeHtml4(jsonObject.toJSONString()), QosEnum.QOS0.getCategory());
			}
		} catch (Exception e) {
			log.error(e.getMessage());
			return R.fail("OTA数据下发失败");
		}
		return R.success("OTA下发成功");
	}

	@Override
	@PostMapping(SOA_ADDRESS)
	public R<Map<String, String>> getSasToken(@RequestBody Map<String, String> map) {
		if (MapUtil.isEmpty(map)) {
			return R.data(null);
		}
		Map<String, String> resultMap = new HashMap<>(map.size());
		map.forEach((key, value) -> {
			try {
				String redisKey = value + key;
				String sasToken = bladeRedis.get(redisKey);
				if (StringUtils.isNotBlank(sasToken)) {
					resultMap.put(key, sasToken);
				} else {
					CloudBlobContainer blobContainer = BlobHelper.getBlobContainer(value.toLowerCase(), blobConfig);
					CloudBlockBlob blob = blobContainer.getBlockBlobReference(key);
					sasToken = BlobHelper.getSasToken(blob, 1, redisKey, bladeRedis);
					resultMap.put(key, sasToken);
				}
			} catch (URISyntaxException | StorageException | InvalidKeyException e) {
				log.error("PublishDeviceUpgrade->getSasToken error:" , e);
				throw new BusinessException("toolkit.sastoken.get.error");
			}
		});
		return R.data(resultMap);
	}

	@Override
	@PostMapping(DELETE_AZURE_ATTACHMENT_ADDRESS)
	@Async("commonThreadPool")
	public void deleteAzureAttachment(@RequestBody Map<String, JSONObject> map) {
		if (MapUtil.isEmpty(map)) {
			return;
		}
		map.forEach((key, value) -> {
			String container = value.getString("container");
			String thumbnailAzureKey = value.getString("thumbnailAzureKey");
			try {
				CloudBlobContainer blobContainer = BlobHelper.getBlobContainer(container.toLowerCase(), blobConfig);
				// 传入要blob的path
				blobContainer.getBlockBlobReference(key).deleteIfExists();
				// 如果存在缩略图，则删除原件+缩略图
				if (StringUtils.isNotBlank(thumbnailAzureKey)) {
					blobContainer.getBlockBlobReference(thumbnailAzureKey).deleteIfExists();
				}
			} catch (URISyntaxException | StorageException e) {
				log.error("PublishDeviceUpgrade->getSasToken error:" , e);
				throw new BusinessException("toolkit.sastoken.get.error");
			}
		});
	}

	@Override
	@PostMapping(DEVICE_OFFLINE)
	public R<String> portableDeviceOffline(List<JSONObject> clientKey) {
		try {
			for (JSONObject jsonObject : clientKey) {
				mqttClientFactory.getClient().publish(Constants.PORTABLE_DEVICE_OFFLINE.replace("$Sn", jsonObject.getString("deviceSn") + "_" + jsonObject.getString("userId")), jsonObject.toString().getBytes(), QosEnum.QOS1.getCategory());
			}
		} catch (Exception e) {
			log.error(e.getMessage());
			return R.fail("设备下线消息下发失败");
		}
		return R.success("设备下线消息下发成功");
	}
}
