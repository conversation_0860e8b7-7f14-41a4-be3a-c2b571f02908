package org.skyworth.ess.device.excel;

import lombok.AllArgsConstructor;
import org.springblade.common.excel.ExcelImportServiceAbstract;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DeviceExitFactoryImportImpl extends ExcelImportServiceAbstract<DeviceExitFactoryInfoExcel> {
	private final IDictBizClient dictBizClient;

	@Override
	public String validateDataEffective(List<DeviceExitFactoryInfoExcel> dataList) {
		Map<String, List<DictBiz>> dictMap = dictBizClient.batchGetList(Arrays.asList("device_company", "device_net_type", "deive_single_third_phase", "device_mode_type", "device_firmware_batch")).getData();
		List<DictBiz> dictData = dictMap.get("device_company");
		List<DictBiz> deviceNetType = dictMap.get("device_net_type");
		List<DictBiz> deviceSingleThirdPhase = dictMap.get("deive_single_third_phase");
		List<DictBiz> deviceModeType = dictMap.get("device_mode_type");
		List<DictBiz> firmwareBatch = dictMap.get("device_firmware_batch");
		boolean allListsNotEmpty = Stream.of(dictData, deviceNetType, deviceSingleThirdPhase, deviceModeType, firmwareBatch).noneMatch(List::isEmpty);
		if (!allListsNotEmpty) {
			return "the company or net type or single third phase not setting in system,please setting in business menu,the code is : device_company, device_net_type,deive_single_third_phase,device_firmware_batch";
		}
		StringBuilder stringBuilder = this.validateCompany(dataList, dictData);
		stringBuilder.append(this.validateNetType(dataList, deviceNetType));
		stringBuilder.append(this.validateDeviceSingleThirdPhase(dataList, deviceSingleThirdPhase));
		stringBuilder.append(this.validateDeviceModeType(dataList, deviceModeType));
		stringBuilder.append(this.validateFirmwareBatch(dataList, firmwareBatch));
		return stringBuilder.toString();
	}

	private StringBuilder validateCompany(List<DeviceExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
		StringBuilder sb = new StringBuilder();
		for (DeviceExitFactoryInfoExcel vo : dataList) {
			String company = vo.getCompany();
			boolean flag = false;
			for (DictBiz biz : dictData) {
				if (biz.getDictValue().equalsIgnoreCase(company)) {
					vo.setCompany(biz.getDictKey());
					flag = true;
					break;
				}
			}
			if (!flag) {
				sb.append(company).append(",");
			}
		}
		if (StringUtils.isNotEmpty(sb)) {
			sb.append(" these company is not setting in business menu,the code is : device_company");
		}
		return sb;
	}

	private StringBuilder validateNetType(List<DeviceExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
		StringBuilder sb = new StringBuilder();
		for (DeviceExitFactoryInfoExcel vo : dataList) {
			String netType = vo.getNetType();
			boolean flag = false;
			for (DictBiz biz : dictData) {
				if (biz.getDictValue().equalsIgnoreCase(netType)) {
					vo.setNetType(biz.getDictKey());
					flag = true;
					break;
				}
			}
			if (!flag) {
				sb.append(netType).append(",");
			}
		}
		if (StringUtils.isNotEmpty(sb)) {
			sb.append(" these net type is not setting in business menu,the code is : device_net_type");
		}
		return sb;
	}

	private StringBuilder validateDeviceSingleThirdPhase(List<DeviceExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
		StringBuilder sb = new StringBuilder();
		for (DeviceExitFactoryInfoExcel vo : dataList) {
			String singleThirdPhase = vo.getSingleThirdPhase();
			boolean flag = false;
			for (DictBiz biz : dictData) {
				if (biz.getDictValue().equalsIgnoreCase(singleThirdPhase)) {
					vo.setSingleThirdPhase(biz.getDictKey());
					flag = true;
					break;
				}
			}
			if (!flag) {
				sb.append(singleThirdPhase).append(",");
			}
		}
		if (StringUtils.isNotEmpty(sb)) {
			sb.append(" these single Third Phase is not setting in business menu,the code is : deive_single_third_phase");
		}
		return sb;
	}

	private StringBuilder validateDeviceModeType(List<DeviceExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
		StringBuilder sb = new StringBuilder();
		for (DeviceExitFactoryInfoExcel vo : dataList) {
			String deviceType = vo.getDeviceType();
			boolean flag = false;
			for (DictBiz biz : dictData) {
				if (biz.getDictValue().equalsIgnoreCase(deviceType)) {
					vo.setDeviceType(biz.getDictKey());
					flag = true;
					break;
				}
			}
			if (!flag) {
				sb.append(deviceType).append(",");
			}
		}
		if (StringUtils.isNotEmpty(sb)) {
			sb.append(" these device type is not setting in business menu,the code is : device_mode_type");
		}
		return sb;
	}

	private StringBuilder validateFirmwareBatch(List<DeviceExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
		StringBuilder sb = new StringBuilder();
		for (DeviceExitFactoryInfoExcel vo : dataList) {
			String firmwareBatch = vo.getFirmwareBatch();
			boolean flag = false;
			for (DictBiz biz : dictData) {
				if (biz.getDictValue().equalsIgnoreCase(firmwareBatch)) {
					vo.setFirmwareBatch(biz.getDictKey());
					flag = true;
					break;
				}
			}
			if (!flag) {
				sb.append(firmwareBatch).append(",");
			}
		}
		if (StringUtils.isNotEmpty(sb)) {
			sb.append(" these firmware batch is not setting in business menu,the code is : device_firmware_batch");
		}
		return sb;
	}
}
