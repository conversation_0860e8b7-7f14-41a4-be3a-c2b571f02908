<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.additionalInfo.mapper.AdditionalInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="additionalInfoResultMap" type="org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity">
        <result column="id" property="id"/>
        <result column="img_biz_key" property="imgBizKey"/>
        <result column="img_remark" property="imgRemark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectAdditionalInfoPage" resultMap="additionalInfoResultMap">
        select * from image_additional_info where is_deleted = 0
    </select>


    <select id="exportAdditionalInfo" resultType="org.skyworth.ess.additionalInfo.excel.AdditionalInfoExcel">
        SELECT * FROM image_additional_info ${ew.customSqlSegment}
    </select>

    <delete id="deleteByBusinessIds">
        delete from image_additional_info iai where iai.img_biz_key in
        <foreach collection="businessIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

</mapper>
