/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 设备/逆变器自定义模式表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@TableName("device_custom_mode")
@ApiModel(value = "DeviceCustomMode对象", description = "设备/逆变器自定义模式表")
@EqualsAndHashCode(callSuper = true)
public class DeviceCustomModeEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 逆变器模式
	 */
	@ApiModelProperty(value = "储能工作模式")
	@TableField(value = "device_mode")
	private String hybridWorkMode;
	/**
	 * 一次/每天
	 */
	@ApiModelProperty(value = "一次/每天")
	private String onceEveryday;
	/**
	 * 充电开始时间
	 */
	@ApiModelProperty(value = "充电开始时间")
	private String chargeStartTime1;
	/**
	 * 充电结束时间
	 */
	@ApiModelProperty(value = "充电结束时间")
	private String chargeEndTime1;
	/**
	 * 放电开始时间
	 */
	@ApiModelProperty(value = "放电开始时间")
	private String dischargeStartTime1;
	/**
	 * 放电结束时间
	 */
	@ApiModelProperty(value = "放电结束时间")
	private String dischargeEndTime1;
	/**
	 * 结束充电容量比例
	 */
	@ApiModelProperty(value = "结束充电容量比例")
	private Integer capacityOfChargerEnd;
	/**
	 * 结束放电容量比例
	 */
	@ApiModelProperty(value = "结束放电容量比例")
	private Integer capacityOfDischargerEnd;
	/**
	 * 最大充电功率
	 */
	@ApiModelProperty(value = "最大充电功率")
	private Integer maximumChargerPower;
	/**
	 * 最大放电功率
	 */
	@ApiModelProperty(value = "最大放电功率")
	private Integer maximumDischargerPower;
	/**
	 * 分时控制开关
	 */
	private String timeBasedControlEnable;

	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "最大充电功率(新)")
	private String chargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束充电容量比例(新)")
	private String chargeEndSocInTime1;

	@ApiModelProperty(value = "最大放电功率(新)")
	private String dischargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束放电容量比例(新)")
	private String dischargeEndSocInTime1;


	private String onceEveryday2;
	private String chargeStartTime2;
	private String chargeEndTime2;
	private String chargePowerInTime2HighWord;
	private String chargeEndSocInTime2;
	private String dischargeStartTime2;
	private String dischargeEndTime2;
	private String dischargePowerInTime2HighWord;
	private String dischargeEndSocInTime2;

	private String onceEveryday3;
	private String chargeStartTime3;
	private String chargeEndTime3;
	private String chargePowerInTime3HighWord;
	private String chargeEndSocInTime3;
	private String dischargeStartTime3;
	private String dischargeEndTime3;
	private String dischargePowerInTime3HighWord;
	private String dischargeEndSocInTime3;
}
