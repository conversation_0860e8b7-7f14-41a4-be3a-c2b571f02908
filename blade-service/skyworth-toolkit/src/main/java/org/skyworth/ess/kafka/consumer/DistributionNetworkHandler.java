package org.skyworth.ess.kafka.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.BinaryConvertUtils2;
import org.skyworth.ess.device.client.*;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.log.service.OriginalDataRecordService;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.mq.kafka.config.KafkaDataHandler;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.system.cache.DictBizCache;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * */
@Component
@Slf4j
@AllArgsConstructor
public class DistributionNetworkHandler implements KafkaDataHandler {

	private BinaryConvertUtils2 binaryConvertUtils;

	private IDeviceLog21Client iDeviceLog21Client;

	private IDeviceLog23Client iDeviceLog23Client;

	private IDeviceLog24Client iDeviceLog24Client;

	private IDeviceIssueBiz deviceIssue;

	private OriginalDataRecordService originalDataRecordService;

	private BladeRedis redis;

	private ITimeZoneDeviceServiceClient timeZoneDeviceServiceClient;

	@Override
	public int handlerData(List<JSONObject> list) {
		// 用于存储已经处理过的key值
		Set<Object> seen = new HashSet<>();
		//过滤重复的数据
		List<JSONObject> filteredList = list.parallelStream()
			.filter(jsonObject -> isUnique(jsonObject, seen))
			.collect(Collectors.toList());
		// 获取站点所属时区
		// 提取 plantIds 并获取对应的时区映射
		List<Long> plantIds = list.stream()
			.map(json -> json.getJSONObject(KafkaDataHandler.LINE))
			.filter(Objects::nonNull)
			.map(line -> line.getLong("plantID"))
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
		Map<String, String> timeZoneMap = timeZoneDeviceServiceClient.getMapFromCacheByPlantIdList(plantIds).getData();
		filteredList.stream().parallel().forEach(jsonObject -> {
			JSONObject data = jsonObject.getJSONObject(LINE);
			long plantId = Long.parseLong(data.getString("plantID"));
			String modbusProtocolVersion = data.getString("commuVersion");
			String deviceSerialNumber = data.getString("deviceSn");
			try{
				Date deviceDateTime = null;
				String content1 = data.getString("content1");
				String content3 = data.getString("content3");
				String content4 = data.getString("content4");
				long timeStamp = data.getLong("timeStamp");
				log.info("DistributionNetworkHandler plantId: {} , deviceSerialNumber :{} ", plantId, deviceSerialNumber);
				if(ValidationUtil.isNotEmpty(plantId) && StringUtils.isNotEmpty(deviceSerialNumber)
						&&ValidationUtil.isNotEmpty(content1)&&ValidationUtil.isNotEmpty(content3)&&ValidationUtil.isNotEmpty(content4)) {
					// 时间转换，转换为标准时区时间
					String timeZone = timeZoneMap.getOrDefault(plantId+"", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
					deviceDateTime =  DateUtil.convertLongTimeZoneForDeviceUpload(timeStamp, timeZone, CommonConstant.COMMON_DEFAULT_TIME_ZONE);
					processDataOne(content1, modbusProtocolVersion, plantId, deviceDateTime, deviceSerialNumber);
					processDataThird(content3, modbusProtocolVersion, plantId, deviceDateTime, deviceSerialNumber);
					processDataFour(content4, modbusProtocolVersion, plantId, deviceDateTime, deviceSerialNumber);
                    String key1=CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSerialNumber+"_v2_1";
					String key3=CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSerialNumber+"_v2_3";
					String key4=CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSerialNumber+"_v2_4";
					String value1 = null;
					String value3 = null;
					String value4 = null;
					int count=120;
					int j=1;
					boolean res=false;
					while (j<count){ //一分钟超时
						value1=redis.get(key1);
						value3=redis.get(key3);
						value4=redis.get(key4);
						if(StringUtils.isEmpty(value1)||StringUtils.isEmpty(value3)||StringUtils.isEmpty(value4)){  //三个其中有一个为空则继续等待
							Thread.sleep(500);
							++j;
						}else {
							res=true;
							break;
						}
					}
					log.info("DistributionNetworkHandler res : {}, value1 : {}, value3 : {}, value4 : {}",res , value1, value3, value4);
					if(res){
						//发送指令给app
						JSONObject appObj=new JSONObject();
						appObj.put("deviceSn", deviceSerialNumber);
						Integer startupByBackstage=1;
						log.info("DistributionNetworkHandler StartupByBackstage");
						//查询开机设置标识
						Map<String,Object> startupByBackstageMap=originalDataRecordService.getStartupByBackstage(plantId,deviceSerialNumber);
						if(ValidationUtil.isNotEmpty(startupByBackstageMap)&&!startupByBackstageMap.isEmpty()){
							startupByBackstage= (Integer) startupByBackstageMap.get("startup_by_backstage");
						}
						//更新为配网成功
						originalDataRecordService.updateWifiAndPlant(plantId,deviceSerialNumber);
						appObj.put("default_param_setting",startupByBackstage);
						appObj.put("topic",Constants.MSG_PUSH_APP);

						appObj.put("inverterKind","");
						log.info("DistributionNetworkHandler device21");
						Device21Entity device21ByPlantId = iDeviceLog21Client.getDevice21ByPlantId(plantId,deviceSerialNumber);
						if (ValidationUtil.isNotEmpty(device21ByPlantId.getDeviceModel())){
							String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE,device21ByPlantId.getDeviceModel());
							appObj.put("inverterKind",value);
						}
						log.info("DistributionNetworkHandler send success_distribution_net : ");
						Map<String, String> stringStringMap = deviceIssue.dataIssueToDevice(appObj);
						log.info("DistributionNetworkHandler send success_distribution_net result : {} ",stringStringMap);
					}
					redis.del(key1);
					redis.del(key3);
					redis.del(key4);

				}
			}catch (Exception e){
				log.warn("脏数据：" + jsonObject.get(LINE));
			}finally {
				redis.del( "device21RedisKey:" + deviceSerialNumber + ":" + plantId);
				log.info("insertBatchDeviceLog21ForNetWork server_distribution_network del redis  result : {} , {}",deviceSerialNumber,plantId);
			}
		});
		return 0;
	}


	private void processDataOne(String content1, String modbusProtocolVersion, long plantId, Date deviceDateTime, String deviceSerialNumber) {
		ThreadPoolCustom.getCustomThreadPool().submit(() -> {
			List<DeviceLog21> deviceLog21List=new ArrayList<>();
			JSONObject result1 = binaryConvertUtils.getData(content1, Constants.ONE, modbusProtocolVersion);
			log.info("DistributionNetworkHandler processDataOne : {} ",result1 );
			if (result1 != null) {
				DeviceLog21 deviceLog21 = JSON.toJavaObject(result1, DeviceLog21.class);
				deviceLog21.setPlantId(plantId);
				deviceLog21.setDeviceDateTime(deviceDateTime);
				deviceLog21.setModbusProtocolVersion(modbusProtocolVersion);
				deviceLog21.setDeviceSerialNumber(deviceSerialNumber);
				Date currentTime = new Date();
				deviceLog21.setCreateTime(currentTime);
				deviceLog21.setUpdateTime(currentTime);
				deviceLog21.setIsDeleted(0);
				deviceLog21.setSynchStatus("Y");
				deviceLog21.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				deviceLog21List.add(deviceLog21);
			}
			if(!deviceLog21List.isEmpty()){
				//插入数据
				iDeviceLog21Client.insertBatchDeviceLog21ForNetWork(deviceLog21List);
			}
      });
	}

	private void processDataThird(String content3, String modbusProtocolVersion, long plantId, Date deviceDateTime, String deviceSerialNumber) {
		ThreadPoolCustom.getCustomThreadPool().submit(() -> {
			List<DeviceLog23> deviceLog23List=new ArrayList<>();
			JSONObject result3 = binaryConvertUtils.getData(content3, Constants.THIRD, modbusProtocolVersion);
			log.info("DistributionNetworkHandler processDataThird : {} ",result3 );
			if (result3 != null) {
				DeviceLog23 deviceLog23 = JSON.toJavaObject(result3, DeviceLog23.class);
				deviceLog23.setPlantId(plantId);
				deviceLog23.setDeviceDateTime(deviceDateTime);
				deviceLog23.setModbusProtocolVersion(modbusProtocolVersion);
				deviceLog23.setDeviceSerialNumber(deviceSerialNumber);
				Date currentTime = new Date();
				deviceLog23.setCreateTime(currentTime);
				deviceLog23.setUpdateTime(currentTime);
				deviceLog23.setIsDeleted(0);
				deviceLog23.setSynchStatus("Y");
				deviceLog23.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				deviceLog23List.add(deviceLog23);
			}
			if(!deviceLog23List.isEmpty()){
				//插入数据
				iDeviceLog23Client.insertBatchDeviceLog23ForNetWork(deviceLog23List);
			}
		});
	}


	private void processDataFour(String content4, String modbusProtocolVersion, long plantId, Date deviceDateTime, String deviceSerialNumber ) {
		ThreadPoolCustom.getCustomThreadPool().submit(() -> {
			List<DeviceLog24Entity> deviceLog24EntityList=new ArrayList<>();
			JSONObject result4 = binaryConvertUtils.getData(content4, Constants.FOUR, modbusProtocolVersion);
			log.info("DistributionNetworkHandler processDataFour : {} ", result4 );
			if (result4 != null) {
				DeviceLog24Entity deviceLog24 = JSON.toJavaObject(result4, DeviceLog24Entity.class);
				deviceLog24.setPlantId(plantId);
				deviceLog24.setDeviceDateTime(deviceDateTime);
				deviceLog24.setModbusProtocolVersion(modbusProtocolVersion);
				deviceLog24.setDeviceSerialNumber(deviceSerialNumber);
				Date currentTime = new Date();
				deviceLog24.setCreateTime(currentTime);
				deviceLog24.setUpdateTime(currentTime);
				deviceLog24.setIsDeleted(0);
				deviceLog24.setSynchStatus("Y");
				deviceLog24.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				deviceLog24EntityList.add(deviceLog24);
			}
			if(!deviceLog24EntityList.isEmpty()){
				//插入数据
				iDeviceLog24Client.insertBatchDeviceLog24ForNetWork(deviceLog24EntityList);
			}
		});
	}


	private static boolean isUnique(JSONObject jsonObject, Set<Object> seen) {
		if (!jsonObject.containsKey(DistributionNetworkHandler.LINE)) {
			return true;
		}

		Object value = jsonObject.get(DistributionNetworkHandler.LINE);

		if (value instanceof JSONObject) {
			JSONObject nestedObject = (JSONObject) value;
			return seen.add(nestedObject.toString());
		} else {
			return seen.add(value);
		}
	}

}
