/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.dto;

import org.skyworth.ess.sku.entity.SkuInventoryManageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SkuInventoryManageDTO extends SkuInventoryManageEntity {
	private static final long serialVersionUID = 1L;

}
