package org.skyworth.ess.addressmap.excel;

import lombok.AllArgsConstructor;
import org.springblade.common.excel.ExcelImportServiceAbstract;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class AddressMapDefinitionImportImpl extends ExcelImportServiceAbstract<AddressMapDefinitionExcel> {
    private final IDictBizClient dictBizClient;
    @Override
    public String validateDataEffective(List<AddressMapDefinitionExcel> dataList) {
		// 暂不做正确性校验，后期可更改
        return "";
    }
}
