package org.springblade.resource.rate;

import cn.hutool.extra.spring.SpringUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.redis.cache.BladeRedis;

import java.time.Duration;

/**
 * <AUTHOR>
 */
public class RateLimiterService {
	private final IPBlacklist ipBlacklist = SpringUtil.getBean(IPBlacklist.class);
	private final BladeRedis bladeRedis = SpringUtil.getBean(BladeRedis.class);
	private final int maxRequests;
	private final long timeWindowMillis;

	public RateLimiterService(int maxRequests, long timeWindowMillis) {
		this.maxRequests = maxRequests;
		this.timeWindowMillis = timeWindowMillis;
	}

	public boolean tryAcquire(String ip) {
		long currentTime = System.currentTimeMillis();
		String requestCountKey = CommonConstant.RESOURCE_REQUEST_COUNT_PREFIX + ip;
		String lastResetTimeKey = requestCountKey + ":lastResetTime";

		// Get the last reset time from Redis
		String lastResetTimeString = bladeRedis.get(lastResetTimeKey);
		long lastResetTime = lastResetTimeString != null ? Long.parseLong(lastResetTimeString) : 0;

		if (currentTime - lastResetTime > timeWindowMillis) {
			// Reset the count and last reset time
			bladeRedis.setEx(requestCountKey, 1, Duration.ofMinutes(5));
			bladeRedis.setEx(lastResetTimeKey, String.valueOf(currentTime), Duration.ofMinutes(5));
			return true;
		}
		// 这里用不了increment指令，因为我们的redis序列化方式不支持
		Integer object = bladeRedis.get(requestCountKey);
		int count = object + 1;
		bladeRedis.set(requestCountKey, count);
		if (count > maxRequests) {
			ipBlacklist.addToBlacklist(ip);
			bladeRedis.del(requestCountKey);
			bladeRedis.del(lastResetTimeKey);
			return false;
		}

		return true;
	}

}
