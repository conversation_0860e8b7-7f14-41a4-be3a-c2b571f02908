client.battery.query.connot.empty=The query criteria cannot be empty.
client.setItem.delete.fail=Delete the sub-configuration item node first!
client.system.error=System Error
client.email.address.error=The email address is incorrect
client.parameter.error.empty=Parameter exception: Parameter cannot be empty
client.parameter.error.record.duplicate=There are duplicate records, please check.
client.parameter.error.record.empty=Please select a record.
client.invert.serial.number.exist=the inverter serial number : %s is exist.
client.plant.name.exist=the plant name : [%s] is exist
client.ota.record.count.error=The total number of selected records does not match the total number of servers
client.plant.change.user.phone.empty=The mobile verification code is empty
client.plant.change.user.phone.error=The mobile verification code you entered is incorrect
client.plant.change.user.email.empty=The email verification code you entered is empty
client.plant.change.user.email.error=The email verification code you entered is incorrect
client.plant.change.user.phone.email.empty=The mobile and email addresses cannot be completely empty
client.plant.change.user.check=The current user password is incorrect
client.plant.change.user.phone.same=This can't proceed if you don't want to change the mobile number of your account.
client.plant.change.user.email.same=This can't proceed if you don't want to change the email of your account.
client.ota.record.upgrade.one=The same SN can only update one type of firmware at a time, and the SN with exception check is as follows:%s
client.email.code.exist=Email verification code has been sent, please check your inbox, if you do not receive please look in the trash or check if it is blocked.
client.please.register.account.first=Please register account.
client.user.email.is.exits=The email address has been registered
client.inverter.sn.exist=The inverter SN is in use and cannot be deleted
client.battery.serial.number.exist=the battery serial number : %s is exist.
client.battery.exit.add.sn.exist=The battery SN has been added in the factory delivery information
client.ota.record.upgrading=The following devices have firmware being upgraded, please check:%s
client.ota.record.already.latest=The firmware selected for upgrade is already the latest version. Please check or refresh the page:%s
client.battery.sn.is.used=The battery SN is in use and cannot be deleted
client.parameter.error.invalidDateFormat=The time format is incorrect
client.inverter.max.parallel.limit=Exceeding the limit on the number of inverters of this model that can be paralleled.
client.inverter.bind.same.parallel=Please add an inverter with the same specifications as the previous inverter.
client.inverter.latest.unbind.limit=When there is only one inverter at the site, the binding cannot be unbound.
client.inverter.unable.unbind.limit=This inverter cannot be unbound.
client.inverter.sn.not.exists=The following inverter serial number does not exist: %s, please check.
client.inverter.sn.has.used=The following inverter has been used: %s, cannot be deleted.
client.battery.sn.not.exists=The following battery serial number does not exist: %s, please check.
client.battery.sn.has.used=The following battery has been used:%s, cannot be deleted.
client.inverter.sn.empty=The inverter serial number cannot be empty.
client.battery.sn.empty=Battery serial number cannot be empty.
client.import.record.not.empty=Import record cannot be empty.

