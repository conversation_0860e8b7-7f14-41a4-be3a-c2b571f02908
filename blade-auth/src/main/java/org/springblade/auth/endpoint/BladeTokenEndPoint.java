/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.auth.endpoint;

import com.wf.captcha.SpecCaptcha;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.core.tool.utils.WebUtil;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.AuthorizationRequest;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpSession;
import java.time.Duration;

import static org.springblade.core.cache.constant.CacheConstant.*;

/**
 * BladeEndPoint
 *
 * <AUTHOR>
 */
@NonDS
@Slf4j
@RestController
@AllArgsConstructor
public class BladeTokenEndPoint {

	private final BladeRedis bladeRedis;
	private final JwtProperties jwtProperties;
	private final ClientDetailsService clientDetailsService;
	private final TokenStore tokenStore;

	/**
	 * 登录页面
	 */
	@GetMapping("/oauth/login")
	public ModelAndView require(ModelAndView model) {
		model.setViewName("login");
		return model;
	}

	/**
	 * 授权页面
	 */
	@GetMapping("/oauth/confirm_access")
	public ModelAndView confirm(HttpSession session, ModelAndView model) {
		Object auth = session.getAttribute("authorizationRequest");
		if (auth != null) {
			AuthorizationRequest authorizationRequest = (AuthorizationRequest) auth;
			model.addObject("client", clientDetailsService.loadClientByClientId(authorizationRequest.getClientId()));
			model.addObject("principal", SecurityContextHolder.getContext().getAuthentication().getPrincipal());
		}
		model.setViewName("confirm");
		return model;
	}

	/**
	 * 用户信息
	 */
	@GetMapping("/oauth/user-info")
	public R<Authentication> currentUser(Authentication authentication) {
		return R.data(authentication);
	}

	/**
	 * 验证码
	 */
	@GetMapping("/oauth/captcha")
	public Kv captcha() {
		SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
		String verCode = specCaptcha.text().toLowerCase();
		String key = StringUtil.randomUUID();
		// 存入redis并设置过期时间为30分钟
		bladeRedis.setEx(CacheNames.CAPTCHA_KEY + key, verCode, Duration.ofMinutes(30));
		// 将key和base64返回给前端
		return Kv.create().set("key", key).set("image", specCaptcha.toBase64());
	}

	/**
	 * 退出登录
	 */
	@GetMapping("/oauth/logout")
	public Kv logout() {
		commonLogout();
		return Kv.create().set("success", "true").set("msg", "success");
	}

	private void commonLogout() {
		BladeUser user = AuthUtil.getUser();
		String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
		// 清空redis保存的token
		if (user != null && jwtProperties.getState()) {
			JwtUtil.removeAccessToken(user.getTenantId(), user.getClientId(), String.valueOf(user.getUserId()), token);
		}
		// 清空资源服务器保存的token
		OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
		OAuth2RefreshToken refreshToken = null;
		if (accessToken != null && StringUtil.isNoneBlank(accessToken.getValue())) {
			refreshToken = accessToken.getRefreshToken();
			tokenStore.removeAccessToken(accessToken);
		}
		if (refreshToken != null && StringUtil.isNoneBlank(refreshToken.getValue())) {
			tokenStore.removeRefreshToken(refreshToken);
		}
	}

	@GetMapping("/app/oauth/logout")
	public R appLogout() {
		commonLogout();
		return R.data("success");
	}

	/**
	 * 缓存清空
	 */
	@GetMapping("/oauth/clear-cache")
	public Kv clearCache() {
		CacheUtil.clear(BIZ_CACHE);
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(DICT_CACHE);
		CacheUtil.clear(FLOW_CACHE);
		CacheUtil.clear(SYS_CACHE);
		CacheUtil.clear(PARAM_CACHE);
		CacheUtil.clear(RESOURCE_CACHE);
		CacheUtil.clear(MENU_CACHE);
		CacheUtil.clear(DICT_CACHE, Boolean.FALSE);
		CacheUtil.clear(MENU_CACHE, Boolean.FALSE);
		CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
		CacheUtil.clear(PARAM_CACHE, Boolean.FALSE);
		return Kv.create().set("success", "true").set("msg", "success");
	}

}
