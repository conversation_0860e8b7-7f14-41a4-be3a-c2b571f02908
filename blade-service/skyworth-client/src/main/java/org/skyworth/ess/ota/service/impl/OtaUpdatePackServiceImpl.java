/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.skyworth.ess.constant.ClientConstant;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.excel.OtaUpdatePackExcel;
import org.skyworth.ess.ota.mapper.OtaUpdatePackMapper;
import org.skyworth.ess.ota.service.IOtaUpdatePackService;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.skyworth.ess.ota.wrapper.OtaUpdatePackWrapper;
import org.skyworth.ess.timeshift.ITimeShiftService;
import org.skyworth.ess.util.TimeZoneUtil;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * OTA升级包 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Service
@RequiredArgsConstructor
public class OtaUpdatePackServiceImpl extends BaseServiceImpl<OtaUpdatePackMapper, OtaUpdatePackEntity> implements IOtaUpdatePackService {
	private final IAttachmentInfoClient attachmentInfoService;
	private final IDictBizClient dictBizClient;
	@Value("${ota.cdn.domain:}")
	private String cdnDomainName;
	@Value("${oss.domestic:false}")
	private boolean ossDomestic;
	private final ITimeShiftService timeShiftService;
	private final TimeZoneUtil timeZoneUtil;

	@Override
	public IPage<OtaUpdatePackEntity> pageList(IPage<OtaUpdatePackEntity> page,
											   QueryWrapper<OtaUpdatePackEntity> queryWrapper) {
		IPage<OtaUpdatePackEntity> pageResult = super.page(page, queryWrapper);
		List<OtaUpdatePackEntity> resultRecords = pageResult.getRecords();
		String timeZone = timeZoneUtil.getUserWebTimeZone();
		if (CollectionUtils.isNotEmpty(resultRecords)) {
			// 获取设备类型
			R<List<DictBiz>> deviceTypeListR = dictBizClient.getList(ClientConstant.OTA_DEVICE_TYPE);
			// 获取公司名称
			R<List<DictBiz>> companyListR = dictBizClient.getList(ClientConstant.OTA_COMPANY);
			Map<String, String> companyMap =
				companyListR.getData().stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
			Map<String, String> deviceTypeMap =
				deviceTypeListR.getData().stream().collect(Collectors.toMap(DictBiz::getDictKey,
					DictBiz::getDictValue));
			List<Long> businessIds =
				resultRecords.stream().map(OtaUpdatePackEntity::getBusinessId).collect(Collectors.toList());
			// 批量查询附件信息
			Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap =
				attachmentInfoService.findByBusinessIds(businessIds).getData();
			// 参数设置
			resultRecords.forEach(a -> {
				a.setTimeZone(timeZone);
				if (StringUtil.isNotBlank(a.getCompany())) {
					a.setCompanyName(companyMap.get(a.getCompany()));
				}
				if (StringUtil.isNotBlank(a.getBigType())) {
					a.setBigTypeName(deviceTypeMap.get(a.getBigType()));
				}
				if (StringUtil.isNotBlank(a.getSmallType())) {
					a.setSmallTypeName(deviceTypeMap.get(a.getSmallType()));
				}
				if (a.getBusinessId() != null) {
					if (ObjUtil.isNotNull(attachmentInfoMap) && !attachmentInfoMap.isEmpty()) {
						a.setAttachmentInfoEntity(attachmentInfoMap.get(a.getBusinessId()) == null ? null :
							attachmentInfoMap.get(a.getBusinessId()).get(0));
					}
				}
			});
			// 时区转换
			timeShiftService.getWebAndConvertTimeZones(resultRecords,timeZone,"createTime");
		}
		return pageResult;
	}


	@Override
	public List<OtaUpdatePackExcel> exportOtaUpdatePack(Wrapper<OtaUpdatePackEntity> queryWrapper) {
		List<OtaUpdatePackExcel> otaUpdatePackList = baseMapper.exportOtaUpdatePack(queryWrapper);
		//otaUpdatePackList.forEach(otaUpdatePack -> {
		//	otaUpdatePack.setTypeName(DictCache.getValue(DictEnum.YES_NO, OtaUpdatePack.getType()));
		//});
		return otaUpdatePackList;
	}

	/**
	 * 1.验证是否重复
	 * 2.修改最新标识
	 * 3.保存ota升级信息
	 * 4.保存附件信息
	 *
	 * @param otaUpdatePackEntity 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/9/28 15:28
	 **/
	@Override
	public Boolean saveAndDealWithAttachment(OtaUpdatePackEntity otaUpdatePackEntity) {
		verifyDuplicateAndUpdateNewVersion(otaUpdatePackEntity);
		AttachmentInfoEntity attachmentInfoEntity = otaUpdatePackEntity.getBatchVO().getAddList().get(0);
		// 设置cdnURL,华为云CDN
		if (ossDomestic) {
			otaUpdatePackEntity.setPackCdnUrl(cdnDomainName + attachmentInfoEntity.getAzureKey());
		}
		// 微软云CDN
		else {
			otaUpdatePackEntity.setPackCdnUrl(cdnDomainName + attachmentInfoEntity.getContainer() + "/" + attachmentInfoEntity.getAzureKey());
		}
		int result = baseMapper.insert(otaUpdatePackEntity);
		attachmentInfoService.saveAndUpdate(otaUpdatePackEntity.getBatchVO());
		return result > 0;
	}

	/**
	 * 1.验证是否重复
	 * 2.修改数据
	 * 3.修改附件
	 * 4.删除服务器附件
	 *
	 * @param otaUpdatePackEntity 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/9/28 15:51
	 **/
	@Override
	public Boolean updateAndDealWithAttachment(OtaUpdatePackEntity otaUpdatePackEntity) {
		//验证是否重复
		verifyDuplicateAndUpdateNewVersion(otaUpdatePackEntity);
		BatchVO<AttachmentInfoEntity> batchVO = otaUpdatePackEntity.getBatchVO();
		// 附件有修改则修改cdn地址,指向最新的附件地址
		if (batchVO != null && CollectionUtils.isNotEmpty(batchVO.getAddList())) {
			AttachmentInfoEntity attachmentInfoEntity = batchVO.getAddList().get(0);
			// 设置cdnURL,华为云CDN
			if (ossDomestic) {
				otaUpdatePackEntity.setPackCdnUrl(cdnDomainName + attachmentInfoEntity.getAzureKey());
			}
			// 微软云CDN
			else {
				otaUpdatePackEntity.setPackCdnUrl(cdnDomainName + attachmentInfoEntity.getContainer() + "/" + attachmentInfoEntity.getAzureKey());
			}
		}
		int result = baseMapper.updateById(otaUpdatePackEntity);
		if (batchVO != null) {
			attachmentInfoService.saveAndUpdate(batchVO);
		}
		return result > 0;
	}

	@Override
	public OtaUpdatePackVO detail(OtaUpdatePackEntity otaUpdatePack) {
		OtaUpdatePackEntity otaUpdatePackEntity = super.getOne(Condition.getQueryWrapper(otaUpdatePack));
		OtaUpdatePackVO otaUpdatePackVO = OtaUpdatePackWrapper.build().entityVO(otaUpdatePackEntity);
		Long businessId = otaUpdatePackVO.getBusinessId();
		AttachmentInfoEntity attachmentInfoEntity =
			attachmentInfoService.findByBusinessIds(Collections.singletonList(businessId)).getData().get(businessId).get(0);
		otaUpdatePackVO.setAttachmentInfoEntity(attachmentInfoEntity);
		return otaUpdatePackVO;
	}

	private void verifyDuplicateAndUpdateNewVersion(OtaUpdatePackEntity otaUpdatePackEntity) {
		String bigType = otaUpdatePackEntity.getBigType();
		String smallType = otaUpdatePackEntity.getSmallType();
		String company = otaUpdatePackEntity.getCompany();
		String version = otaUpdatePackEntity.getVersionNumber();
		String firmwareBatch = otaUpdatePackEntity.getFirmwareBatch();
		String deviceType = otaUpdatePackEntity.getDeviceType();
		if (StringUtil.isAllBlank(bigType, smallType, company, version, firmwareBatch, deviceType)) {
			throw new BusinessException("client.parameter.error.empty");
		}
		LambdaQueryWrapper<OtaUpdatePackEntity> queryWrapper =
			Wrappers.<OtaUpdatePackEntity>query().lambda().eq(OtaUpdatePackEntity::getBigType, bigType)
			.eq(OtaUpdatePackEntity::getSmallType, smallType)
			.eq(OtaUpdatePackEntity::getCompany, company)
			.eq(OtaUpdatePackEntity::getVersionNumber, version)
			.eq(OtaUpdatePackEntity::getIsDeleted, BizConstant.NUMBER_ZERO)
			.eq(OtaUpdatePackEntity::getFirmwareBatch, firmwareBatch)
			.eq(OtaUpdatePackEntity::getDeviceType, deviceType);
		// 修改操作
		Long id = otaUpdatePackEntity.getId();
		if (id != null) {
			queryWrapper.ne(OtaUpdatePackEntity::getId, id);
		}
		long count = baseMapper.selectCount(queryWrapper);
		if (count > BizConstant.NUMBER_ZERO) {
			throw new BusinessException("client.parameter.error.record.duplicate");
		}
		if (BizConstant.NUMBER_ONE.equals(otaUpdatePackEntity.getIsNewVersion())) {
			// 修改最新版本号
			super.update(Wrappers.<OtaUpdatePackEntity>update().lambda().set(OtaUpdatePackEntity::getIsNewVersion,
					BizConstant.NUMBER_ZERO)
				.eq(OtaUpdatePackEntity::getBigType, bigType)
				.eq(OtaUpdatePackEntity::getSmallType, smallType)
				.eq(OtaUpdatePackEntity::getCompany, company)
				.eq(OtaUpdatePackEntity::getFirmwareBatch, firmwareBatch)
				.eq(OtaUpdatePackEntity::getDeviceType, deviceType)
			);
		}
	}

}
