package org.skyworth.ess.app.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppReportDetailVO implements Serializable {

	private static final long serialVersionUID = 1L;
	//  对应前端 展示是 光伏日发电量
	private List<AppReportDataVO> powerGeneration;
	// 前端 对应的展示是 光伏总储电量
	private List<AppReportDataVO> dischargeCapacity;
	// 1048 集合
	private List<AppReportDataVO> pvGenerationList;
	// 2009 集合
	private List<AppReportDataVO> batteryOutputList;
	// 130A + 130C + 130E + 1353 + 1359 + 135E 集合
	private List<AppReportDataVO> powerConsumptionList;
	// 1300 + 1302 + 1304 集合
	private List<AppReportDataVO> gridConsumptionList;
	// 1300 + 1302 + 1304 集合
	private List<AppReportDataVO> feedInGridList;
	// 2009 集合
	private List<AppReportDataVO> batteryInputList;
	// 2000 集合
	private List<AppReportDataVO> batterySocList;
	/* 1、天报表为： L1+L2+L3=1503~1504+1508~1509+150D~150E
	 2、周月年报表为：0x1514~0x1515
	 */
	private List<AppReportDataVO> otherPvList;

}
