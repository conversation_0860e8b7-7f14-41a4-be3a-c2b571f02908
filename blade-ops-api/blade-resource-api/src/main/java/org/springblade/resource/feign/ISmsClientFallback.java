/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.resource.feign;

import org.springblade.core.tool.api.R;
import org.springblade.resource.vo.AttachVO;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 流程远程调用失败处理类
 *
 * <AUTHOR>
 */
@Component
public class ISmsClientFallback implements ISmsClient {
	@Override
	public R sendMessage(String code, String params, String phones) {
		return R.fail("远程调用失败");
	}

	@Override
	public R sendValidate(String code, String phone) {
		return R.fail("远程调用失败");
	}

	@Override
	public R validateMessage(String code, String id, String value, String phone) {
		return R.fail("远程调用失败");
	}

	@Override
	public R<List<AttachVO>> putFiles(String bucketName, MultipartFile[] multipartFile) {
		return R.fail("远程调用失败");
	}

	@Override
	public R<Map<String, String>> getSasToken(Map<String, String> map) {
		return R.fail("远程调用失败");
	}

	@Override
	public R<Boolean> deleteAzureAttachment(Map<String, String> map) {
		return R.fail("远程调用失败");
	}

}
