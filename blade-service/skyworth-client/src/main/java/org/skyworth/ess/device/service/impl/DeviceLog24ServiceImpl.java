/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service.impl;

import cn.hutool.core.util.ObjectUtil;
import org.skyworth.ess.device.entity.DeviceLog24Entity;
import org.skyworth.ess.device.mapper.DeviceLog24Mapper;
import org.skyworth.ess.device.service.IDeviceLog24Service;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * DeviceLog24 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Service
public class DeviceLog24ServiceImpl extends BaseServiceImpl<DeviceLog24Mapper, DeviceLog24Entity> implements IDeviceLog24Service {

	@Resource
	DeviceLog24Mapper deviceLog24Mapper;
	@Override
	public List<DeviceLog24Entity> selectDataByLatestTime() {
		return baseMapper.selectDataByLatestTime();
	}

    @Override
    public HashMap<String, Object> getAllQuickSetup(Long plantId, String deviceSerialNumber) {
		HashMap<String, Object> allQuickSetup = deviceLog24Mapper.getAllQuickSetup(plantId, deviceSerialNumber);
		if (ObjectUtil.isEmpty(allQuickSetup)) {
			allQuickSetup = deviceLog24Mapper.getAllQuickSetupIsDelete(plantId,deviceSerialNumber);
		}
		return allQuickSetup;
    }

//	@Override
//	public IPage<DeviceLog24VO> selectDeviceLog24Page(IPage<DeviceLog24VO> page, DeviceLog24VO DeviceLog24) {
//		return page.setRecords(baseMapper.selectDeviceLog24Page(page, DeviceLog24));
//	}
//
//
//	@Override
//	public List<DeviceLog24Excel> exportDeviceLog24(Wrapper<DeviceLog24Entity> queryWrapper) {
//		List<DeviceLog24Excel> DeviceLog24List = baseMapper.exportDeviceLog24(queryWrapper);
//		//DeviceLog24List.forEach(DeviceLog24 -> {
//		//	DeviceLog24.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceLog24.getType()));
//		//});
//		return DeviceLog24List;
//	}

}
