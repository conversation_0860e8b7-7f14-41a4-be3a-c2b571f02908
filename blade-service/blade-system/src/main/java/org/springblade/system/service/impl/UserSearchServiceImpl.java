/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.plant.feign.IPlantClient;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.system.entity.User;
import org.springblade.system.mapper.UserMapper;
import org.springblade.system.service.IUserSearchService;
import org.springblade.system.vo.UserRegistrationVO;
import org.springblade.system.vo.UserStatisticsVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用户查询服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserSearchServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserSearchService {
	private IPlantClient plantClient;

	@Override
	public List<User> listByUser(List<Long> userId) {
		return this.list(Wrappers.<User>lambdaQuery().in(User::getId, userId));
	}

	@Override
	public List<User> listByDept(List<Long> deptId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		deptId.forEach(id -> queryWrapper.like(User::getDeptId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByPost(List<Long> postId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		postId.forEach(id -> queryWrapper.like(User::getPostId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByRole(List<Long> roleId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		roleId.forEach(id -> queryWrapper.like(User::getRoleId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listAllByUser(List<Long> userId) {
		return baseMapper.listAllByUser(userId);
	}

	@Override
	public List<UserStatisticsVO> singleDayRegisterInfo(Map<String, Object> map) {
		return baseMapper.singleDayRegisterInfo(map);
	}

	@Override
	public List<UserRegistrationVO> singleDayRegisterExcelInfo(Map<String, Object> map) {
		return baseMapper.singleDayRegisterExcelInfo(map);
	}

	@Override
	public List<User> selectMappingUser(List<Long> userIds) {
		return baseMapper.selectMappingUser(userIds);
	}

	@Override
	public List<User> selectMappingSourceUser(List<Long> userIds) {
		return baseMapper.selectMappingSourceUser(userIds);
	}

	@Override
	public List<User> selectUserByPhone(String phone) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.likeRight(User::getPhone, phone);
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listTenantUserByDept(Long deptId, Long plantId, String tenantId) {
		List<Long> userId = plantClient.queryUnauthorizedUser(plantId).getData();
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(User::getTenantId, tenantId);
		queryWrapper.like(User::getDeptId, deptId);
		if (CollectionUtils.isNotEmpty(userId)) {
			queryWrapper.notIn(User::getId, userId);
		}
		queryWrapper.orderByDesc(User::getId);
		return this.list(queryWrapper);
	}

}
