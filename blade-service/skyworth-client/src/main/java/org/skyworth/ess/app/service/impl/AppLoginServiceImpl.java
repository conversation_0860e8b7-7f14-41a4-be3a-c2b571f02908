package org.skyworth.ess.app.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.EmailCacheNamesEnum;
import org.springblade.common.mail.SendMail;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.RandomType;
import org.springblade.core.tool.utils.RegexUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.IUserClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年6月20日 10:40:27
 */
@Slf4j
@AllArgsConstructor
@Service
public class AppLoginServiceImpl {

	@Resource
	private BladeRedis bladeRedis;
	@Resource
	private SendMail sendMail;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private IUserClient userClient;

	public R<String> getVerificationCode(String email, String type, String language) {
		Pattern pattern = Pattern.compile(RegexUtil.EMAIL);
		if (!pattern.matcher(email).matches()) {
			throw new BusinessException("client.email.address.error");
		}
		// 获取验证码
		String redisCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.getCacheNameSuffix(type) + email);
		if (StringUtil.isNotBlank(redisCode)) {
			log.info("the redisCode is exist : {}", redisCode);
			throw new BusinessException("client.email.code.exist");
		}
		//	 忘记密码、删除、登陆时  验证用户是否存在，不存在则提示用户注册账户
		User queryUser = new User();
		queryUser.setEmail(email);
		R<User> userR = userClient.userByUserInfo(queryUser);
		if (type.equals(EmailCacheNamesEnum.EMAIL_FORGET_PASSWORD_CAPTCHA_SUFFIX.getType()) || type.equals(EmailCacheNamesEnum.EMAIL_DELETE_CAPTCHA_SUFFIX.getType())
			|| type.equals(EmailCacheNamesEnum.EMAIL_LOGIN_CAPTCHA_SUFFIX.getType())) {
			if (userR.getCode() != 200) {
				throw new BusinessException("client.please.register.account.first");
			}
		} else if (type.equals(EmailCacheNamesEnum.EMAIL_REGISTER_CAPTCHA_SUFFIX.getType()) || type.equals(EmailCacheNamesEnum.EMAIL_BIND_CAPTCHA_SUFFIX.getType())) {
			if (userR.getCode() == 200) {
				throw new BusinessException("client.user.email.is.exits");
			}
		}

		String verCode = StringUtil.random(6, RandomType.INT);
		// 数据字典
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<List<DictBiz>> mailContent = dictBizClient.getListByLang("mail_content", currentLanguage);
		List<DictBiz> dictData = mailContent.getData();
		String text;
		if (CollectionUtil.isNotEmpty(dictData)) {
			DictBiz dictBiz = dictData.get(0);
			text = dictBiz.getDictValue();
			// 处理电站转移的情况
			if (type.equals(EmailCacheNamesEnum.EMAIL_PLANT_CHANGE_USER.getType())) {
				if (userR.getCode() != 200) {
					// 用户不存在时，采用新的模板，否则使用默认模板
					Optional<DictBiz> first = dictData.stream()
						.filter(x -> type.equals(x.getAttribute1())).findFirst();
					if (first.isPresent()) {
						text = first.get().getDictValue();
					}
				}
			}
			String replace = text.replace("{rn}", "<br>");
			text = replace.replace("{verCode}", verCode);
		} else {
			text = verCode;
		}

		boolean flag = false;
		// 邮箱验证码
		if (ValidationUtil.isNotEmpty(type)) {
			flag = sendMail.sendMail(text, email);
			log.info("send mail flag : {}", flag);
			if (flag) {
				// 存入redis并设置过期时间为10分钟
				bladeRedis.setEx(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.getCacheNameSuffix(type) + email, verCode, Duration.ofMinutes(10));
				return R.success("");
			}
		}
		return R.fail("");
	}
}
