package org.skyworth.ess.app.service;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.aspect.AdvancedSettingsOperationLog;
import org.skyworth.ess.constant.SourceEnum;
import org.springblade.core.tool.api.R;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/11/6 11:04:14
 */
public interface IAppSetupService {

	R issueSetup(AppAdvancedSetup deviceAdvancedSetup);


	JSONObject getAdvancedPwd();

	JSONObject getSetupItem(AppSetRequestVO appSetRequestVO);

	@AdvancedSettingsOperationLog(type = SourceEnum.OTHER)
	R issueSetupByAutoAdjust(AppAdvancedSetup deviceAdvancedSetup);

	R setTimeZone(AppAdvancedSetup deviceAdvancedSetup);
}
