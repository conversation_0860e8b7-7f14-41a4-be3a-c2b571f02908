package org.skyworth.ess.fegin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.client.IWifiStickPlantClient;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.mapper.WifiStickPlantMapper;
import org.skyworth.ess.plant.service.IPlantService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;

@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class WifiStickPlantClient extends BaseServiceImpl<WifiStickPlantMapper, WifiStickPlantEntity> implements IWifiStickPlantClient {

	private IDevice21Service device21Service;
	private IPlantService plantService;
	private BladeRedis redis;
	private RedisLockClient redisLockClient;

    @PostMapping(address)
    @Override
    public void updateHeartStatus(@RequestBody List<String> wifiSnList) {

			LambdaQueryWrapper<WifiStickPlantEntity> eq = Wrappers.<WifiStickPlantEntity>query().lambda()
				.in(WifiStickPlantEntity::getDeviceSerialNumber,wifiSnList).eq(WifiStickPlantEntity::getIsDeleted,0) ;
			List<WifiStickPlantEntity> stickPlantEntityList=baseMapper.selectList(eq);
			if(ValidationUtil.isNotEmpty(stickPlantEntityList)&&!stickPlantEntityList.isEmpty()){
				stickPlantEntityList.stream().parallel().forEach(wifiStickPlant->{
					String currentTime=TimeUtils.getCurrentTime();
					wifiStickPlant.setWifiStickStatus(BizConstant.CLIENT_WIFI_STICK_STATUS_ONLINE);
					wifiStickPlant.setHeartBeatTime(currentTime);
					wifiStickPlant.setUpdateTime(new Date());
					//更新心跳时间
					baseMapper.updateById(wifiStickPlant);
					//更新逆变器状态
					device21Service.updateByDeviceId(wifiStickPlant.getDeviceSerialNumber(),"1");
					//更新站点状态
					plantService.updateStatusById(wifiStickPlant.getPlantId(),"1",null,null);
				});
			}

    }
}
