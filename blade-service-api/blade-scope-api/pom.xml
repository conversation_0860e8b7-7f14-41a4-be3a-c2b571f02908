<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>blade-service-api</artifactId>
        <groupId>org.springblade</groupId>
        <version>3.1.1.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>blade-scope-api</artifactId>
    <name>${project.artifactId}</name>
    <version>${bladex.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-secure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-datascope</artifactId>
        </dependency>
    </dependencies>

</project>
