package org.skyworth.ess.operationlog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.vo.AdvancedSettingsOperationLogVO;
import org.springblade.core.mp.base.BaseService;

import java.util.Map;

/**
 * 高级设置操作日志 服务类
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public interface IAdvancedSettingsOperationLogService extends BaseService<AdvancedSettingsOperationLogEntity> {

	/**
	 * app获取高级设置操作日志列表
	 *
	 * @param page 分页参数
	 * @param map  查询参数
	 * @return 高级设置操作日志列表
	 */
	IPage<AdvancedSettingsOperationLogVO> getLogList(IPage<AdvancedSettingsOperationLogVO> page,
													 Map<String, Object> map);

	/**
	 * web获取高级设置操作日志列表
	 *
	 * @param page 分页参数
	 * @param map  查询参数
	 * @return 高级设置操作日志列表
	 */
	IPage<AdvancedSettingsOperationLogVO> getWebLogList(IPage<AdvancedSettingsOperationLogVO> page,
														Map<String, Object> map);
}
