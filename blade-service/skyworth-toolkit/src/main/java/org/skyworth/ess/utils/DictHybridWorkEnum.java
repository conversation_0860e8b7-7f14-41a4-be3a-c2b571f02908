package org.skyworth.ess.utils;

public enum DictHybridWorkEnum {
	used_mode("0000","Self used mode"),
	Feed_in("0001","Feed-in priority mode"),
	Time_based("0002","Time-based control"),
	Back_up("0003","Back-up mode"),
	discharge("0004","battery discharge mode");
	private String code;
	private String message;

	private DictHybridWorkEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictHybridWorkEnum match(String key) {

		DictHybridWorkEnum result = null;

		for (DictHybridWorkEnum s : values()) {
			if (s.getCode().equals(key)) {
				result = s;
				break;
			}
		}

		return result;
	}

}
