<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.event.mapper.ImportantEventMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ImportantEventResultMap" type="org.skyworth.ess.event.entity.ImportantEventEntity">
        <result column="id" property="id"/>
        <result column="event_type" property="eventType"/>
        <result column="plant_id" property="plantId"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="event_date" property="eventDate"/>
        <result column="event_content" property="eventContent"/>
        <result column="event_remark" property="eventRemark"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectImportantEventPage" resultType="org.skyworth.ess.event.vo.ImportantEventVO">
        select id, event_type, plant_id, serial_number, event_date, event_content, event_remark, create_user_account,
        update_user_account, tenant_id, create_user, create_dept, create_time, update_user,
        update_time, status, is_deleted from important_event
        <where>
            is_deleted = 0
            <if test="ImportantEvent.serialNumber!=null and ImportantEvent.serialNumber!=''">
                and serial_number = #{ImportantEvent.serialNumber}
            </if>
            <if test="ImportantEvent.plantId!=null and ImportantEvent.plantId!=''">
                and plant_id = #{ImportantEvent.plantId}
            </if>
            <if test="ImportantEvent.eventType!=null and ImportantEvent.eventType!=''">
                and event_type = #{ImportantEvent.eventType}
            </if>
        </where>
        order by event_date desc
    </select>


    <select id="exportImportantEvent" resultType="org.skyworth.ess.event.excel.ImportantEventExcel">
        SELECT * FROM important_event ${ew.customSqlSegment}
    </select>

</mapper>
