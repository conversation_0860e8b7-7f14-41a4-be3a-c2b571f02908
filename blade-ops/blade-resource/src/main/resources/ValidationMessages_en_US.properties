system.error=system error.
resource.the.same.phone.number.exceeds.frequency.limit=You have sent SMS verification codes too many times.Please try again 1 hour later.
resource.please.register.account.first=This account doesn't exist, please register.
resource.the.sms.template.is.not.exist=the sms template is not exist.
resource.the.phone.is.wrong=The Phone is wrong
resource.phone.number.format.is.incorrect=Phone number format is incorrect, please confirm.
resource.number.is.exists=The current resource number already exists!
resource.not.found.storage.config=The corresponding object storage configuration was not obtained.
resource.not.found.sms.config=The corresponding SMS configuration was not obtained.
resource.file.upload.exception=File upload exception,error: %s
resource.bucketname.is.illegal=The bucket name is invalid. Procedure
resource.upload.thumbnail.exception=Abnormal uploading thumbnail,error: %s
resource.sastoken.get.exception=SmsClient->An exception occurred while obtaining SasToken.
resource.sms.phone.not.in.area=The mobile phone number does not belong to the area where SMS messages can be sent.
resource.user.phone.is.exits=The Phone already existed.
