/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.aspect.FileSave;
import org.skyworth.ess.aspect.OperationLog;
import org.skyworth.ess.company.entity.AgentEntity;
import org.skyworth.ess.company.fegin.AgentClientBiz;
import org.skyworth.ess.company.service.IAgentService;
import org.skyworth.ess.company.utils.AgentUtil;
import org.skyworth.ess.company.vo.AgentListVO;
import org.skyworth.ess.constant.ModelEnum;
import org.skyworth.ess.constant.OperateEnum;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * 代理商公司信息表 控制器
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("agentCompany")
@Api(value = "代理商公司信息表", tags = "代理商公司信息表接口")
@Validated
public class AgentController extends BladeController {

	private final IAgentService agentService;

	private final AgentClientBiz agentClient;

	/**
	 * 代理商公司信息表 分页
	 *
	 * @param agentInfo 查询条件
	 * @param query     入参
	 * @return R<IPage < AgentListVO>>
	 * <AUTHOR>
	 * @since 2023/11/14 14:11
	 **/
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "agentInfo")
	@PreAuth("hasPermission('client:agentCompany:list')")
	public R<IPage<AgentListVO>> getAgentList(@ApiIgnore @RequestParam Map<String, Object> agentInfo, Query query) {
		String companyName = AgentUtil.getParameter(agentInfo, "companyName");
		String agentArea = AgentUtil.getParameter(agentInfo, "agentArea");
		Integer agentAreaInt = null;
		if (StringUtils.isNotEmpty(agentArea)) {
			agentAreaInt = Integer.parseInt(agentArea);
		}
		IPage<AgentListVO> pages = agentService.getAgentList(Condition.getPage(query), agentAreaInt, companyName);
		return R.data(pages);
	}

	/**
	 * 代理商公司信息表新增
	 *
	 * @param agentEntity 入参
	 * @return R<Boolean>
	 * <AUTHOR>
	 * @since 2023/11/14 14:11
	 **/
	@PostMapping("/save")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "新增代理商", notes = "传入agentEntity")
	@OperationLog(operate = OperateEnum.ADD, model = ModelEnum.AGENT_OPERATE, businessIdFiled = "companyInfo.id")
	@FileSave
	@PreAuth("hasPermission('client:agentCompany:save')")
	public R<Boolean> save(@Validated @RequestBody AgentEntity agentEntity) {
		return R.status(agentService.saveAgent(agentEntity));
	}

	/**
	 * 代理商公司信息表修改
	 *
	 * @param agentEntity 入参
	 * @return R<Boolean>
	 * <AUTHOR>
	 * @since 2023/11/14 14:11
	 **/
	@PostMapping("/update")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "修改代理商", notes = "传入agentEntity")
	@OperationLog(operate = OperateEnum.EDIT, model = ModelEnum.AGENT_OPERATE, businessIdFiled = "companyInfo.id")
	@FileSave
	@PreAuth("hasPermission('client:agentCompany:update')")
	public R<Boolean> update(@Validated @RequestBody AgentEntity agentEntity) {
		return R.status(agentService.update(agentEntity));
	}

	/**
	 * 批量删除
	 *
	 * @param map 入参
	 * @return R<Boolean>
	 * <AUTHOR>
	 * @since 2023/11/14 14:11
	 **/
	@PostMapping("/deleteBatch")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@OperationLog(operate = OperateEnum.DELETE, model = ModelEnum.AGENT_OPERATE)
	@PreAuth("hasPermission('client:agentCompany:deleteBatch')")
	public R<Boolean> deleteBatch(@ApiParam(value = "主键集合", required = true) @RequestBody Map<String, String> map) {
		if (map.isEmpty()) {
			throw new BusinessException("agent.parameter.notEmpty");
		}
		String ids = map.get("ids");
		if (StringUtils.isBlank(ids)) {
			throw new BusinessException("agent.parameter.notEmpty");
		}
		return R.status(agentService.deleteBatch(Func.toLongList(ids)));
	}

	/**
	 * 查询详情
	 *
	 * @param id 入参
	 * @return R<AgentEntity>
	 * <AUTHOR>
	 * @since 2023/11/14 14:10
	 **/
	@GetMapping("/detail")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "查询详情", notes = "传入代理商id")
	@PreAuth("hasPermission('client:agentCompany:detail')")
	public R<AgentEntity> detail(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		if (id == null) {
			throw new BusinessException("agent.parameter.notEmpty");
		}
		return R.data(agentService.detail(id));
	}

	/**
	 * 根据代理商编号查询代理商信息
	 */
	@GetMapping("/getAgentInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据代理商编号查询代理商信息", notes = "传入agentNumber")
	public R<AgentCompanyVO> getAgentInfo(@Valid @RequestParam("orderNumber") String agentNumber) {
		return agentClient.agentNumber(agentNumber);
	}
}
