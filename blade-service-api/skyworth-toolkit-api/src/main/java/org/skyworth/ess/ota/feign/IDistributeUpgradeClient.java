package org.skyworth.ess.ota.feign;


import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.ota.constant.ToolkitConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;


@FeignClient(
	value = ToolkitConstant.APPLICATION_TOOLKIT_NAME,
	fallback = DistributeUpgradeClientCallBack.class
)
public interface IDistributeUpgradeClient {

	String API_PREFIX_OTA = "/ota";
	String API_PREFIX_FILE = "/attachment";
	String API_PREFIX_PORTABLE = "/portable";
	String OTA_UPGRADE = API_PREFIX_OTA + "/deviceUpgrade";
	String SOA_ADDRESS = API_PREFIX_FILE + "/getSoaToken";
	String DELETE_AZURE_ATTACHMENT_ADDRESS = API_PREFIX_FILE + "/delete";
	String DEVICE_OFFLINE = API_PREFIX_PORTABLE + "/offline";

	/**
	 * OTA升级
	 *
	 * @param distributeUpgradeVOList 入参
	 * @return R<String>
	 * <AUTHOR>
	 * @since 2024/1/25 10:19
	 **/
	@PostMapping(OTA_UPGRADE)
	R<String> deviceUpgrade(@RequestBody List<JSONObject> distributeUpgradeVOList);

	/**
	 * 获取文件签名
	 *
	 * @param map 入参
	 * @return R<Map < String, String>>
	 * <AUTHOR>
	 * @since 2024/1/25 10:19
	 **/
	@PostMapping(SOA_ADDRESS)
	R<Map<String, String>> getSasToken(@RequestBody Map<String, String> map);

	/**
	 * 删除微软云附件
	 *
	 * @param map 入参
	 * <AUTHOR>
	 * @since 2024/1/25 10:19
	 **/
	@PostMapping(DELETE_AZURE_ATTACHMENT_ADDRESS)
	void deleteAzureAttachment(@RequestBody Map<String, JSONObject> map);

	/**
	 * 便携设备被其他用户蓝牙联通，需要踢掉当前在线用户
	 *
	 * @param clientKey 入参
	 * @return R<String>
	 * <AUTHOR>
	 * @since 2024/1/25 10:18
	 **/
	@PostMapping(DEVICE_OFFLINE)
	R<String> portableDeviceOffline(@RequestBody List<JSONObject> clientKey);

}
