package org.skyworth.ess.remark.vo;

import org.skyworth.ess.remark.entity.RemarkHistoryEntity;
import org.springblade.core.tool.node.INode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 站点评论记录表 视图实体类
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RemarkHistoryVO extends RemarkHistoryEntity {
	private static final long serialVersionUID = 1L;

}
