package org.skyworth.ess.util;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
public enum EveryDayPowerAndEnergyEnum {


	/**
	 * 逆变器文件名
	 * */
	INVERTER_EVERY_DAY_POWER("inverterPowerFileName","Inverter_Everyday_Power","Wechselrichter Alltagsleistung"),
	Inverter_Everyday_Energy("inverterEnergyFileName","Inverter_Everyday_Energy","Wechselrichter Alltagsenergie"),
	Battery_Everyday_Energy("BatteryEnergyFileName","Battery Everyday Energy","Batterie Alltagsenergie"),
	BATTERY_EVERY_DAY_POWER("batteryPowerFileName","Battery Everyday Power","Batterie Alltagsleistung"),
    GRID("grid","Grid","Gitter"),
	LOAD("load","Load","Last"),
	BACKUP("backup","Backup","Backup"),
	BatteryPower("batteryPower","Battery Power","Batterieleistung"),
	Voltage("voltage","Voltage(V)","Spannung(V)"),
	Current("current","Current(A)","Strom(A)"),
	BatterySOC("batterySOC","Battery SOC","Batterie SOC"),
	BatteryMaximumCellVoltage("batteryMaximumCellVoltage","Battery Maximum Cell Voltage(V)","Batterie Maximale Zellspannung(V)"),
	batteryMinimumCellVoltage("batteryMinimumCellVoltage","Battery Minimum Cell Voltage(V)","Batterie Minimum Cell Voltage(V)"),
	batteryMaximumCellTemperature("batteryMaximumCellTemperature","Battery Maximum Cell Temperature(℃)","Maximale Zellentemperatur der Batterie(℃)"),
	batteryMinimumCellTemperature("batteryMinimumCellTemperature","Battery Minimum Cell Temperature(℃)","Mindest Zellentemperatur  der Batterie(℃)"),
	batteryVoltageAndCurrent("batteryVoltageAndCurrent","Battery Voltage and Current","Batteriespannung und -strom"),
	batteryCellVoltage("batteryCellVoltage","Battery Cell Voltage","Spannung der Batteriezelle"),
	batteryCellTemperature("batteryCellTemperature","Battery Cell Temperature","Batteriezellentemperatur"),
	Date("date","Date","Datum"),
	everydayEnergy("everydayEnergy","Everyday Energy","Energie des täglichen Lebens"),
	batteryDailyChargeEnergy("batteryDailyChargeEnergy","Battery Daily Charge Energy(kWh)","Tägliche Ladeenergie der Batterie (kWh)"),
	batteryDailyDischargeEnergy("batteryDailyDischargeEnergy","Battery Daily Discharge Energy(kWh)","Tägliche Entladungsenergie der Batterie (kWh)"),
	PVGeneration("PVGeneration","PV Generation(kWh)","PV-Erzeugung(kWh)"),
	gridConsumption("gridConsumption","Grid Consumption(kWh)","Stromnetzverbrauch(kWh)"),
	FeedInGrid("FeedInGrid","Feed-in Grid(kWh)","Einspeisung Stromnetz(kWh)"),
	loadConsumption("loadConsumption","Load Consumption(kWh)","Lastverbrauch(kWh)"),
	backupConsumption("backupConsumption","Backup Consumption(kWh)","Backup-Verbrauch(kWh)"),
	inverter("inverter","inverter","Wechselrichter"),
	battery("battery","battery","Batterie"),

	;

	private String code;

	private String enMessage;

	private String deMessage;

	EveryDayPowerAndEnergyEnum(String code, String enMessage, String deMessage) {
		this.code = code;
		this.enMessage = enMessage;
		this.deMessage = deMessage;
	}

	public static String matchLanguage(String language,String key) {
		String result = "";

		for (EveryDayPowerAndEnergyEnum s : values()) {
			if (Objects.equals(s.getCode(), key)) {
				if("de".equals(language)){
					result = s.getDeMessage();
				}else {
					result = s.getEnMessage();
				}
				break;
			}
		}
		return result;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setEnMessage(String enMessage) {
		this.enMessage = enMessage;
	}

	public void setDeMessage(String deMessage) {
		this.deMessage = deMessage;
	}
}
