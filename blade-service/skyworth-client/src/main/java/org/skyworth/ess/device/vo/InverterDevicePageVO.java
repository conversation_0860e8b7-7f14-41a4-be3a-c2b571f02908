package org.skyworth.ess.device.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/9/21 10:47:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "InverterDevicePageVO对象", description = "设备/逆变器分页数据查询对象")
public class InverterDevicePageVO extends AddressInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "是否删除 0: 未删除 1: 已删除")
	private String deleteFlag;

	@ApiModelProperty(value = "设备SN")
	private String deviceSerialNumber;
	@ApiModelProperty(value = "创建人、用户ID")
	private Long createdBy;
	@ApiModelProperty(value = "创建人、用户名称")
	private String createdByName;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	@ApiModelProperty(value = "站点名称")
	private String plantName;
	@ApiModelProperty(value = "额定功率")
	private BigDecimal ratedPower;
	@ApiModelProperty(value = "设备状态")
	private String deviceStatus;
	@ApiModelProperty(value = "安装团队")
	private String installTeam;
	@ApiModelProperty(value = "运维团队")
	private String operationTeam;
	@ApiModelProperty(value = "安装时间")
	private LocalDateTime installDate;
	@ApiModelProperty(value = "手机和区号")
	private String userPhoneAndAreaCode;

	@ApiModelProperty("运维团队")
	private String operationCompanyName;
	@ApiModelProperty("运维人员")
	private String operationUserName;
	@ApiModelProperty("运维团队Id")
	private List<Long> operationCompanyIds;
	@ApiModelProperty("运维人员Id")
	private List<Long> operationUserIds;
	@ApiModelProperty("运维团队id")
	private Long operationCompanyId;
	@ApiModelProperty("运维人员id")
	private Long operationUserId;
	@ApiModelProperty("部门id")
	private String deptId;

	@ApiModelProperty(value = "设备/逆变器种类")
	@TableField(exist = false)
	private String inverterKind;

	@ApiModelProperty(value = "设备/逆变器相数")
	@TableField(exist = false)
	private String gridPhaseNumber;

	@ApiModelProperty(value = "运维团队部门id")
	@TableField(exist = false)
	private Long operationCompanyDeptId;


	@ApiModelProperty(value = "查询用户id")
	@TableField(exist = false)
	private List<Long> queryUserIds;

	@ApiModelProperty(value = "查询用户手机号码")
	@TableField(exist = false)
	private String phone;

	@ApiModelProperty(value = "创建用户id")
	@TableField(exist = false)
	private List<Long> createUserIdList;

	@ApiModelProperty(value = "安装商用户id")
	@TableField(exist = false)
	private List<Long> operateUserIdList;

	public Long getOperationCompanyDeptId() {
		if(this.operationCompanyDeptId != null){
			return this.operationCompanyDeptId ;
		}
		return this.getOperationCompanyId();
	}

}
