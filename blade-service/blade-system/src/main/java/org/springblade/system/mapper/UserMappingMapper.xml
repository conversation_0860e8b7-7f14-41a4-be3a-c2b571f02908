<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.UserMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userMappingResultMap" type="org.springblade.system.entity.UserMappingEntity">
        <result column="id" property="id"/>
        <result column="source_user_id" property="sourceUserId"/>
        <result column="mapping_user_id" property="mappingUserId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectUserMappingPage" resultMap="userMappingResultMap">
        select * from blade_user_mapping where is_deleted = 0
    </select>

    <select id="getUserMappingIgnoreTenant" resultMap="userMappingResultMap">
        select * from blade_user_mapping where is_deleted = 0
        <if test="params.sourceUserId != null">
            and source_user_id = #{params.sourceUserId}
        </if>
        <if test="params.mappingUserId != null">
            and mapping_user_id = #{params.mappingUserId}
        </if>
    </select>

    <select id="batchGetUserMappingIgnoreTenant" resultMap="userMappingResultMap">
        <foreach collection="list" item="params" separator="union all">
            select * from blade_user_mapping where is_deleted = 0
            <if test="params.sourceUserId != null">
                and source_user_id = #{params.sourceUserId}
            </if>
            <if test="params.mappingUserId != null">
                and mapping_user_id = #{params.mappingUserId}
            </if>
        </foreach>
    </select>
    <update id="batchDeleteUserMappingIgnoreTenant">
        update blade_user_mapping set is_deleted=1, update_time=now() where is_deleted = 0 and tenant_id = #{tenantId}
        and id in
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
