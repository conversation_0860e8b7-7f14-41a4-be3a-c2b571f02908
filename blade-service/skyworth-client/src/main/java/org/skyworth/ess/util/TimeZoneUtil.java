package org.skyworth.ess.util;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 时区工具类
 *
 * @ClassName TimeZoneUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/25 17:53
 */
@Component
public class TimeZoneUtil {

	@Resource
	private BladeRedis bladeRedis;

	/**
	 * 从redis获取当前用户时区
	 *
	 * @return 用户时区
	 */
	public String getUserWebTimeZone() {
		BladeUser userAuth = AuthUtil.getUser();
		String timeZone = bladeRedis.get(CommonConstant.WEB_TIME_ZONE_FOR_USER_PRE + userAuth.getUserId());
		if (StringUtil.isBlank(timeZone)) {
			timeZone = CommonConstant.COMMON_DEFAULT_TIME_DE_ZONE;
		}
		return timeZone;
	}

	/**
	 * 移除时区中的字母
	 *
	 * @param timeZone 时区
	 * @return 移除字母后的时区
	 */
	public String removeTimeZoneLetters(String timeZone) {
		if (StringUtil.isBlank(timeZone)) {
			return "";
		}
		return timeZone.toUpperCase().replace(CommonConstant.COMMON_TIME_ZONE_UTC, "");
	}
}
