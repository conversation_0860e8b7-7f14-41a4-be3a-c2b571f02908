<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceEverydayTotalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceEverydayTotalResultMap" type="org.skyworth.ess.device.entity.DeviceEverydayTotalEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="total_date" property="totalDate"/>
        <result column="today_energy" property="todayEnergy"/>
        <result column="today_import_energy" property="todayImportEnergy"/>
        <result column="today_export_energy" property="todayExportEnergy"/>
        <result column="today_load_energy" property="todayLoadEnergy"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectDeviceEverydayTotalPage" resultMap="deviceEverydayTotalResultMap">
        select * from device_everyday_total where is_deleted = 0
    </select>


    <select id="exportDeviceEverydayTotal" resultType="org.skyworth.ess.device.excel.DeviceEverydayTotalExcel">
        SELECT * FROM device_everyday_total ${ew.customSqlSegment}
    </select>

    <select id="appFromPvAndGrid" resultType="org.skyworth.ess.device.vo.DeviceEverydayTotalVO">
        select sum(ifnull( b.today_energy,0)) as sumTodayEnergy ,
        sum(ifnull(d.today_export_energy ,0)) as sumTodayExportEnergy,
        sum(ifnull(b.today_load_energy,0)) as sumTodayLoadEnergy ,
        sum(ifnull(b.daily_energy_to_eps,0)) as sumDailyEnergyToEps ,
        sum(ifnull(b.battery_daily_charge_energy ,0)) as sumBatteryDailyChargeEnergy,
        sum(ifnull(d.today_import_energy ,0)) as sumTodayImportEnergy
        from battery_everyday_total b inner join device_everyday_total d on b.plant_id =d.plant_id and
        b.device_serial_number =d.device_serial_number and d.is_deleted =0 and b.total_date = d.total_date
        where b.is_deleted =0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.total_date >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d')]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.total_date <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d') ]]>
        </if>
    </select>

    <select id="queryDayReport" resultType="org.skyworth.ess.device.entity.DeviceEverydayTotalEntity">
        select
        d.plant_id as plantId,
        d.device_serial_number as deviceSerialNumber,
        d.device_date_time as deviceDateTime,
        DATE_FORMAT(CONVERT_TZ(d.device_date_time,'+00:00',#{queryCondition.timeZone}), '%Y-%m-%d') as totalDate,
        d.today_energy as todayEnergy,
        d.today_import_energy as todayImportEnergy,
        d.today_export_energy as todayExportEnergy,
        d.today_load_energy as todayLoadEnergy,
        d.daily_energy_to_eps as dailyEnergyToEps,
        b.energy_today_of_ac_couple_wh as energyTodayOfAcCoupleWh
        from
        battery_everyday_total b
        inner join device_everyday_total d on
        b.plant_id = d.plant_id
        and
        b.device_serial_number = d.device_serial_number
        and d.is_deleted = 0
        and b.total_date = d.total_date
        where
        b.is_deleted = 0
        and b.plant_id =  #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            and b.device_date_time <![CDATA[ >= ]]>
                STR_TO_DATE(CONVERT_TZ(#{queryCondition.startDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d
                %H:%i:%s')
        </if>
        <if test="queryCondition.endDateTime != null">
            and b.device_date_time <![CDATA[ <= ]]>
                STR_TO_DATE(CONVERT_TZ(#{queryCondition.endDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d %H:%i:%s')
        </if>
        order by
        b.total_date asc
    </select>

    <select id="queryMonthReport" resultType="org.skyworth.ess.device.entity.DeviceEverydayTotalEntity">
        select
        DATE_FORMAT(CONVERT_TZ(d.device_date_time,'+00:00',#{queryCondition.timeZone}), '%Y-%m') as totalDate,
        SUM(d.today_energy) as todayEnergy,
        SUM(d.today_import_energy) as todayImportEnergy,
        SUM(d.today_export_energy) as todayExportEnergy,
        SUM(d.today_load_energy) as todayLoadEnergy,
        SUM(d.daily_energy_to_eps) as dailyEnergyToEps,
        sum(b.energy_today_of_ac_couple_wh) as energyTodayOfAcCoupleWh
        from
        battery_everyday_total b
        inner join device_everyday_total d on
        b.plant_id = d.plant_id
        and b.device_serial_number = d.device_serial_number
        and d.is_deleted = 0
        and b.total_date = d.total_date
        where
        b.is_deleted = 0
        and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            and b.device_date_time <![CDATA[ >= ]]>
            STR_TO_DATE(CONVERT_TZ(#{queryCondition.startDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d
            %H:%i:%s')
        </if>
        <if test="queryCondition.endDateTime != null">
            and b.device_date_time <![CDATA[ <= ]]>
            STR_TO_DATE(CONVERT_TZ(#{queryCondition.endDateTime},#{queryCondition.timeZone},'+00:00'),'%Y-%m-%d %H:%i:%s')
        </if>
        group by totalDate
        order by totalDate asc
    </select>
</mapper>
