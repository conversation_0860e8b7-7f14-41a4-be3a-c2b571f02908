package org.skyworth.ess.plant.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.List;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 新站点列表接口
 * @create-time 2024年5月27日 17:41:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("plant")
public class PlantListVO extends SkyWorthEntity {

	/**
	 * 站点名称
	 */
	@ApiModelProperty(value = "站点名称")
	private String plantName;
	/**
	 * 国家
	 */
	@ApiModelProperty(value = "国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ApiModelProperty(value = "省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ApiModelProperty(value = "区县")
	private String countyCode;
	/**
	 * 国家名称
	 */
	private String countryName;
	/**
	 * 一级行政区域名称
	 */
	private String provinceName;
	/**
	 * 二级行政区域名称
	 */
	private String cityName;
	/**
	 * 三级行政区域名称
	 */
	private String countyName;
	/**
	 * 时区
	 */
	@ApiModelProperty(value = "时区")
	private String timeZone;

	/**
	 * 详细地址
	 */
	@ApiModelProperty(value = "详细地址")
	private String detailAddress;

	/**
	 * 站点状态，由站点下不同设备（逆变器、电池等）汇总，如果其中一个异常站点则异常
	 */
	@ApiModelProperty(value = "站点状态，由站点下不同设备（逆变器、电池等）汇总，如果其中一个异常站点则异常")
	private String plantStatus;

	/**
	 * 日发电量 pv
	 */
	private String powerGeneration ;

	/**
	 * 日储电量  es
	 */
	private String loadCapacity ;

	/**
	 * 逆变器sn
	 */
	private String deviceSerialNumber;

	private String address;

	@ApiModelProperty(value = "设备/逆变器种类")
	@TableField(exist = false)
	private String inverterKind;

	/**
	 * 0 开机
	 */
	private String inverterControl;

	/**
	 * 该逆变器是否开过机：0是 1否
	 */
	private Integer startupByBackstage;
	/**
	 * 该逆变器是否配网成功：0是 1否
	 */
	private Integer inverterConfigureNetwork;
	// 站点下并机设备信息
	private List<PlantDeviceVO> plantDeviceVOList;

	private String isParallelMode;
	// app查看图片上传remark的用户角色类型
	private String userRoleType4Remark;
}
