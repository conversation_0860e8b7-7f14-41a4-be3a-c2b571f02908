package org.skyworth.ess.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.kafka.consumer.BaseEnergyKafkaHandler;
import org.skyworth.ess.ota.feign.ITimeZoneCachedClient;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@NonDS
@ApiIgnore
@RestController
@Slf4j
public class TimeZoneCachedClient implements ITimeZoneCachedClient {
	@Resource
	private BaseEnergyKafkaHandler deviceLog22Handler;

	@CreateCache(name = "plant:timeZone:", expire = 1, timeUnit = TimeUnit.DAYS, cacheType = CacheType.REMOTE)
	private Cache<String, String> plantTimeZoneCache;


	@Override
	public void putCache(String plantId, String timeZone) {
		plantTimeZoneCache.put(plantId,timeZone);
	}

	@Override
	public Map<String, String> getAllCache(Set<String> plantIdSet) {
		return plantTimeZoneCache.getAll(plantIdSet);
	}

	@Override
	public void removeAllCache(Set<String> plantIdSet) {
		plantTimeZoneCache.removeAll(plantIdSet);
	}

	@Override
	public String getCache(Long plantId) {
		return plantTimeZoneCache.get(String.valueOf(plantId));
	}

	//@Override
	//public String baseEnergyKafkaHandler(List<JSONObject> jsonObjectList) {
		 //deviceLog22Handler.handlerData(jsonObjectList);
		 //return "success";
	//}
}
