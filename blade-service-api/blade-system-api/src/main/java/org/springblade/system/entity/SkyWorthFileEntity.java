package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.List;
import java.util.Map;

/**
 * 包含文件对象的通用对象
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-14 08:50
 **/
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "附件操作和展示公共对象", description = "附件操作和展示公共对象")
@Data
public class SkyWorthFileEntity extends SkyWorthEntity {
	@TableField(exist = false)
	@ApiModelProperty(value = "附件操作信息,包括新增&修改&删除附件对象")
	private BatchVO<AttachmentInfoEntity> batchVO;

	@TableField(exist = false)
	@ApiModelProperty(value = "附件回显对象,key为附件businessId,value为附件列表信息")
	private Map<Long, List<AttachmentInfoEntity>> attachmentMap;

	@TableField(exist = false)
	@ApiModelProperty(value = "附件描述操作对象，保存businessId和描述")
	private List<Map<String, String>> imgDescOperationList;

	@TableField(exist = false)
	@ApiModelProperty(value = "附件描述回显对象，通过businessId获取描述")
	private Map<Long, String> imgDescViewMap;

	@TableField(exist = false)
	@ApiModelProperty(value = "附件回显对象,key为附件businessKey的名称,value为附件列表信息")
	private Map<String, List<AttachmentInfoEntity>> attachmentClearnessKeyViewMap;

	@TableField(exist = false)
	@ApiModelProperty(value = "附件回显对象,key为附件businessKey的名称,value为附件列表信息")
	private Map<String, String> imgDescClearnessKeyViewMap;
}
