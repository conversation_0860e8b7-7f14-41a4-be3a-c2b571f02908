/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.additionalInfo.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.excel.AdditionalInfoExcel;
import org.skyworth.ess.additionalInfo.vo.AdditionalInfoVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 图片附加信息 服务类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface IAdditionalInfoService extends BaseService<AdditionalInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param additionalInfo
	 * @return
	 */
	IPage<AdditionalInfoVO> selectAdditionalInfoPage(IPage<AdditionalInfoVO> page, AdditionalInfoVO additionalInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<AdditionalInfoExcel> exportAdditionalInfo(Wrapper<AdditionalInfoEntity> queryWrapper);

	/**
	 * 批量维护文件描述
	 *
	 * @param additionalInfoEntityList 入参
	 * <AUTHOR>
	 * @since 2023/11/30 14:07
	 **/
	void saveAdditionalInfoEntityList(List<AdditionalInfoEntity> additionalInfoEntityList);

	/**
	 * 通过图片businessIds集合查询图片描述
	 *
	 * @param businessIds 入参
	 * @return Map<Long, String>
	 * <AUTHOR>
	 * @since 2023/11/30 14:39
	 **/
	Map<Long, String> selectAdditionalMapByBusinessIds(List<Long> businessIds);

}
