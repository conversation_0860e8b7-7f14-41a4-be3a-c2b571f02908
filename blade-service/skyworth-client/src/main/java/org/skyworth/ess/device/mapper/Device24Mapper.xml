<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.Device24Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="Device24ResultMap" type="org.skyworth.ess.device.entity.Device24Entity">
        <result column="id" property="id"/>
        <result column="year" property="year"/>
        <result column="month_day" property="monthDay"/>
        <result column="hours_min" property="hoursMin"/>
        <result column="seconds" property="seconds"/>
        <result column="power_derating_percent_by_modbus" property="powerDeratingPercentByModbus"/>
        <result column="modbus_address" property="modbusAddress"/>
        <result column="rS485_baud_rate" property="rs485BaudRate"/>
        <result column="wifi_sta_ssid" property="wifiStaSsid"/>
        <result column="wifi_sta_password" property="wifiStaPassword"/>
        <result column="digital_meter_modbus_address" property="digitalMeterModbusAddress"/>
        <result column="digital_meter_type" property="digitalMeterType"/>
        <result column="power_flow_direction" property="powerFlowDirection"/>
        <result column="power_limit_function" property="powerLimitFunction"/>
        <result column="power_limit_ct_ratio" property="powerLimitCtRatio"/>
        <result column="meter_location" property="meterLocation"/>
        <result column="maximum_feed_in_grid_power" property="maximumFeedInGridPower"/>
        <result column="first_connect_start_time" property="firstConnectStartTime"/>
        <result column="reconnect_time" property="reconnectTime"/>
        <result column="grid_frequency_high_loss_level1_limit" property="gridFrequencyHighLossLevel1Limit"/>
        <result column="grid_frequency_low_loss_level1_limit" property="gridFrequencyLowLossLevel1Limit"/>
        <result column="grid_voltage_high_loss_level1_limit" property="gridVoltageHighLossLevel1Limit"/>
        <result column="grid_voltage_low_loss_level1_limit" property="gridVoltageLowLossLevel1Limit"/>
        <result column="grid_frequency_high_loss_level1_trip_time" property="gridFrequencyHighLossLevel1TripTime"/>
        <result column="grid_frequency_low_loss_level1_trip_time" property="gridFrequencyLowLossLevel1TripTime"/>
        <result column="grid_voltage_high_loss_level1_trip_time" property="gridVoltageHighLossLevel1TripTime"/>
        <result column="grid_voltage_low_loss_level1_trip_time" property="gridVoltageLowLossLevel1TripTime"/>
        <result column="grid_frequency_high_loss_level2_limit" property="gridFrequencyHighLossLevel2Limit"/>
        <result column="grid_frequency_low_loss_level2_limit" property="gridFrequencyLowLossLevel2Limit"/>
        <result column="grid_voltage_high_loss_level2_limit" property="gridVoltageHighLossLevel2Limit"/>
        <result column="grid_voltage_low_loss_level2_limit" property="gridVoltageLowLossLevel2Limit"/>
        <result column="grid_frequency_high_loss_level2_trip_time" property="gridFrequencyHighLossLevel2TripTime"/>
        <result column="grid_frequency_low_loss_level2_trip_time" property="gridFrequencyLowLossLevel2TripTime"/>
        <result column="grid_voltage_high_loss_level2_trip_time" property="gridVoltageHighLossLevel2TripTime"/>
        <result column="grid_voltage_low_loss_level2_trip_time" property="gridVoltageLowLossLevel2TripTime"/>
        <result column="grid_frequency_high_level1_back" property="gridFrequencyHighLevel1Back"/>
        <result column="grid_frequency_low_level1_back" property="gridFrequencyLowLevel1Back"/>
        <result column="ten_min_average_sustained_voltage" property="tenMinAverageSustainedVoltage"/>
        <result column="reconnect_soft_output_power_percent" property="reconnectSoftOutputPowerPercent"/>
        <result column="over_frequency_power_reduction_droop" property="overFrequencyPowerReductionDroop"/>
        <result column="insulation_resistance_active_limit" property="insulationResistanceActiveLimit"/>
        <result column="grid_over_voltage_deRating_point" property="gridOverVoltageDeratingPoint"/>
        <result column="grid_frequency_high_level1_trip_time" property="gridFrequencyHighLevel1TripTime"/>
        <result column="grid_frequency_low_Level1_trip_time" property="gridFrequencyLowLevel1TripTime"/>
        <result column="grid_over_frequency_deRating_start_point" property="gridOverFrequencyDeratingStartPoint"/>
        <result column="grid_over_frequency_deRating_end_point" property="gridOverFrequencyDeratingEndPoint"/>
        <result column="grid_voltage_high_level1_trip_time" property="gridVoltageHighLevel1TripTime"/>
        <result column="grid_voltage_low_level1_trip_time" property="gridVoltageLowLevel1TripTime"/>
        <result column="grid_voltage_high_level1_back" property="gridVoltageHighLevel1Back"/>
        <result column="grid_voltage_low_level1_back" property="gridVoltageLowLevel1Back"/>
        <result column="first_connect_soft_start_output_power_percent" property="firstConnectSoftStartOutputPowerPercent"/>
        <result column="over_voltage_derating_settling_time" property="overVoltageDeratingSettlingTime"/>
        <result column="connection_and_reconnection_power_ramp_rate" property="connectionAndReconnectionPowerRampRate"/>
        <result column="output_reactive_power_mode" property="outputReactivePowerMode"/>
        <result column="power_factor_setting" property="powerFactorSetting"/>
        <result column="reactive_control_response_time" property="reactiveControlResponseTime"/>
        <result column="curve_node1_percent" property="curveNode1Percent"/>
        <result column="curve_node2_percent" property="curveNode2Percent"/>
        <result column="curve_node3_percent" property="curveNode3Percent"/>
        <result column="curve_node4_percent" property="curveNode4Percent"/>
        <result column="curve_node1_value_setting" property="curveNode1ValueSetting"/>
        <result column="curve_node2_value_setting" property="curveNode2ValueSetting"/>
        <result column="curve_node3_value_setting" property="curveNode3ValueSetting"/>
        <result column="regulation_code" property="regulationCode"/>
        <result column="inverter_control" property="inverterControl"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="factory_reset" property="factoryReset"/>
        <result column="clear_historical_info" property="clearHistoricalInfo"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="modbus_protocol_version" property="modbusProtocolVersion"/>
        <result column="device_date_time" property="deviceDateTime"/>
    </resultMap>


    <select id="selectDevice24Page" resultMap="Device24ResultMap">
        select * from device_24 where is_deleted = 0
    </select>


    <select id="exportDevice24" resultType="org.skyworth.ess.device.excel.Device24Excel">
        SELECT * FROM device_24 ${ew.customSqlSegment}
    </select>

    <select id="getAllAdvancedSetup" resultType="java.util.HashMap">
        select
            d24.year as year,
            d24.month_day as monthDay,
            d24.hours_min as hoursMin,
            d24.seconds as seconds,
            d24.digital_meter_modbus_address as digitalMeterModbusAddress,
            d24.digital_meter_type as digitalMeterType,
            d24.power_flow_direction as powerFlowDirection,
            d24.power_limit_function as powerLimitFunction,
            d24.meter_location as meterLocation,
            d24.maximum_feed_in_grid_power as maximumFeedInGridPower,
            d24.regulation_code as regulationCode,
            d24.inverter_control as inverterControl,
            d24.factory_reset as factoryReset,
            d24.clear_historical_info as clearHistoricalInfo,
            d23.grid_charge as gridCharge,
            d23.maximum_grid_charger_power as maximumGridChargerPower,
            d23.maximum_charger_power as maximumChargerPower,
            d23.maximum_discharger_power as maximumDischargerPower,
            d23.off_grid_mode as offGridMode,
            d23.gen_port as genPort,
            d23.lithium_battery_activation_function as lithiumBatteryActivationFunction,
            d23.maximum_gen_charge_power as maximumGenChargePower,
            d23.maximum_input_power_from_generator as maximumInputPowerFromGenerator,
            d23.support_normal_load_function as supportNormalLoadFunction,
            d23.parallel_mode_function as parallelModeFunction,
            d23.feed_in_grid_function as feedInGridFunction,
            d23.maximum_grid_forced_charge_power as maximumGridForcedChargePower,
            d23.always_on_with_grid_function as alwaysOnWithGridFunction,
            d23.hybrid_work_mode as hybridWorkMode,
            d21.plant_id as plantId,
            d21.device_serial_number as deviceSerialNumber,
            d21.device_model as deviceModel,
            d21.master_software_version as masterSoftwareVersion,
            d21.slave_firmware_version as slaveFirmwareVersion,
            d21.ems_firmware_version as emsFirmwareVersion,
            d21.dcdc_firmware_version as dcdcFirmwareVersion,
            d23.maximum_input_power_from_grid as maximumInputPowerFromGrid,
            d21.rated_voltage as ratedVoltage,
            d21.rated_frequency as ratedFrequency,
            d23.capacity_of_charger_end as capacityOfChargerEnd,
            d23.capacity_of_discharger_end as capacityOfDischargerEnd,
            d23.capacity_of_discharge_end_on_grid as capacityOfDischargeEndOnGrid,
            d23.force_charge_start_soc as forceChargeStartSoc,
            d23.force_charge_end_soc as forceChargeEndSoc,
            d23.backup_minimum_output_voltage as backupMinimumOutputVoltage,
            d23.backup_maximum_output_voltage as backupMaximumOutputVoltage,
            d23.rated_output_voltage as ratedOutputVoltage,
            d23.generator_start_soc as generatorStartSoc,
            d23.generator_end_soc as generatorEndSoc,
            d23.generator_dry_force_on_or_off as generatorDryForceOnOrOff,
            d24.arc_fault_detect_function as arcFaultDetectFunction,
            d24.arc_fault_reset_status as arcFaultResetStatus,
            CAST(d24.arc_fault_strength_setting_value AS SIGNED) AS arcFaultStrengthSettingValue,
            d24.power_derating_control_mode as powerDeratingControlMode,
            d24.power_switching as powerSwitching,
        CAST(d23.rs485_device AS SIGNED) AS rs485Device,
        d23.ac_coupling_function as acCouplingFunction,
        d24.output_reactive_power_mode as outputReactivePowerMode,
        d24.power_factor_setting as powerFactorSetting,
        d24.reactive_control_response_time as reactiveControlResponseTime,
        d24.curve_node1_percent as curveNode1Percent,
        d24.curve_node2_percent as curveNode2Percent,
        d24.curve_node3_percent as curveNode3Percent,
        d24.curve_node4_percent as curveNode4Percent,
        d24.curve_node1_value_setting as curveNode1ValueSetting,
        d24.curve_node2_value_setting as curveNode2ValueSetting,
        d24.curve_node3_value_setting as curveNode3ValueSetting,
        d24.cosp_curve_node1_percent as cospCurveNode1Percent,
        d24.cosp_curve_node2_percent as cospCurveNode2Percent,
        d24.cosp_curve_node3_percent as cospCurveNode3Percent,
        d24.cosp_curve_node4_percent as cospCurveNode4Percent,
        d24.cosp_curve_node1_value as cospCurveNode1Value,
        d24.cosp_curve_node2_value as cospCurveNode2Value,
        d24.cosp_curve_node3_value as cospCurveNode3Value,
        d24.cosp_curve_node4_value as cospCurveNode4Value,
        d24.curve_node4_value_setting as curveNode4ValueSetting,
        d24.curve_node1_percentage_setting as curveNode1PercentageSetting,
        d24.curve_node2_percentage_setting as curveNode2PercentageSetting,
        d24.curve_node3_percentage_setting as curveNode3PercentageSetting,
        d24.curve_node4_percentage_setting as curveNode4PercentageSetting,
        d24.curve_node1_value_setting_pn as curveNode1ValueSettingPn,
        d24.curve_node2_value_setting_pn as curveNode2ValueSettingPn,
        d24.curve_node3_value_setting_pn as curveNode3ValueSettingPn,
        d24.curve_node4_value_setting_pn as curveNode4ValueSettingPn,
        d24.reactive_power_percent as reactivePowerPercent,
            d23.parallel_system_battery_connect_type as parallelSystemBatteryConnectType
        from device_24 d24
            left join device_23 d23 on  d24.device_serial_number = d23.device_serial_number and d24.plant_id = d23.plant_id
            left join device_21 d21 on  d24.device_serial_number = d21.device_serial_number and d24.plant_id = d21.plant_id
        WHERE d21.plant_id = #{plantId} and d21.device_serial_number = #{deviceSerialNumber}
          and d21.is_deleted = 0 and  d23.is_deleted = 0 and d24.is_deleted = 0
    </select>

    <select id="getAllAdvancedSetupIsDelete" resultType="java.util.HashMap">
        select
            d24.year as year,
            d24.month_day as monthDay,
            d24.hours_min as hoursMin,
            d24.seconds as seconds,
            d24.digital_meter_modbus_address as digitalMeterModbusAddress,
            d24.digital_meter_type as digitalMeterType,
            d24.power_flow_direction as powerFlowDirection,
            d24.power_limit_function as powerLimitFunction,
            d24.meter_location as meterLocation,
            d24.maximum_feed_in_grid_power as maximumFeedInGridPower,
            d24.regulation_code as regulationCode,
            d24.inverter_control as inverterControl,
            d24.factory_reset as factoryReset,
            d24.clear_historical_info as clearHistoricalInfo,
            d23.grid_charge as gridCharge,
            d23.maximum_grid_charger_power as maximumGridChargerPower,
            d23.maximum_charger_power as maximumChargerPower,
            d23.maximum_discharger_power as maximumDischargerPower,
            d23.off_grid_mode as offGridMode,
            d23.gen_port as genPort,
            d23.lithium_battery_activation_function as lithiumBatteryActivationFunction,
            d23.maximum_gen_charge_power as maximumGenChargePower,
            d23.maximum_input_power_from_generator as maximumInputPowerFromGenerator,
            d23.support_normal_load_function as supportNormalLoadFunction,
            d23.parallel_mode_function as parallelModeFunction,
            d23.feed_in_grid_function as feedInGridFunction,
            d23.maximum_grid_forced_charge_power as maximumGridForcedChargePower,
            d23.always_on_with_grid_function as alwaysOnWithGridFunction,
            d23.hybrid_work_mode as hybridWorkMode,
            d21.plant_id as plantId,
            d21.device_serial_number as deviceSerialNumber,
            d21.device_model as deviceModel,
            d21.master_software_version as masterSoftwareVersion,
            d21.slave_firmware_version as slaveFirmwareVersion,
            d21.ems_firmware_version as emsFirmwareVersion,
            d21.dcdc_firmware_version as dcdcFirmwareVersion,
            d23.maximum_input_power_from_grid as maximumInputPowerFromGrid,
            d21.rated_voltage as ratedVoltage,
            d21.rated_frequency as ratedFrequency,
            d23.capacity_of_charger_end as capacityOfChargerEnd,
            d23.capacity_of_discharger_end as capacityOfDischargerEnd,
            d23.capacity_of_discharge_end_on_grid as capacityOfDischargeEndOnGrid,
            d23.force_charge_start_soc as forceChargeStartSoc,
            d23.force_charge_end_soc as forceChargeEndSoc,
            d23.backup_minimum_output_voltage as backupMinimumOutputVoltage,
            d23.backup_maximum_output_voltage as backupMaximumOutputVoltage,
            d23.rated_output_voltage as ratedOutputVoltage,
            d23.generator_start_soc as generatorStartSoc,
            d23.generator_end_soc as generatorEndSoc,
            d23.generator_dry_force_on_or_off as generatorDryForceOnOrOff,
            d24.arc_fault_detect_function as arcFaultDetectFunction,
            d24.arc_fault_reset_status as arcFaultResetStatus,
            CAST(d24.arc_fault_strength_setting_value AS SIGNED) AS arcFaultStrengthSettingValue,
            d24.power_derating_control_mode as powerDeratingControlMode,
            d24.power_switching as powerSwitching,
        CAST(d23.rs485_device AS SIGNED) AS rs485Device,
        d23.ac_coupling_function as acCouplingFunction,
        d24.output_reactive_power_mode as outputReactivePowerMode,
        d24.power_factor_setting as powerFactorSetting,
        d24.reactive_control_response_time as reactiveControlResponseTime,
        d24.curve_node1_percent as curveNode1Percent,
        d24.curve_node2_percent as curveNode2Percent,
        d24.curve_node3_percent as curveNode3Percent,
        d24.curve_node4_percent as curveNode4Percent,
        d24.curve_node1_value_setting as curveNode1ValueSetting,
        d24.curve_node2_value_setting as curveNode2ValueSetting,
        d24.curve_node3_value_setting as curveNode3ValueSetting,
        d24.cosp_curve_node1_percent as cospCurveNode1Percent,
        d24.cosp_curve_node2_percent as cospCurveNode2Percent,
        d24.cosp_curve_node3_percent as cospCurveNode3Percent,
        d24.cosp_curve_node4_percent as cospCurveNode4Percent,
        d24.cosp_curve_node1_value as cospCurveNode1Value,
        d24.cosp_curve_node2_value as cospCurveNode2Value,
        d24.cosp_curve_node3_value as cospCurveNode3Value,
        d24.cosp_curve_node4_value as cospCurveNode4Value,
        d24.curve_node4_value_setting as curveNode4ValueSetting,
        d24.curve_node1_percentage_setting as curveNode1PercentageSetting,
        d24.curve_node2_percentage_setting as curveNode2PercentageSetting,
        d24.curve_node3_percentage_setting as curveNode3PercentageSetting,
        d24.curve_node4_percentage_setting as curveNode4PercentageSetting,
        d24.curve_node1_value_setting_pn as curveNode1ValueSettingPn,
        d24.curve_node2_value_setting_pn as curveNode2ValueSettingPn,
        d24.curve_node3_value_setting_pn as curveNode3ValueSettingPn,
        d24.curve_node4_value_setting_pn as curveNode4ValueSettingPn,
        d24.reactive_power_percent as reactivePowerPercent,
            d23.parallel_system_battery_connect_type as parallelSystemBatteryConnectType
        from device_24 d24
            left join device_23 d23 on  d24.device_serial_number = d23.device_serial_number and d24.plant_id = d23.plant_id
            left join device_21 d21 on  d24.device_serial_number = d21.device_serial_number and d24.plant_id = d21.plant_id
        WHERE d21.plant_id = #{plantId} and d21.device_serial_number = #{deviceSerialNumber}
          and d24.is_deleted = 1
    </select>

    <update id="updateSetup">
        UPDATE device_24 SET
        update_time = now(),
        <foreach collection="device24Map" item="val" index="key" separator=",">
            ${key} = #{val}
        </foreach>
        WHERE plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber}
    </update>

    <select id="getDevice24Info" resultMap="Device24ResultMap">
        select id, plant_id, device_serial_number,inverter_control
        from device_24 where is_deleted=0
        and plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.plantId}
        </foreach>
    </select>

    <update id="updateInverterControl">
        update device_24 set inverter_control = #{params.inverterControl}
        where plant_id = #{params.plantId} and device_serial_number = #{params.deviceSerialNumber}
    </update>

    <select id="getInverterControl" resultType="java.util.Map">
        select inverter_control  as inverterControl
        from device_24
        where plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber} and is_deleted = 0
    </select>

    <select id="getInverterControlIsDelete" resultType="java.util.Map">
        select inverter_control  as inverterControl
        from device_24
        where plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber} and is_deleted = 1
    </select>

    <select id="getInverterModeTimeBase" resultType="java.util.Map">
        select
            d23.hybrid_work_mode as hybridWorkMode,
            d23.time_based_control_enable as timeBasedControlEnable,
            d23.once_everyday as onceEveryday,
            d23.charge_start_time1 as chargeStartTime1,
            d23.charge_end_time1 as chargeEndTime1,
            d23.discharge_start_time1 as dischargeStartTime1,
            d23.discharge_end_time1 as dischargeEndTime1,
            d23.charge_power_in_time1_high_word as chargePowerInTime1HighWord,
            d23.charge_end_soc_in_time1 as chargeEndSocInTime1,
            d23.discharge_power_in_time1_high_word as dischargePowerInTime1HighWord,
            d23.discharge_end_soc_in_time1 as dischargeEndSocInTime1,
            d23.once_everyday2 as onceEveryday2,
            d23.charge_start_time2 as chargeStartTime2,
            d23.charge_end_time2 as chargeEndTime2,
            d23.discharge_start_time2 as dischargeStartTime2,
            d23.discharge_end_time2 as dischargeEndTime2,
            d23.charge_power_in_time2_high_word as chargePowerInTime2HighWord,
            d23.charge_end_soc_in_time2 as chargeEndSocInTime2,
            d23.discharge_power_in_time2_high_word as dischargePowerInTime2HighWord,
            d23.discharge_end_soc_in_time2 as dischargeEndSocInTime2,
            d23.once_everyday3 as onceEveryday3,
            d23.charge_start_time3 as chargeStartTime3,
            d23.charge_end_time3 as chargeEndTime3,
            d23.discharge_start_time3 as dischargeStartTime3,
            d23.discharge_end_time3 as dischargeEndTime3,
            d23.charge_power_in_time3_high_word as chargePowerInTime3HighWord,
            d23.charge_end_soc_in_time3 as chargeEndSocInTime3,
            d23.discharge_power_in_time3_high_word as dischargePowerInTime3HighWord,
            d23.discharge_end_soc_in_time3 as dischargeEndSocInTime3,


            d21.plant_id as plantId,
            d21.device_serial_number as deviceSerialNumber,
            d21.device_model as deviceModel,
            d21.device_status as deviceStatus
        from device_24 d24
                 left join device_23 d23 on  d24.device_serial_number = d23.device_serial_number and d24.plant_id = d23.plant_id
                 left join device_21 d21 on  d24.device_serial_number = d21.device_serial_number and d24.plant_id = d21.plant_id
        WHERE d21.plant_id = #{plantId} and d21.device_serial_number = #{deviceSerialNumber}
          and d21.is_deleted = 0 and d23.is_deleted = 0 and d24.is_deleted = 0
    </select>

    <select id="getInverterModeTimeBaseIsDelete" resultType="java.util.Map">
        select
            d23.hybrid_work_mode as hybridWorkMode,
            d23.time_based_control_enable as timeBasedControlEnable,
            d23.once_everyday as onceEveryday,
            d23.charge_start_time1 as chargeStartTime1,
            d23.charge_end_time1 as chargeEndTime1,
            d23.discharge_start_time1 as dischargeStartTime1,
            d23.discharge_end_time1 as dischargeEndTime1,
            d23.charge_power_in_time1_high_word as chargePowerInTime1HighWord,
            d23.charge_end_soc_in_time1 as chargeEndSocInTime1,
            d23.discharge_power_in_time1_high_word as dischargePowerInTime1HighWord,
            d23.discharge_end_soc_in_time1 as dischargeEndSocInTime1,
            d23.once_everyday2 as onceEveryday2,
            d23.charge_start_time2 as chargeStartTime2,
            d23.charge_end_time2 as chargeEndTime2,
            d23.discharge_start_time2 as dischargeStartTime2,
            d23.discharge_end_time2 as dischargeEndTime2,
            d23.charge_power_in_time2_high_word as chargePowerInTime2HighWord,
            d23.charge_end_soc_in_time2 as chargeEndSocInTime2,
            d23.discharge_power_in_time2_high_word as dischargePowerInTime2HighWord,
            d23.discharge_end_soc_in_time2 as dischargeEndSocInTime2,
            d23.once_everyday3 as onceEveryday3,
            d23.charge_start_time3 as chargeStartTime3,
            d23.charge_end_time3 as chargeEndTime3,
            d23.discharge_start_time3 as dischargeStartTime3,
            d23.discharge_end_time3 as dischargeEndTime3,
            d23.charge_power_in_time3_high_word as chargePowerInTime3HighWord,
            d23.charge_end_soc_in_time3 as chargeEndSocInTime3,
            d23.discharge_power_in_time3_high_word as dischargePowerInTime3HighWord,
            d23.discharge_end_soc_in_time3 as dischargeEndSocInTime3,


            d21.plant_id as plantId,
            d21.device_serial_number as deviceSerialNumber,
            d21.device_model as deviceModel,
            d21.device_status as deviceStatus
        from device_24 d24
                 left join device_23 d23 on  d24.device_serial_number = d23.device_serial_number and d24.plant_id = d23.plant_id
                 left join device_21 d21 on  d24.device_serial_number = d21.device_serial_number and d24.plant_id = d21.plant_id
        WHERE d21.plant_id = #{plantId} and d21.device_serial_number = #{deviceSerialNumber}
          and d24.is_deleted = 1
    </select>
</mapper>
