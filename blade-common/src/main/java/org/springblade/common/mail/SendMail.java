package org.springblade.common.mail;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.MultipartFileToFile;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@Service
@Slf4j
public class SendMail {
	@Autowired
	private MailConfig mailConfig;

	public static final String OUTLOOK_HOST = "smtp.office365.com";
	public boolean sendMail(String text, String sendToMail) {
		log.info("sendMail contant : {} ,sendToMail : {}", text, sendToMail);
		try {
			MimeMessage message = getMimeMessage();
			message.setSubject(mailConfig.getSubject());
			message.setContent(text, "text/html;charset=UTF-8");
			message.setFrom(new InternetAddress(mailConfig.getUsername()));
			message.setRecipient(Message.RecipientType.TO, new InternetAddress(sendToMail));
			Transport.send(message);
		} catch (MessagingException e) {
			log.error("send mail error : {} ", e.getMessage());
			return false;
		}
		return true;
	}

	/**
	 * 发送邮件财务清单通知客户缴费
	 *
	 * @param multipartFileArray sendToMail userName
	 */
	public boolean sendEmail(MultipartFile[] multipartFileArray, String sendToMail, String mailBody,String bccMail,String topic) {
		try {
			MimeMessage message = getMimeMessage();
			message.setSubject(topic);

			List<File> fileList = new ArrayList<>();
			if (ObjectUtil.isNotEmpty(multipartFileArray)) {
				for (MultipartFile multipartFile : multipartFileArray) {
					File file = MultipartFileToFile.multipartFileToFile(multipartFile);
					if (file != null) {
						fileList.add(file);
					}
				}
			}
			// MultipartFile 转 File
			message.setContent(getMultipart(fileList, mailBody), "text/html;charset=UTF-8");
			message.setFrom(new InternetAddress(mailConfig.getUsername()));

			String[] sendToMailArr = sendToMail.split(",");
			for (String mail : sendToMailArr) {
				if (StringUtils.isNotEmpty(mail)) {
					message.addRecipient(Message.RecipientType.TO, new InternetAddress(mail));
				}
			}
			if(ValidationUtil.isNotEmpty(bccMail)){
				String[] bccMailArr=bccMail.split(",");
				for(String bcc:bccMailArr){
					message.addRecipient(Message.RecipientType.BCC,new InternetAddress(bcc));
				}
			}
			Transport.send(message);
			for (File file : fileList) {
				//发送完成后删除 临时文件
				MultipartFileToFile.deleteTempFile(file);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("send mail error : {} ", e.getMessage());
			return false;
		}
		return true;
	}

	private MimeMultipart getMultipart(List<File> files, String mailBody) {
		MimeMultipart multipart = new MimeMultipart();
		try {
			if(ValidationUtil.isNotEmpty(mailBody)){
				MimeBodyPart text = new MimeBodyPart();
				text.setContent(String.format(mailBody), "text/html;charset=UTF-8");
				//将文本和图片添加到multipart
				multipart.addBodyPart(text);
			}
		} catch (MessagingException e) {
			throw new RuntimeException(e);
		}

		for (File file : files) {
			DataHandler handler = new DataHandler(new FileDataSource(file));
			try {
				MimeBodyPart attach = new MimeBodyPart();
				attach.setFileName(handler.getName());
				attach.setDataHandler(handler);
				multipart.addBodyPart(attach);
			} catch (MessagingException e) {
				throw new RuntimeException(e);
			}
		}

		return multipart;
	}

	/**
	 *抽取邮件公共配置
	 * */
	@NotNull
	private MimeMessage getMimeMessage() {
		Properties props = new Properties();
		//	SMTP主机名
		props.put("mail.smtp.host", mailConfig.getHost());
		//	主机端口号
		props.put("mail.smtp.socketFactory.port", mailConfig.getPort());
		//	是否需要用户认证
		props.put("mail.smtp.auth", mailConfig.getAuth());
		props.put("mail.smtp.starttls.enable", mailConfig.getStarttlsEnable());
		props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
		// 微软邮箱需要增加这2个参数
		if (mailConfig.getHost().equals(OUTLOOK_HOST)){
			props.put("mail.smtp.port", mailConfig.getPort());
			props.put("mail.transport.protocol", "smtp");
		}
		Session session = Session.getInstance(props, new Authenticator() {
			@Override
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(mailConfig.getUsername(), mailConfig.getPassword());
			}
		});

		session.setDebug(true);
		MimeMessage message = new MimeMessage(session);
		return message;
	}

	/**
	 * 多参数发送邮件
	 * */
	public boolean sendMailMultiParams(List<File> fileList,String toMails,String ccMails,String bccMails,String subject,String text){
		try{
			MimeMessage message = getMimeMessage();
			message.setSubject(subject);
			message.setFrom(new InternetAddress(mailConfig.getUsername()));
			String[] toMailsArr=toMails.split(",");
			for(String toMail:toMailsArr){
				message.addRecipient(Message.RecipientType.TO, new InternetAddress(toMail));
			}
			if(ValidationUtil.isNotEmpty(ccMails)) {
				String[] ccMailsArr = ccMails.split(",");
				for (String ccMail : ccMailsArr) {
					message.addRecipient(Message.RecipientType.CC, new InternetAddress(ccMail));
				}
			}
			if(ValidationUtil.isNotEmpty(bccMails)) {
				String[] bccMailsArr = bccMails.split(",");
				for (String bccMail : bccMailsArr) {
					message.addRecipient(Message.RecipientType.BCC, new InternetAddress(bccMail));
				}
			}
			if(!CollectionUtils.isNullOrEmpty(fileList)){
                message.setContent(getMultipart(fileList,text));
			}else {
				message.setContent(text, "text/html;charset=UTF-8");
			}
			Transport.send(message);
		}catch (Exception e){
			log.error("send mail error : {} ", e.getMessage());
			return false;
		}
		return true;
	}

}
