<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.addressmap.mapper.AddressMapDefinitionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="addressMapDefinitionResultMap" type="org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity">
        <result column="id" property="id"/>
        <result column="company" property="company"/>
        <result column="modbus_protocol_version" property="modbusProtocolVersion"/>
        <result column="address" property="address"/>
        <result column="definition" property="definition"/>
        <result column="data_type" property="dataType"/>
        <result column="unit" property="unit"/>
        <result column="length" property="length"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="system_required_configuration" property="systemRequiredConfiguration"/>
        <result column="system_required_configuration_str" property="systemRequiredConfigurationStr"/>
    </resultMap>


    <select id="selectAddressMapDefinitionPage" resultMap="addressMapDefinitionResultMap">
        select
            id,
            company,
            modbus_protocol_version,
            address,
            definition,
            data_type,
            unit,
            length,
            decimal_address,
            create_user_account,
            update_user_account,
            tenant_id,
            create_user,
            create_dept,
            create_time,
            update_user,
            update_time,
            status,
            is_deleted,
            system_required_configuration
        from address_map_definition where is_deleted = 0
    </select>


    <select id="exportAddressMapDefinition" resultType="org.skyworth.ess.addressmap.excel.AddressMapDefinitionExcel">
        SELECT
            id,
            company,
            modbus_protocol_version,
            address,
            definition,
            data_type,
            unit,
            length,
            decimal_address,
            create_user_account,
            update_user_account,
            tenant_id,
            create_user,
            create_dept,
            create_time,
            update_user,
            update_time,
            status,
            is_deleted,
            system_required_configuration,
            CASE
                WHEN system_required_configuration = 1 THEN 'Yes'
                WHEN system_required_configuration = 0 OR system_required_configuration IS NULL THEN 'No'
            END AS system_required_configuration_str
        FROM address_map_definition ${ew.customSqlSegment}
    </select>

    <update id="updateBatchByCondition">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update address_map_definition set
            <if test="item.definition!=null and item.definition!=''">
                definition=#{item.definition},
            </if>
            <if test="item.unit!=null and item.unit!=''">
                unit=#{item.unit},
            </if>
            <if test="item.length!=null and item.length!=''">
                length=#{item.length},
            </if>
            <if test="item.decimalAddress!=null and item.decimalAddress!=''">
                decimal_address=#{item.decimalAddress},
            </if>
            <if test="item.createUserAccount!=null and item.createUserAccount!=''">
                create_user_account=#{item.createUserAccount},
            </if>
            <if test="item.updateUserAccount!=null and item.updateUserAccount!=''">
                update_user_account=#{item.updateUserAccount},
            </if>
            update_time = now()
            where company = #{item.company}
            and modbus_protocol_version = #{item.modbusProtocolVersion}
            and address = #{item.address}
            and data_type = #{item.dataType}
        </foreach>

    </update>
</mapper>
