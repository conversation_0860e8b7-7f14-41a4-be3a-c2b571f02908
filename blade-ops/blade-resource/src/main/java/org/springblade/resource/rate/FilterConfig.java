package org.springblade.resource.rate;


import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class FilterConfig {
	private IPBlacklist ipBlacklist;

	@Bean
	public IPBlacklist ipBlacklist() {
		this.ipBlacklist = new IPBlacklist();
		return new IPBlacklist();
	}

	@Bean
	public FilterRegistrationBean<RateLimitAndBlacklistFilter> loggingFilter() {
		FilterRegistrationBean<RateLimitAndBlacklistFilter> registrationBean = new FilterRegistrationBean<>();

		// 1分钟内最多5次请求
		RateLimiterService rateLimiterService = new RateLimiterService(5, TimeUnit.MINUTES.toMillis(1));

		RateLimitAndBlacklistFilter filter = new RateLimitAndBlacklistFilter(rateLimiterService, ipBlacklist);
		registrationBean.setFilter(filter);
		registrationBean.addUrlPatterns("/phone/sms/verificationCode");
		registrationBean.addUrlPatterns("/app/login/getVerificationCode");

		return registrationBean;
	}
}
