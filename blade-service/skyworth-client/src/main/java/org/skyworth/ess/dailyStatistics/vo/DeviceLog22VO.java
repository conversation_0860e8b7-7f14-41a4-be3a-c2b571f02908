/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.dailyStatistics.vo;

import io.swagger.annotations.ApiModelProperty;
import org.skyworth.ess.dailyStatistics.entity.DeviceLog22Entity;
import org.springblade.core.tool.node.INode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器日志表，记录22数据 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceLog22VO extends DeviceLog22Entity {
	private static final long serialVersionUID = 1L;

	private String appTotalDate;
	private Date appDeviceDateTime;
	private BigDecimal appBatteryDailyChargeEnergy;

	private BigDecimal appBatteryDailyDischargeEnergy;
	// 光伏日发电量，上层对象中包含
	private BigDecimal pvTodayEnergy;
	//电池 储电量
	private BigDecimal appBatteryAccumulatedChargeEnergy;

	// 地址码 1048
	private BigDecimal appPvTotalInputPower;
	// 地址码 2009 正数和0记录为0，负数则取绝对值，取 Battery power 的负数值
	private BigDecimal appBatteryPower;
	// 地址码 130A
	private BigDecimal appPhaserWattOfLoad;
	// 130C
	private BigDecimal appPhasesWattOfLoad;
	// 130E
	private BigDecimal appPhasetWattOfLoad;
	// 地址码 1353
	private BigDecimal appPhaserWattOfEps;
	// 1359
	private BigDecimal appPhasesWattOfEps;
	// 135E
	private BigDecimal appPhasetWattOfEps;

	// 地址码 130A + 130C + 130E + 地址码 1353 + 1359 + 135E
	private BigDecimal appLoadAddEps;
	// 1300
	private BigDecimal appPhaserWattOfGrid;
	// 1302
	private BigDecimal appPhasesWattOfGrid;
	// 1304
	private BigDecimal appPhasetWattOfGrid;
	// 2000
	private BigDecimal appBatterySoc;
	// 2009  少于0的时候记录为0，取 Battery power 的正数值
	private BigDecimal appBatteryInput;
	// 1300 取负数，正数和0记录为0，负数则取绝对值相加
	private BigDecimal appFeedInGridPhaser;
	// 1302 取负数，正数和0记录为0，负数则取绝对值相加
	private BigDecimal appFeedInGridPhases;
	// 1304 取负数，正数和0记录为0，负数则取绝对值相加
	private BigDecimal appFeedInGridPhaset;

	private BigDecimal fromPv = new BigDecimal("0");
	private BigDecimal fromGrid = new BigDecimal("0");
	// 2024.3月版本 begin
	/**
	 * 原始当日光伏发电量，未转换，不带单位  1027  battery_current_status
	 */
	private BigDecimal originalTodayEnergy;
	/**
	 * 原始当日电网输出，未转换，不带单位  1334  馈电量  device_current_status
	 */
	private BigDecimal originalTodayExportEnergy;
	// 1336 原始值  device_current_status
	private BigDecimal originalTodayLoadEnergy;
	// 1360 原始值 device_current_status
	private BigDecimal originalDailyEnergyToEps;
	// 200B 原始值  device_current_status
	private BigDecimal originalBatteryDailyChargeEnergy;
	/**
	 * 电网今日输入 1332 原始数据  device_current_status
	 */
	private BigDecimal originalTodayImportEnergy;
	// end

	// 并机
	// 13DC
	private BigDecimal appTotallyInputDcWattSum;
	// 13DE
	private BigDecimal appBatteryPowerSum;
	// 13A6
	private BigDecimal appPhaseL1WattOfLoadSum;
	// 13A8
	private BigDecimal appPhaseL2WattOfLoadSum;
	// 13AA
	private BigDecimal appPhaseL3WattOfLoadSum;
	// 13B4
	private BigDecimal appPhaseL1WattSumOfBackup;
	// 13B6
	private BigDecimal appPhaseL2WattSumOfBackup;
	// 13B8
	private BigDecimal appPhaseL3WattSumOfBackup;
	// 13A6 + 13A8 + 13AA + 13B4 + 13B6 + 13B8
	private BigDecimal appLoadAddBackup;
	// 13A0
	private BigDecimal appPhaseL1WattOfGridSum;
	// 13A2
	private BigDecimal appPhaseL2WattOfGridSum;
	// 13A4
	private BigDecimal appPhaseL3WattOfGridSum;
	// 并机 end
	/* 1、天报表为： L1+L2+L3=1503~1504+1508~1509+150D~150E
	 2、周月年报表为：0x1514~0x1515
	 */
	private BigDecimal appOtherPv;
	// 1514
	private BigDecimal energyTodayOfAcCoupleWh;
}
