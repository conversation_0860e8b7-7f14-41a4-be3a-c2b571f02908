/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmlog.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.mapper.AlarmLogMapper;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageCondition;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageVO;
import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.skyworth.ess.alarmoperationrecord.service.IAlarmLogOperationRecordService;
import org.skyworth.ess.app.service.impl.AppServiceImpl;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.timeshift.ITimeShiftService;
import org.skyworth.ess.util.TimeZoneUtil;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 异常日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
@AllArgsConstructor
public class AlarmLogServiceImpl extends BaseServiceImpl<AlarmLogMapper, AlarmLogEntity> implements IAlarmLogService {

	private final IDictBizClient dictBizClient;
	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final String INVERTER_TYPE = BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER;
	private final String BATTERY_TYPE = BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY;
	private final IBatteryMapDeviceService batteryMapDeviceService;
	private final ITimeShiftService timeShiftService;
	private final IAlarmLogOperationRecordService alarmLogOperationRecordService;
	private final TimeZoneUtil timeZoneUtil;

	/**
	 * 分页app查询告警日志
	 *
	 * @param alarmLogPageCondition 告警日志查询条件
	 * @param query                 分页查询对象
	 * @return 返回分页查询结果，包含告警日志列表
	 */
	@Override
	public IPage<AlarmLogPageVO> appList(AlarmLogPageCondition alarmLogPageCondition, Query query) {
		// 初始化分页对象
		IPage<AlarmLogPageVO> page = Condition.getPage(query);
		// 设置查询条件中的用户ID和部门ID
		setUserIdAndDeptIdInCondition(alarmLogPageCondition);
		// 执行分页查询
		List<AlarmLogPageVO> alarmLogEntityList = baseMapper.list(alarmLogPageCondition, page);
		// 获取并处理字典数据
		if (!CollectionUtils.isNullOrEmpty(alarmLogEntityList)) {
			processDictionaryData(alarmLogEntityList);
		}
		List<Long> plantIdList =
			alarmLogEntityList.stream().map(AlarmLogPageVO::getPlantId).distinct().collect(Collectors.toList());
		timeShiftService.getAndReturnList(alarmLogEntityList, "deviceDateTime", plantIdList);
		timeShiftService.getAndReturnList(alarmLogEntityList, "recoveryTime", plantIdList);
		// 设置查询结果集
		page.setRecords(alarmLogEntityList);
		// 返回分页查询结果
		return page;
	}

	/**
	 * 获取web告警日志详情
	 *
	 * @param alarmLogPageCondition 告警入参
	 * @param query                 分页条件
	 * @return 告警日志详情
	 */
	@Override
	public IPage<AlarmLogPageVO> webList(AlarmLogPageCondition alarmLogPageCondition, Query query) {
		// 初始化分页对象
		IPage<AlarmLogPageVO> page = Condition.getPage(query);
		// 设置查询条件中的用户ID和部门ID
		setUserIdAndDeptIdInCondition(alarmLogPageCondition);
		String timeZone = timeZoneUtil.getUserWebTimeZone();
		// 设置web时区
		alarmLogPageCondition.setTimeZone(timeZoneUtil.removeTimeZoneLetters(timeZone));
		// 执行分页查询
		List<AlarmLogPageVO> alarmLogEntityList = baseMapper.webList(alarmLogPageCondition, page);
		// 获取并处理字典数据
		if (!CollectionUtils.isNullOrEmpty(alarmLogEntityList)) {
			processDictionaryData(alarmLogEntityList);
			alarmLogEntityList.forEach(a->{
				a.setTimeZone(timeZone);
			});
		}
		// 设置查询结果集
		page.setRecords(alarmLogEntityList);
		// 返回分页查询结果
		return page;
	}

	@Override
	public AlarmLogPageVO detail(String id) {
		AlarmLogPageVO detail = baseMapper.detail(id);
		if (ObjectUtil.isNotEmpty(detail)) {
			processDictionaryData(ListUtil.of(detail));
		}

		timeShiftService.shiftTimeZoneAndDateTimeForEntity(detail, "deviceDateTime", "recoveryTime");
		return detail;
	}

	@Override
	public List<AlarmLogOperationRecordEntity> operationRecordList(String alarmLogId) {
		List<AlarmLogOperationRecordEntity> alarmLogOperationRecordEntityList =
			alarmLogOperationRecordService.selectOperationRecordList(alarmLogId);
		List<Long> plantIdList =
			alarmLogOperationRecordEntityList.stream().map(AlarmLogOperationRecordEntity::getPlantId).distinct().collect(Collectors.toList());
		timeShiftService.getAndReturnList(alarmLogOperationRecordEntityList, "updateTime", plantIdList);
		String language = CommonUtil.getCurrentLanguage();
		Optional.of(alarmLogOperationRecordEntityList).orElse(new ArrayList<>()).forEach(a -> {
			String handler = a.getUpdateUserAccount();
			if (CommonConstant.COMMON_SYSTEM_USER_ACCOUNT.equalsIgnoreCase(handler)) {
				if (CommonConstant.CURRENT_LANGUAGE_DE.equalsIgnoreCase(language)) {
					a.setRemark(CommonConstant.COMMON_SYSTEM_AUTO_RECOVERY_DE);
				} else {
					a.setRemark(CommonConstant.COMMON_SYSTEM_AUTO_RECOVERY_EN);
				}
			}
		});
		return alarmLogOperationRecordEntityList;
	}

	/**
	 * 设置查询条件中的用户ID和部门ID
	 *
	 * @param condition 查询条件对象
	 */
	private void setUserIdAndDeptIdInCondition(AlarmLogPageCondition condition) {
		BladeUser user = AuthUtil.getUser();
		String deptId = AppServiceImpl.inspectInnerRole(user);
		condition.setUserId(user.getUserId());
		condition.setDepartmentId(deptId);
	}

	/**
	 * 获取并处理字典数据
	 *
	 * @param alarmLogEntityList 告警日志实体列表
	 */
	private void processDictionaryData(List<AlarmLogPageVO> alarmLogEntityList) {
		// 批量获取字典数据
		Map<String, List<DictBiz>> dictBizMap =
			dictBizClient.batchGetList(Arrays.asList(DictBizCodeEnum.DEVICE_TYPE.getDictCode(),
				DictBizCodeEnum.BATTERY_MODE_TYPE.getDictCode(), DictBizCodeEnum.DEVICE_MODE_TYPE.getDictCode(),
				DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode())).getData();
		// 获取当前语言环境
		String currentLanguage = CommonUtil.getCurrentLanguage();
		// 根据语言环境筛选设备类型字典数据
		Map<String, String> dictDeviceTypeBizMap = getDictDeviceTypeBizMap(dictBizMap, currentLanguage);
		// 处理每个告警日志的显示数据
		processAlarms(alarmLogEntityList, dictDeviceTypeBizMap, dictBizMap, currentLanguage);
	}

	/**
	 * 生成处理建议Map对象
	 *
	 * @param dictBizMap      字典Map
	 * @param currentLanguage 入参
	 * @return Map<String, String>
	 * <AUTHOR>
	 * @since 2024/9/6 17:27
	 **/
	private Map<String, DictBiz> getDictSuggestionsHandlingMap(List<AlarmLogPageVO> alarmLogEntityList, Map<String,
		List<DictBiz>> dictBizMap, String currentLanguage) {
		Map<String, DictBiz> dictsuggestionsHandlingMap = new HashMap<>();
		// 空值检查
		if (alarmLogEntityList == null || alarmLogEntityList.isEmpty() || dictBizMap == null) {
			return dictsuggestionsHandlingMap;
		}
		// 获取地址码列表
		List<String> addressCodeList = alarmLogEntityList.stream()
			.map(AlarmLogPageVO::getAddressCode)
			.distinct()
			.collect(Collectors.toList());
		// 获取字典业务列表
		List<DictBiz> dictBizList = dictBizMap.getOrDefault(DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode(),
			Collections.emptyList());
		// 获取一级菜单的code
		Map<Long, String> dictParentMap = dictBizList.stream()
			.filter(a -> addressCodeList.contains(a.getDictKey()) && a.getLanguage().equalsIgnoreCase(currentLanguage))
			.collect(Collectors.toMap(DictBiz::getId, DictBiz::getDictKey, (a, b) -> a));
		// 处理字典建议
		dictBizList.forEach(a -> {
			if (dictParentMap.containsKey(a.getParentId()) && a.getLanguage().equalsIgnoreCase(currentLanguage) && StringUtils.isNotBlank(a.getRemark())) {
				dictsuggestionsHandlingMap.put(dictParentMap.get(a.getParentId()) + a.getDictKey(), a);
			}
		});
		return dictsuggestionsHandlingMap;
	}


	/**
	 * 根据语言环境筛选设备类型字典数据
	 *
	 * @param dictBizMap      字典业务数据Map
	 * @param currentLanguage 当前语言环境
	 * @return 包含设备类型的字典映射
	 */
	private Map<String, String> getDictDeviceTypeBizMap(Map<String, List<DictBiz>> dictBizMap,
														String currentLanguage) {
		return dictBizMap.get(DictBizCodeEnum.DEVICE_TYPE.getDictCode()).stream()
			.filter(dictBiz -> dictBiz.getLanguage().equalsIgnoreCase(currentLanguage))
			.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));
	}


	/**
	 * 封装查询结果
	 *
	 * @param alarmLogEntityList   告警列表
	 * @param dictDeviceTypeBizMap 设备类型map
	 * @param dictBizMap           数据字典map
	 * @param currencyLanguage     当前语言
	 * <AUTHOR>
	 * @since 2024/8/13 17:12
	 **/
	public void processAlarms(List<AlarmLogPageVO> alarmLogEntityList, Map<String, String> dictDeviceTypeBizMap,
							  Map<String, List<DictBiz>> dictBizMap, String currencyLanguage) {
		List<String> allSerialNumbers = extractSerialNumbers(alarmLogEntityList);
		Map<String, String> deviceSerialNumberMap = fetchDeviceSerialNumbers(allSerialNumbers, dictBizMap,
			currencyLanguage);
		// 生成处理建议
		Map<String, DictBiz> dictSuggestionsHandlingMap = getDictSuggestionsHandlingMap(alarmLogEntityList, dictBizMap,
			currencyLanguage);
		processAlarmLogEntities(alarmLogEntityList, dictDeviceTypeBizMap, deviceSerialNumberMap,
			dictSuggestionsHandlingMap);
	}

	/**
	 * 从报警日志实体列表中提取所有序列号。
	 *
	 * @param alarmLogEntityList 报警日志实体列表
	 * @return 序列号列表
	 */
	private List<String> extractSerialNumbers(List<AlarmLogPageVO> alarmLogEntityList) {
		return alarmLogEntityList.stream()
			.filter(alarmLogPageVO -> alarmLogPageVO.getExceptionType() != null &&
				(alarmLogPageVO.getExceptionType().equalsIgnoreCase(INVERTER_TYPE) ||
					alarmLogPageVO.getExceptionType().equalsIgnoreCase(BATTERY_TYPE)
				))
			.map(AlarmLogPageVO::getSerialNumber)
			.distinct()
			.collect(Collectors.toList());
	}

	/**
	 * 根据序列号查询设备信息。
	 *
	 * @param serialNumbers    序列号列表
	 * @param dictBizMap       字典数据映射
	 * @param currencyLanguage 当前语言
	 * @return 设备序列号与类型映射
	 */
	private Map<String, String> fetchDeviceSerialNumbers(List<String> serialNumbers,
														 Map<String, List<DictBiz>> dictBizMap,
														 String currencyLanguage) {
		// 创建一个HashMap来存储设备的序列号及其对应的状态
		Map<String, String> deviceSerialNumberMap = new HashMap<>();
		// 检查传入的序列号列表是否为空
		if (!CollectionUtils.isNullOrEmpty(serialNumbers)) {
			List<BatteryMapDeviceEntity> batteryMapDeviceList =
				batteryMapDeviceService.list(Wrappers.<BatteryMapDeviceEntity>lambdaQuery().in(BatteryMapDeviceEntity::getDeviceSerialNumber, serialNumbers));
			List<BatteryExitFactoryInfoEntity> batteryInfoList = null;
			if (!CollectionUtils.isNullOrEmpty(batteryMapDeviceList)) {
				// 收集电池设备列表中的序列号
				List<String> batterySerialNumbers =
					batteryMapDeviceList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
				// 查询电池出口工厂信息，并将结果存储在列表中
				batteryInfoList =
					batteryExitFactoryInfoService.list(Wrappers.<BatteryExitFactoryInfoEntity>lambdaQuery().in(BatteryExitFactoryInfoEntity::getBatterySerialNumber, batterySerialNumbers));
			}
			// 查询逆变器出口工厂信息，并将结果存储在列表中
			List<DeviceExitFactoryInfoEntity> deviceInfoList =
				deviceExitFactoryInfoService.list(Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery().in(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, serialNumbers));
			// 根据设备模式类型字典码获取设备类型映射
			Map<String, String> batteryTypeMap =
				dictBizMap.get(DictBizCodeEnum.BATTERY_MODE_TYPE.getDictCode()).stream()
					.filter(dictBiz -> currencyLanguage.equalsIgnoreCase(dictBiz.getLanguage()))
					.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));

			// 根据设备模式类型字典码获取设备类型映射
			Map<String, String> deviceTypeMap = dictBizMap.get(DictBizCodeEnum.DEVICE_MODE_TYPE.getDictCode()).stream()
				.filter(dictBiz -> currencyLanguage.equalsIgnoreCase(dictBiz.getLanguage()))
				.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));
			// 检查电池设备信息列表是否为空
			if (!CollectionUtils.isNullOrEmpty(batteryInfoList)) {
				// 设置电池的类型
				batteryInfoList.forEach(a -> a.setBatteryType(batteryTypeMap.get(a.getBatteryType())));
				// 将电池序列号和类型添加到设备序列号映射中
				deviceSerialNumberMap.putAll(batteryInfoList.stream().filter(a -> StringUtils.isNotEmpty(a.getBatteryType())).collect(Collectors.toMap(BatteryExitFactoryInfoEntity::getBatterySerialNumber, BatteryExitFactoryInfoEntity::getBatteryType)));
			}
			// 检查逆变器设备信息列表是否为空
			if (!CollectionUtils.isNullOrEmpty(deviceInfoList)) {
				// 设置设备的类型
				deviceInfoList.forEach(a -> a.setDeviceType(deviceTypeMap.get(a.getDeviceType())));
				// 将设备序列号和类型添加到设备序列号映射中
				deviceSerialNumberMap.putAll(deviceInfoList.stream().filter(a -> StringUtils.isNotBlank(a.getDeviceType())).collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType)));
			}
		}
		return deviceSerialNumberMap;
	}

	/**
	 * 处理报警日志实体列表，更新相关信息。
	 *
	 * @param alarmLogEntityList    报警日志实体列表
	 * @param dictDeviceTypeBizMap  异常类型字典映射
	 * @param deviceSerialNumberMap 设备序列号与类型映射
	 */
	private void processAlarmLogEntities(List<AlarmLogPageVO> alarmLogEntityList,
										 Map<String, String> dictDeviceTypeBizMap,
										 Map<String, String> deviceSerialNumberMap,
										 Map<String, DictBiz> dictSuggestionsHandlingMap) {
		alarmLogEntityList.forEach(alarmLogEntity -> {
			// 根据异常类型ID获取异常类型名称
			alarmLogEntity.setExceptionTypeName(dictDeviceTypeBizMap.get(alarmLogEntity.getExceptionType()));
			DictBiz dictBiz =
				dictSuggestionsHandlingMap.get(alarmLogEntity.getAddressCode() + alarmLogEntity.getAlarmNumber());
			// 获取处理建议和异常描述
			if (dictBiz != null) {
				alarmLogEntity.setOperationSuggestion(dictBiz.getRemark());
				alarmLogEntity.setExceptionMessage(dictBiz.getDictValue());
			}
			// 根据设备类型获取对应的类型名称
			String serialNumber = alarmLogEntity.getSerialNumber();
			if (StringUtils.isNotBlank(serialNumber)) {
				if (BATTERY_TYPE.equals(alarmLogEntity.getExceptionType())) {
					List<BatteryMapDeviceEntity> batteryMapDeviceEntityList =
						batteryMapDeviceService.queryListByPlantIdAndSnAndBattery(alarmLogEntity.getPlantId(),
							serialNumber);
					if (CollectionUtil.isNotEmpty(batteryMapDeviceEntityList)) {
						// 获取电池序列号，去重，返回list
						List<String> batterySerialNumberList =
							batteryMapDeviceEntityList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).distinct().collect(Collectors.toList());
						// 遍历batterySerialNumberList，从deviceSerialNumberMap获取对应的电池型号，排除空的和重复的电池型号，再用逗号进行拼接
						String batteryTypeSetStr =
							batterySerialNumberList.stream().filter(a -> StringUtils.isNotEmpty(deviceSerialNumberMap.get(a))).distinct().map(deviceSerialNumberMap::get).distinct().collect(Collectors.joining(","));
						alarmLogEntity.setDeviceType(batteryTypeSetStr);
					}
				} else {
					alarmLogEntity.setDeviceType(deviceSerialNumberMap.get(serialNumber));
				}
			}
		});
	}

	/**
	 * 获取用户异常日志数量
	 *
	 * @param plantId 站点id
	 * @return int
	 * <AUTHOR>
	 * @since 2024/11/21 10:06
	 **/
	@Override
	public int exitsUserAlarmCount(long plantId) {
		return baseMapper.exitsUserAlarmCount(plantId);
	}

	/**
	 * 获取代理商异常日志数量
	 *
	 * @param plantId 站点id
	 * @return int
	 * <AUTHOR>
	 * @since 2024/11/21 10:06
	 **/
	@Override
	public int exitsAgentAlarmCount(long plantId) {
		return baseMapper.exitsAgentAlarmCount(plantId);
	}
}
