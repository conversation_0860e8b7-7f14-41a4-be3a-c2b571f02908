/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.vo.SkuBaseInfoVO;
import java.util.Objects;

/**
 * 物料基础信息表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public class SkuBaseInfoWrapper extends BaseEntityWrapper<SkuBaseInfoEntity, SkuBaseInfoVO>  {

	public static SkuBaseInfoWrapper build() {
		return new SkuBaseInfoWrapper();
 	}

	@Override
	public SkuBaseInfoVO entityVO(SkuBaseInfoEntity skuBaseInfo) {
		SkuBaseInfoVO skuBaseInfoVO = Objects.requireNonNull(BeanUtil.copy(skuBaseInfo, SkuBaseInfoVO.class));

		//User createUser = UserCache.getUser(skuBaseInfo.getCreateUser());
		//User updateUser = UserCache.getUser(skuBaseInfo.getUpdateUser());
		//skuBaseInfoVO.setCreateUserName(createUser.getName());
		//skuBaseInfoVO.setUpdateUserName(updateUser.getName());

		return skuBaseInfoVO;
	}


}
