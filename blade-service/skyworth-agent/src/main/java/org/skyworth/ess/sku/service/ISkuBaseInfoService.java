/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.vo.SkuBaseInfoResultVO;
import org.skyworth.ess.sku.vo.SkuBaseInfoVO;
import org.skyworth.ess.sku.excel.SkuBaseInfoExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * 物料基础信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public interface ISkuBaseInfoService extends BaseService<SkuBaseInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param skuBaseInfo
	 * @return
	 */
	IPage<SkuBaseInfoVO> selectSkuBaseInfoPage(IPage<SkuBaseInfoVO> page, SkuBaseInfoVO skuBaseInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SkuBaseInfoExcel> exportSkuBaseInfo(Wrapper<SkuBaseInfoEntity> queryWrapper);
	R<String> saveSku(SkuBaseInfoEntity skuBaseInfoEntity);

	R<String> updateSku(SkuBaseInfoEntity skuBaseInfoEntity);
	List<SkuBaseInfoEntity> querySkuBaseInfoBySkuCodeList(List<String> skuCodeList);

	/**
	 * 下拉菜单
	 * @param skuBaseInfo
	 * @return
	 */
	R<List<SkuBaseInfoResultVO>> pullDown(SkuBaseInfoVO skuBaseInfo);

	R<SkuBaseInfoVO> basePackageSkuInfo(String basePackage);
}
