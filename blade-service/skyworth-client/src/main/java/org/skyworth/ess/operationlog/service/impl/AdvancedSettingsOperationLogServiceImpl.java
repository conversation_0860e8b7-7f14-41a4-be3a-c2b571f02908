package org.skyworth.ess.operationlog.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.mapper.AdvancedSettingsOperationLogMapper;
import org.skyworth.ess.operationlog.service.IAdvancedSettingsOperationLogService;
import org.skyworth.ess.operationlog.vo.AdvancedSettingsOperationLogVO;
import org.skyworth.ess.timeshift.ITimeShiftService;
import org.skyworth.ess.util.TimeZoneUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 高级设置操作日志 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Service
public class AdvancedSettingsOperationLogServiceImpl extends BaseServiceImpl<AdvancedSettingsOperationLogMapper,
	AdvancedSettingsOperationLogEntity> implements IAdvancedSettingsOperationLogService {

	@Autowired
	private ITimeShiftService timeShiftService;
	@Autowired
	private TimeZoneUtil timeZoneUtil;

	@Override
	public IPage<AdvancedSettingsOperationLogVO> getLogList(IPage<AdvancedSettingsOperationLogVO> page, Map<String,
		Object> map) {
		List<AdvancedSettingsOperationLogVO> logList = baseMapper.getLogList(page, map);
		List<Long> plantIdList =
			logList.stream().map(AdvancedSettingsOperationLogVO::getPlantId).distinct().collect(Collectors.toList());
		timeShiftService.getAndReturnList(logList, "createTime", plantIdList);
		return page.setRecords(logList);
	}

	@Override
	public IPage<AdvancedSettingsOperationLogVO> getWebLogList(IPage<AdvancedSettingsOperationLogVO> page, Map<String,
		Object> map) {
		List<AdvancedSettingsOperationLogVO> logList = baseMapper.getLogList(page, map);
		String timeZone = timeZoneUtil.getUserWebTimeZone();
		// 时区转换
		timeShiftService.getWebAndConvertTimeZones(logList,timeZone,"createTime");
		// 设置时区
		logList.forEach(log -> log.setTimeZone(timeZone));
		return page.setRecords(logList);
	}
}
