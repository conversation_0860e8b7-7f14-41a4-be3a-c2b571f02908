<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceCustomModeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceCustomModeResultMap" type="org.skyworth.ess.device.entity.DeviceCustomModeEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_mode" property="hybridWorkMode"/>
        <result column="once_everyday" property="onceEveryday"/>
        <result column="charge_start_time1" property="chargeStartTime1"/>
        <result column="charge_end_time1" property="chargeEndTime1"/>
        <result column="discharge_start_time1" property="dischargeStartTime1"/>
        <result column="discharge_end_time1" property="dischargeEndTime1"/>
        <result column="capacity_of_charger_end" property="capacityOfChargerEnd"/>
        <result column="capacity_of_discharger_end" property="capacityOfDischargerEnd"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="time_based_control_enable" property="timeBasedControlEnable"/>
    </resultMap>


    <select id="selectDeviceCustomModePage" resultMap="deviceCustomModeResultMap">
        select * from device_custom_mode where is_deleted = 0
    </select>


    <select id="exportDeviceCustomMode" resultType="org.skyworth.ess.device.excel.DeviceCustomModeExcel">
        SELECT * FROM device_custom_mode ${ew.customSqlSegment}
    </select>

    <update id="updateCustomMode">
        update device_custom_mode set
        <if test="params.hybridWorkMode!=null and params.hybridWorkMode!=''">
            device_mode=#{params.hybridWorkMode},
        </if>
        <if test="params.onceEveryday!=null and params.onceEveryday!=''">
            once_everyday=#{params.onceEveryday},
        </if>
        <if test="params.chargeStartTime1!=null and params.chargeStartTime1!=''">
            charge_start_time1=#{params.chargeStartTime1},
        </if>
        <if test="params.chargeEndTime1!=null and params.chargeEndTime1!=''">
            charge_end_time1=#{params.chargeEndTime1},
        </if>
        <if test="params.dischargeStartTime1!=null and params.dischargeStartTime1!=''">
            discharge_start_time1=#{params.dischargeStartTime1},
        </if>
        <if test="params.dischargeEndTime1!=null and params.dischargeEndTime1!=''">
            discharge_end_time1=#{params.dischargeEndTime1},
        </if>
        <if test="params.capacityOfChargerEnd!=null ">
            capacity_of_charger_end=#{params.capacityOfChargerEnd},
        </if>
        <if test="params.capacityOfDischargerEnd!=null ">
            capacity_of_discharger_end=#{params.capacityOfDischargerEnd},
        </if>
        <if test="params.timeBasedControlEnable!=null ">
            time_based_control_enable=#{params.timeBasedControlEnable},
        </if>
        <if test="params.updateUserAccount!=null and params.updateUserAccount!=''">
            update_user_account=#{params.updateUserAccount},
        </if>
        <if test="params.updateUser!=null ">
            update_user=#{params.updateUser},
        </if>
        <if test="params.chargePowerInTime1HighWord!=null and params.chargePowerInTime1HighWord!=''">
            charge_power_in_time1_high_word=#{params.chargePowerInTime1HighWord},
        </if>
        <if test="params.chargeEndSocInTime1!=null and params.chargeEndSocInTime1!=''">
            charge_end_soc_in_time1=#{params.chargeEndSocInTime1},
        </if>
        <if test="params.dischargePowerInTime1HighWord!=null and params.dischargePowerInTime1HighWord!=''">
            discharge_power_in_time1_high_word=#{params.dischargePowerInTime1HighWord},
        </if>
        <if test="params.dischargeEndSocInTime1!=null and params.dischargeEndSocInTime1!=''">
            discharge_end_soc_in_time1=#{params.dischargeEndSocInTime1},
        </if>
        <if test="params.onceEveryday2!=null and params.onceEveryday2!=''">
             once_everyday2 =#{params.onceEveryday2},
        </if>
        <if test="params.chargeStartTime2!=null and params.chargeStartTime2!=''">
             charge_start_time2 =#{params.chargeStartTime2},
        </if>
        <if test="params.chargeEndTime2!=null and params.chargeEndTime2!=''">
             charge_end_time2 =#{params.chargeEndTime2},
        </if>
        <if test="params.chargePowerInTime2HighWord!=null and params.chargePowerInTime2HighWord!=''">
             charge_power_in_time2_high_word =#{params.chargePowerInTime2HighWord},
        </if>
        <if test="params.chargeEndSocInTime2!=null and params.chargeEndSocInTime2!=''">
             charge_end_soc_in_time2 = #{params.chargeEndSocInTime2},
        </if>
        <if test="params.dischargeStartTime2!=null and params.dischargeStartTime2!=''">
             discharge_start_time2 = #{params.dischargeStartTime2},
        </if>
        <if test="params.dischargeEndTime2!=null and params.dischargeEndTime2!=''">
             discharge_end_time2 = #{params.dischargeEndTime2},
        </if>
        <if test="params.dischargePowerInTime2HighWord!=null and params.dischargePowerInTime2HighWord!=''">
             discharge_power_in_time2_high_word = #{params.dischargePowerInTime2HighWord},
        </if>
        <if test="params.dischargeEndSocInTime2!=null and params.dischargeEndSocInTime2!=''">
             discharge_end_soc_in_time2 = #{params.dischargeEndSocInTime2},
        </if>

        <if test="params.onceEveryday3!=null and params.onceEveryday3!=''">
             once_everyday3 = #{params.onceEveryday3},
        </if>
        <if test="params.chargeStartTime3!=null and params.chargeStartTime3!=''">
             charge_start_time3 = #{params.chargeStartTime3},
        </if>
        <if test="params.chargeEndTime3!=null and params.chargeEndTime3!=''">
             charge_end_time3 = #{params.chargeEndTime3},
        </if>
        <if test="params.chargePowerInTime3HighWord!=null and params.chargePowerInTime3HighWord!=''">
             charge_power_in_time3_high_word = #{params.chargePowerInTime3HighWord},
        </if>
        <if test="params.chargeEndSocInTime3!=null and params.chargeEndSocInTime3!=''">
             charge_end_soc_in_time3 = #{params.chargeEndSocInTime3},
        </if>
        <if test="params.dischargeStartTime3!=null and params.dischargeStartTime3!=''">
             discharge_start_time3 = #{params.dischargeStartTime3},
        </if>
        <if test="params.dischargeEndTime3!=null and params.dischargeEndTime3!=''">
             discharge_end_time3 = #{params.dischargeEndTime3},
        </if>
        <if test="params.dischargePowerInTime3HighWord!=null and params.dischargePowerInTime3HighWord!=''">
             discharge_power_in_time3_high_word = #{params.dischargePowerInTime3HighWord},
        </if>
        <if test="params.dischargeEndSocInTime3!=null and params.dischargeEndSocInTime3!=''">
             discharge_end_soc_in_time3 = #{params.dischargeEndSocInTime3},
        </if>
        update_time=now()
        where id = #{params.id}
    </update>
</mapper>
