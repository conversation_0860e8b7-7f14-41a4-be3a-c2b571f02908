package org.springblade.flow.engine.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class FlowRole {

	public static Map<String, String> getRole() {
		Map<String, String> map = new HashMap<>(16);
		map.put("llInfoConfirmation", "012");
		map.put("surveyWoAssign", "018,016");
		map.put("surveyLlVerification", "008,009");
		map.put("siteDesign", "010");
		map.put("designVerification", "017");
		map.put("stockConfirmation", "015");
		map.put("quotes", "011,012");
		map.put("paymentConfirmation", "011,012");
		map.put("installationDate", "012");
		map.put("InstallWoAssignment", "018,016");
		map.put("materialCollection", "008");
		map.put("ehsSubmission", "008");
		map.put("ehsVerification", "013");
		map.put("qcSubmission", "013");
		map.put("qcVerification", "014");
		map.put("temporaryCoc", "009");
		map.put("fac", "008");
		map.put("balancePayment", "011");
		map.put("officialCoc", "012");
		return map;
	}
}

