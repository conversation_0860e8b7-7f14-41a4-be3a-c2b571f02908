package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.aspect.AdvancedSettingsOperationLog;
import org.skyworth.ess.constant.AppSetupTypeEnum;
import org.skyworth.ess.constant.SourceEnum;
import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.skyworth.ess.ota.feign.ITimeZoneCachedClient;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.AESUtils;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description app高级设置实现类
 * @create-time 2023/11/6 11:04:27
 */
@Service
@Slf4j
public class AppSetupServiceImpl implements IAppSetupService {

	@Value("${blade.aes.key}")
	private String aesKey;
	@Resource
	IDictBizClient dictClient;
	@Resource
	TimeZoneDeviceService timeZoneDeviceService;
	@Resource
	private ITimeZoneCachedClient timeZoneCachedClient;
	@Resource
	private BladeRedis bladeRedis;


	@Override
	public JSONObject getAdvancedPwd() {
		List<DictBiz> appAdvancedSetupPassword = dictClient.getListAllLang("appAdvancedSetupPassword").getData();
		JSONObject jsonObject = new JSONObject();
		if (ObjectUtil.isNotNull(appAdvancedSetupPassword) && !appAdvancedSetupPassword.isEmpty()) {
			DictBiz dictBiz = appAdvancedSetupPassword.get(0);
			try {
				String encrypt = AESUtils.encrypt(dictBiz.getDictValue(), aesKey);
				jsonObject.put(dictBiz.getDictKey(), encrypt);
				return jsonObject;
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
		return jsonObject;
	}


	@Override
	public JSONObject getSetupItem(AppSetRequestVO appSetRequestVO) {
		AppSetupTemplate appSetupTemplateByType = AppSetupTypeEnum.getAppSetupTemplateByNumType(appSetRequestVO.getSetCategory());
		return appSetupTemplateByType.getSetupItem(appSetRequestVO);
	}

	@Override
	public R issueSetup(AppAdvancedSetup deviceAdvancedSetup) {
		AppSetupTemplate appSetupTemplateByType = AppSetupTypeEnum.getAppSetupTemplateByType(deviceAdvancedSetup.getIssueSetupType());
		return appSetupTemplateByType.issueSetupCommon(deviceAdvancedSetup);
	}

	@AdvancedSettingsOperationLog(type = SourceEnum.OTHER)
	@Override
	public R issueSetupByAutoAdjust(AppAdvancedSetup deviceAdvancedSetup) {
		AppSetupTemplate appSetupTemplateByType = AppSetupTypeEnum.getAppSetupTemplateByType(deviceAdvancedSetup.getIssueSetupType());
		return appSetupTemplateByType.issueSetupCommon(deviceAdvancedSetup);
	}

	@Override
	public R setTimeZone(AppAdvancedSetup deviceAdvancedSetup) {
		boolean result = true;
		String timeZone = deviceAdvancedSetup.getTimeZone();
		if (ValidationUtil.isNotEmpty(timeZone)) {
			Long plantId = deviceAdvancedSetup.getPlantId();
			LambdaQueryWrapper<TimeZoneDevice> queryWrapper = Wrappers.<TimeZoneDevice>query().lambda();
			queryWrapper.eq(TimeZoneDevice::getPlantId, plantId);
			TimeZoneDevice timeZoneDeviceServiceOne = timeZoneDeviceService.getOne(queryWrapper);
			TimeZoneDevice timeZoneDevice = new TimeZoneDevice();
			if (ObjectUtil.isNotNull(timeZoneDeviceServiceOne)) {
				// 如果有历史数据则设置id，mybatis-plus走update方法
				timeZoneDevice.setId(timeZoneDeviceServiceOne.getId());
			}

			String normalizeTimeZone = DateUtil.normalizeTimeZone(timeZone);
			if (ValidationUtil.isNotEmpty(normalizeTimeZone)){
				timeZone = normalizeTimeZone;
			}
			// 无数据，走save方法
			timeZoneDevice.setPlantId(deviceAdvancedSetup.getPlantId());
			timeZoneDevice.setTimeZone(timeZone);
			timeZoneDevice.setUpdateTime(new Date());
			result = timeZoneDeviceService.saveOrUpdate(timeZoneDevice);

			if (result){
				Long plantIdLong = deviceAdvancedSetup.getPlantId();
				timeZoneCachedClient.putCache(String.valueOf(plantIdLong), timeZone);
				bladeRedis.setEx(CacheNames.SET_TIMEZONE_SUCCESS_FLAG_KEY + plantIdLong,timeZone,100L);
			}
		}
		return R.status(result);
	}
}
