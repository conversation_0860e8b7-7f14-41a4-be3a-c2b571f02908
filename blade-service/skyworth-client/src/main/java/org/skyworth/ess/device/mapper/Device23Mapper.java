package org.skyworth.ess.device.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.excel.Device23Excel;
import org.skyworth.ess.device.vo.Device23VO;
import org.springblade.core.mp.mapper.BladeMapper;

import java.util.List;
import java.util.Map;

public interface Device23Mapper extends BladeMapper<Device23Entity> {
    List<Device23Entity> getEntity(@Param("ew") Wrapper<Device23Entity> queryWrapper);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param Device23
	 * @return
	 */
	List<Device23VO> selectDevice23Page(IPage page, Device23VO Device23);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<Device23Excel> exportDevice23(@Param("ew") Wrapper<Device23Entity> queryWrapper);

    int updateSetup(@Param("device23Map") Map<String, Object> device23,@Param("plantId") Long plantId,@Param("deviceSerialNumber") String deviceSerialNumber);

	int updateByPlantId(DeviceCustomModeEntity deviceCustomModeEntity);

	int updatePlantMode(@Param("plantId") Long plantId,@Param("deviceSerialNumber") String deviceSerialNumber);
}
