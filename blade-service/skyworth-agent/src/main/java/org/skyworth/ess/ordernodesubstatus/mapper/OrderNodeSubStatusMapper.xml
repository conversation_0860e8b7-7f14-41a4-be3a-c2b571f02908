<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.ordernodesubstatus.mapper.OrderNodeSubStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="orderNodeSubStatusResultMap" type="org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="business_type" property="businessType"/>
        <result column="node_name" property="nodeName"/>
        <result column="node_version" property="nodeVersion"/>
        <result column="sub_node_name" property="subNodeName"/>
        <result column="sub_status" property="subStatus"/>
        <result column="sub_remark" property="subRemark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>
    <delete id="deleteByOrderId">
        delete from business_order_node_sub_status  where order_id=#{orderId} and business_type=#{bizType} and node_name=#{nodeName}
    </delete>


    <select id="selectOrderNodeSubStatusPage" resultMap="orderNodeSubStatusResultMap">
        select * from business_order_node_sub_status where is_deleted = 0
    </select>


    <select id="exportOrderNodeSubStatus" resultType="org.skyworth.ess.ordernodesubstatus.excel.OrderNodeSubStatusExcel">
        SELECT * FROM business_order_node_sub_status ${ew.customSqlSegment}
    </select>
    <select id="getNodeByOrderId"
            resultMap="orderNodeSubStatusResultMap">
        select sub_node_name,sub_status,sub_remark from business_order_node_sub_status where order_id=#{orderId} and business_type=#{businessType} and node_name='QCVerification' and is_deleted=0
    </select>
    <select id="getWorkFlowInfoByOrderId" resultType="java.util.Map">
        select wf_instance_id ,task_id ,wf_current_status  from business_order_work_flow  where order_id=#{orderId} limit 1

    </select>

</mapper>
