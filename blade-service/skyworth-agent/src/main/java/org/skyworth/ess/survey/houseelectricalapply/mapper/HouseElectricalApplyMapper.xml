<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.survey.houseelectricalapply.mapper.HouseElectricalApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="houseElectricalApplyResultMap" type="org.skyworth.ess.survey.houseelectricalapply.entity.HouseElectricalApplyEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="apply_type" property="applyType"/>
        <result column="apply_qty" property="applyQty"/>
        <result column="apply_capacity" property="applyCapacity"/>
        <result column="apply_img_biz_key" property="applyImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectHouseElectricalApplyPage" resultMap="houseElectricalApplyResultMap">
        select * from survey_house_electrical_apply where is_deleted = 0
    </select>


    <select id="exportHouseElectricalApply" resultType="org.skyworth.ess.survey.houseelectricalapply.excel.HouseElectricalApplyExcel">
        SELECT * FROM survey_house_electrical_apply ${ew.customSqlSegment}
    </select>

</mapper>
