/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 设备/逆变器每日统计 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceEverydayTotalExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 设备/逆变器时间，设备/逆变器上报时时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器时间，设备/逆变器上报时时间")
	private Date deviceDateTime;
	/**
	 * 统计日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("统计日期")
	private String totalDate;
	/**
	 * 发电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("发电能量")
	private BigDecimal todayEnergy;
	/**
	 * 输入能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("输入能量")
	private BigDecimal todayImportEnergy;
	/**
	 * 输出能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("输出能量")
	private BigDecimal todayExportEnergy;
	/**
	 * 负载能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("负载能量")
	private BigDecimal todayLoadEnergy;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
