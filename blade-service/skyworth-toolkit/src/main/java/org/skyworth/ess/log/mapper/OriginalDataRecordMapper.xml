<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.log.mapper.OriginalDataRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="DeviceCurrentStatusResultMap" type="org.skyworth.ess.log.entity.OriginalDataRecord">
        <result column="id" property="id"/>
        <result column="data" property="data"/>
        <result column="type" property="type"/>
        <result column="exception_info" property="exceptionInfo"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>
    <select id="isExistPlant" resultType="java.util.Map">
        select device_serial_number from wifi_stick_plant  where plant_id=#{plantId} and device_serial_number=#{deviceSn} and is_deleted=0
    </select>
    <select id="getTimeZone" resultType="java.util.Map">
        select time_zone, device_serial_number from time_zone_device  where device_serial_number=#{deviceSn} limit 1
    </select>
    <select id="getStartupByBackstage" resultType="java.util.Map">
        select startup_by_backstage from wifi_stick_plant where plant_id=#{plantId} and device_serial_number=#{deviceSn}  and is_deleted=0
    </select>

    <update id="updateWifiAndPlant">
        update wifi_stick_plant set inverter_configure_network=0  where plant_id=#{plantId} and device_serial_number=#{deviceSn}  and is_deleted=0
    </update>

    <select id="getTimeZones" resultType="com.alibaba.fastjson.JSONObject">
        select
        wsp.device_serial_number,
        b.time_zone  as time_zone,
        a.country_code
        from
        plant A
        left join wifi_stick_plant wsp on
        a.id = wsp.plant_id
        left join time_zone_device B on
        wsp.device_serial_number = b.device_serial_number and b.is_deleted = 0
        where
        a.is_deleted = 0
        and wsp.is_deleted = 0
        and wsp.device_serial_number in
        <foreach collection="deviceSnList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="isParallelModeList" resultType="java.util.Map">
        select id as plantId, is_parallel_mode as modeType from plant where id in
        <foreach collection="plantIds" item="plantId" open="(" separator="," close=")">
            #{plantId}
        </foreach>
        and is_deleted=0
    </select>

</mapper>
