/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.skyworth.ess.sku.entity.SkuHistoryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 物料记录表，每次新增写入一条 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SkuHistoryVO extends SkuHistoryEntity {
	private static final long serialVersionUID = 1L;
	// 开始时间
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date beginDate;
	// 结束时间
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date endDate;
	// 查询开始日期之前数据
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date beforeBeginDate;
	// 期初数量
	private Long initQuantity = 0L;
	// 入库数量
	private Long inQuantity = 0L;
	// 出库数量
	private Long outQuantity = 0L;
	// 结存数量
	private Long surplusQuantity = 0L;
	private String skuDeviceType;
	/**
	 * 物料名称
	 */
	private String skuName;
	/**
	 * 物料厂商（sku_company）
	 */
	private String skuCompany;
	/**
	 * 规格
	 */
	private String standards;
	/**
	 * 型号
	 */
	private String model;
	/**
	 * 单位
	 */
	private String unit;

}
