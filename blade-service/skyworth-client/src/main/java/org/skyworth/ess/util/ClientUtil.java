package org.skyworth.ess.util;

import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;

/**
 * <AUTHOR> @version 1.0
 * @project
 * @description
 * @create-time 2024/2/5 14:14:49
 */
public class ClientUtil {

	public static Long getSnowFlakeId(){
		IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
		Number number = identifierGenerator.nextId(new Object());
		return number.longValue();
	}
}
