/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.jobhandler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.service.IDeviceSoftwareVersionInfoService;
import org.springframework.stereotype.Component;

/**
 * @description: 设备升级
 * @author: SDT50545
 * @since: 2023-09-21 18:00
 **/
@Component
@Slf4j
@AllArgsConstructor
public class OtaDeviceUpgradeXxlJob {
	private final IDeviceSoftwareVersionInfoService deviceSoftwareVersionInfoService;


	/**
	 * 下发软件新版本地址到设备端
	 *
	 * @param param 入参
	 * @return ReturnT<String>
	 * <AUTHOR>
	 * @since 2023/9/22 10:03
	 **/
	@XxlJob("otaDeviceUpgrade")
	public ReturnT<String> otaDeviceUpgrade(String param) throws Exception {
		deviceSoftwareVersionInfoService.circularProcessingUpgradeRecords();
		return ReturnT.SUCCESS;
	}

	/**
	 * 修改升级状态，针对升级下发后，长期没响应结果以及响应失败数据修改状态
	 *
	 * @param param 入参
	 * @return ReturnT<String>
	 * <AUTHOR>
	 * @since 2023/9/22 10:03
	 **/
	@XxlJob("processingTimeoutStatus")
	public ReturnT<String> processingTimeoutStatus(String param) throws Exception {
		deviceSoftwareVersionInfoService.processingTimeoutStatus();
		return ReturnT.SUCCESS;
	}

}
