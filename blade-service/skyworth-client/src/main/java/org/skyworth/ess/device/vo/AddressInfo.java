package org.skyworth.ess.device.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/3/5 16:34:39
 */
@Data
public class AddressInfo extends SkyWorthEntity {
	@ApiModelProperty(value = "国家码")
	private String countryCode;
	@ApiModelProperty(value = "一级行政区码")
	private String provinceCode;
	@ApiModelProperty(value = "二级行政区码")
	private String cityCode;
	@ApiModelProperty(value = "三级行政区码")
	private String countyCode;
	@ApiModelProperty(value = "国家码")
	private String countryName;
	@ApiModelProperty(value = "一级行政区码")
	private String provinceName;
	@ApiModelProperty(value = "二级行政区码")
	private String cityName;
	@ApiModelProperty(value = "三级行政区码")
	private String countyName;
	@ApiModelProperty(value = "详细地址")
	private String detailAddress;
	@ApiModelProperty(value = "行政区地址")
	private String address;
}
