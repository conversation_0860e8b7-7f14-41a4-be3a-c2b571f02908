/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.wrapper;

import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 电池每日统计 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public class BatteryEverydayTotalWrapper extends BaseEntityWrapper<BatteryEverydayTotalEntity, BatteryEverydayTotalVO>  {

	public static BatteryEverydayTotalWrapper build() {
		return new BatteryEverydayTotalWrapper();
 	}

	@Override
	public BatteryEverydayTotalVO entityVO(BatteryEverydayTotalEntity batteryEverydayTotal) {
		if (batteryEverydayTotal == null) {
			return null;
		}
		BatteryEverydayTotalVO batteryEverydayTotalVO = Objects.requireNonNull(BeanUtil.copy(batteryEverydayTotal, BatteryEverydayTotalVO.class));

		//User createUser = UserCache.getUser(batteryEverydayTotal.getCreateUser());
		//User updateUser = UserCache.getUser(batteryEverydayTotal.getUpdateUser());
		//batteryEverydayTotalVO.setCreateUserName(createUser.getName());
		//batteryEverydayTotalVO.setUpdateUserName(updateUser.getName());

		return batteryEverydayTotalVO;
	}


}
