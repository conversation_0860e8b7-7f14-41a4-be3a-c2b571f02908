<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.company.mapper.AgentCompanyInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentCompanyInfoResultMap" type="org.skyworth.ess.company.entity.AgentCompanyInfoEntity">
        <result column="id" property="id"/>
        <result column="agent_number" property="agentNumber"/>
        <result column="company_name" property="companyName"/>
        <result column="dept_id" property="deptId"/>
        <result column="company_address" property="companyAddress"/>
        <result column="registration_time" property="registrationTime"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="agent_area" property="agentArea"/>
        <result column="agent_fee" property="agentFee"/>
        <result column="bank_account_name" property="bankAccountName"/>
        <result column="invoicing_bank" property="invoicingBank"/>
        <result column="invoicing_info" property="invoicingInfo"/>
        <result column="taxpayer_identification_number" property="taxpayerIdentificationNumber"/>
        <result column="controller_name" property="controllerName"/>
        <result column="controller_contact" property="controllerContact"/>
        <result column="legal_person_name" property="legalPersonName"/>
        <result column="legal_person_phone" property="legalPersonPhone"/>
        <result column="legal_person_id" property="legalPersonId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>

    <select id="validAgentHasInTransitOrders" resultType="java.lang.String">
        select group_concat(distinct aci.company_name)
        from agent_company_info aci
        left join business_order bo on
        aci.id = bo.distributor_id
        left join business_order_work_flow bowf on
        bo.id = bowf.order_id
        where aci.is_deleted = 0
        and bo.is_deleted = 0
        and bowf.is_deleted = 0
        and bowf.wf_current_status not in ('cancel', 'maintenance')
        <if test="ids!=null and ids.size>0">
            and aci.id in
            <foreach collection="ids" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="deleteBatch">
        update
        agent_company_info a
        set
        a.is_deleted = 1 ,
        update_user = #{updateUser},
        update_time = now()
        where a.is_deleted = 0
        <if test="ids!=null and ids.size>0">
            and a.id in
            <foreach collection="ids" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        ;
        update
        agent_user_info a
        set
        a.is_deleted = 1 ,
        update_user = #{updateUser},
        update_time = now()
        where
        a.is_deleted = 0
        <if test="ids!=null and ids.size>0">
            and a.agent_id in
            <foreach collection="ids" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        ;
        update
        agent_file_info a
        set
        a.is_deleted = 1 ,
        update_user = #{updateUser},
        update_time = now()
        where
        a.is_deleted = 0
        <if test="ids!=null and ids.size>0">
            and a.agent_id in
            <foreach collection="ids" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        ;
    </update>

</mapper>
