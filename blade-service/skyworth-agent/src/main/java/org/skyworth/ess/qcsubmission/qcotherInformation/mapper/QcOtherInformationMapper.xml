<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.qcsubmission.qcotherInformation.mapper.QcOtherInformationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="otherInformationResultMap" type="org.skyworth.ess.qcsubmission.qcotherInformation.entity.QcOtherInformationEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="ge_grounding_equiment_img_biz_key" property="geGroundingCableImgBizKey"/>
        <result column="ge_ground_continuity_img_biz_key" property="geLightningRodImgBizKey"/>
        <result column="ge_ground_system_connet_img_biz_key" property="geGroundSystemConnetImgBizKey"/>
        <result column="ge_grounding_value_db_img_biz_key" property="geGroundingValueDbImgBizKey"/>
        <result column="ge_grounding_value_panels_img_biz_key" property="geGroundingValuePanelsImgBizKey"/>
        <result column="sc_warning_signs_img_biz_key" property="scWarningSignsImgBizKey"/>
        <result column="st_confirm_inverter_status_img_biz_key" property="stConfirmInverterStatusImgBizKey"/>
        <result column="st_verify_power_out_img_biz_key" property="stVerifyPowerOutImgBizKey"/>
        <result column="st_perform_test_img_biz_key" property="stPerformTestImgBizKey"/>
        <result column="fi_completed_install_out_img_biz_key" property="fiCompletedInstallOutImgBizKey"/>
        <result column="fi_completed_install_in_img_biz_key" property="fiCompletedInstallInImgBizKey"/>
        <result column="fi_environ_clean_img_biz_key" property="fiEnvironCleanImgBizKey"/>
        <result column="fi_recommendations" property="fiRecommendations"/>
        <result column="fi_qvwi_tv_img_biz_key" property="fiQvwiTvImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectQcOtherInformationPage" resultMap="otherInformationResultMap">
        select * from qc_other_information where is_deleted = 0
    </select>


    <select id="exportQcOtherInformation" resultType="org.skyworth.ess.qcsubmission.qcotherInformation.excel.QcOtherInformationExcel">
        SELECT * FROM qc_other_information ${ew.customSqlSegment}
    </select>

</mapper>
