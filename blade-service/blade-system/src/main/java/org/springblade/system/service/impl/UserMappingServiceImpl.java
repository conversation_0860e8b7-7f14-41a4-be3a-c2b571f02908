/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.system.entity.UserMappingEntity;
import org.springblade.system.mapper.UserMappingMapper;
import org.springblade.system.service.IUserMappingService;
import org.springblade.system.vo.UserInfoMappingVO;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Service
public class UserMappingServiceImpl extends BaseServiceImpl<UserMappingMapper, UserMappingEntity> implements IUserMappingService {

	@Override
	public IPage<UserInfoMappingVO> selectUserMappingPage(IPage<UserInfoMappingVO> page, UserInfoMappingVO userMapping) {
		return page.setRecords(baseMapper.selectUserMappingPage(page, userMapping));
	}

	@Override
	public UserMappingEntity getUserMappingIgnoreTenant(UserMappingEntity entity) {
		return baseMapper.getUserMappingIgnoreTenant(entity);
	}

	@Override
	public List<UserMappingEntity> batchGetUserMappingIgnoreTenant(List<UserMappingEntity> list) {
		return baseMapper.batchGetUserMappingIgnoreTenant(list);
	}

	@Override
	public int batchDeleteUserMappingIgnoreTenant(List<Long> list, String tenantId) {
		return baseMapper.batchDeleteUserMappingIgnoreTenant(list, tenantId);
	}
}
