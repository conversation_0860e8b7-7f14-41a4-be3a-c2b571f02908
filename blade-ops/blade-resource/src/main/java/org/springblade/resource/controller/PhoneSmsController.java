package org.springblade.resource.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.resource.service.IPhoneSmsService;
import org.springblade.resource.vo.SmsVO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@RequestMapping("/phone/sms")
@Api(value = "获取手机短信接口", tags = "获取手机短信接口")
public class PhoneSmsController extends BladeController {
    private final IPhoneSmsService phoneSmsService;

    @PostMapping("/verificationCode")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "获取手机验证码", notes = "获取手机验证码")
    public R getPhoneVerificationCode(@RequestBody SmsVO smsVO) {
        return phoneSmsService.getPhoneVerificationCode(smsVO);
    }

	@GetMapping("/encryptedValue")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "", notes = "")
	public R getEncryptedValue() {
		return phoneSmsService.getEncryptedValue(new JSONObject());
	}
}
