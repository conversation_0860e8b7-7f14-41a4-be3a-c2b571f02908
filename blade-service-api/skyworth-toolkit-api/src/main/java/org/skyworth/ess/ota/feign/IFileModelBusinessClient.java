package org.skyworth.ess.ota.feign;


import org.skyworth.ess.ota.constant.ToolkitConstant;
import org.skyworth.ess.ota.vo.FileResultVO;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName IFileModelBusinessClient
 * @Description
 * @Date 2024/3/29 10:59
 */
@FeignClient(
        value = ToolkitConstant.APPLICATION_TOOLKIT_NAME
        , fallback = FileModelBusinessClientCallBack.class
)
public interface IFileModelBusinessClient {

    String API_PREFIX_FILE_UPLOAD = "/file";
    String UPLOAD_FILE = API_PREFIX_FILE_UPLOAD + "/upload";

    @PostMapping(value = UPLOAD_FILE, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    R<FileResultVO> uploadFile(@RequestParam("container") String container, @RequestParam("type") int type,
                               @RequestParam(value = "businessId", required = false) Long businessId, @RequestPart("file") MultipartFile[] file);

}
