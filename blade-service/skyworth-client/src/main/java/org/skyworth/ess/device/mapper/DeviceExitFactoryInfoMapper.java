/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.mapper;

import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.device.excel.DeviceExitFactoryInfoExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备/逆变器出厂信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface DeviceExitFactoryInfoMapper extends BaseMapper<DeviceExitFactoryInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param DeviceExitFactoryInfo
	 * @return
	 */
	List<DeviceExitFactoryInfoVO> selectDeviceExitFactoryInfoPage(IPage page, DeviceExitFactoryInfoVO DeviceExitFactoryInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceExitFactoryInfoExcel> exportDeviceExitFactoryInfo(@Param("ew") Wrapper<DeviceExitFactoryInfoEntity> queryWrapper);

	List<DeviceExitFactoryInfoVO> queryDeviceExitFactoryByPlant(@Param("plantId") Long plantId);
	boolean deleteLogicDeviceExitFactory(@Param("updateUser") Long updateUser, @Param("updateUserAccount") String updateUserAccount
			,@Param("ids")List<Long> ids);

	List<DeviceExitFactoryInfoEntity> queryByDeviceSerialNumbers(@Param("list") List<String> list);

	int updateBatchBySn(@Param("list")List<DeviceExitFactoryInfoEntity> list);

	List<DeviceExitFactoryInfoEntity> queryByIds(@Param("list") List<Long> list);

	BigDecimal queryRatedTotalPowerOfInverter();

	int updateNewQualityQuaranteeYear(@Param("params")DeviceExitFactoryInfoEntity entity);

    List<DeviceExitFactoryInfoEntity> getListByDeviceSerialNumberCollect(@Param("list") List<String> deviceSerialNumberCollect);
}
