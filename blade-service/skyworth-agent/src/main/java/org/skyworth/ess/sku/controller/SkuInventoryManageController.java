/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.sku.entity.SkuInventoryManageEntity;
import org.skyworth.ess.sku.vo.SkuInventoryManageVO;
import org.skyworth.ess.sku.excel.SkuInventoryManageExcel;
import org.skyworth.ess.sku.wrapper.SkuInventoryManageWrapper;
import org.skyworth.ess.sku.service.ISkuInventoryManageService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 控制器
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@RestController
@AllArgsConstructor
@RequestMapping("/skuInventoryManage")
@Api(value = "物料库存管理，sku唯一，数量发货时扣除，新增时累加", tags = "物料库存管理，sku唯一，数量发货时扣除，新增时累加接口")
public class SkuInventoryManageController extends BladeController {

	private final ISkuInventoryManageService skuInventoryManageService;

	/**
	 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入skuInventoryManage")
	@PreAuth("hasPermission('agent:skuInventoryManage:detail')")
	public R<SkuInventoryManageVO> detail(SkuInventoryManageEntity skuInventoryManage) {
		SkuInventoryManageEntity detail = skuInventoryManageService.getOne(Condition.getQueryWrapper(skuInventoryManage));
		return R.data(SkuInventoryManageWrapper.build().entityVO(detail));
	}
	/**
	 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入skuInventoryManage")
	@PreAuth("hasPermission('agent:skuInventoryManage:list')")
	public R<IPage<SkuInventoryManageVO>> list(@ApiIgnore @RequestParam Map<String, Object> skuInventoryManage, Query query) {
		IPage<SkuInventoryManageEntity> pages = skuInventoryManageService.page(Condition.getPage(query), Condition.getQueryWrapper(skuInventoryManage, SkuInventoryManageEntity.class));
		return R.data(SkuInventoryManageWrapper.build().pageVO(pages));
	}

	/**
	 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 自定义分页
	 */
	@GetMapping("/page/{size}/{current}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入skuInventoryManage")
	@PreAuth("hasPermission('agent:skuInventoryManage:page')")
	public R<IPage<SkuInventoryManageVO>> page(@RequestBody SkuInventoryManageVO skuInventoryManage, Query query) {
		IPage<SkuInventoryManageVO> pages = skuInventoryManageService.selectSkuInventoryManagePage(Condition.getPage(query), skuInventoryManage);
		return R.data(pages);
	}

	/**
	 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入skuInventoryManage")
	@PreAuth("hasPermission('agent:skuInventoryManage:save')")
	public R save(@Valid @RequestBody SkuInventoryManageEntity skuInventoryManage) {
		return R.status(skuInventoryManageService.save(skuInventoryManage));
	}

	/**
	 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入skuInventoryManage")
	@PreAuth("hasPermission('agent:skuInventoryManage:update')")
	public R update(@Valid @RequestBody SkuInventoryManageEntity skuInventoryManage) {
		return R.status(skuInventoryManageService.updateById(skuInventoryManage));
	}

	/**
	 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入skuInventoryManage")
	@PreAuth("hasPermission('agent:skuInventoryManage:submit')")
	public R submit(@Valid @RequestBody SkuInventoryManageEntity skuInventoryManage) {
		return R.status(skuInventoryManageService.saveOrUpdate(skuInventoryManage));
	}

	/**
	 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('agent:skuInventoryManage:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(skuInventoryManageService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-skuInventoryManage")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入skuInventoryManage")
	@PreAuth("hasPermission('agent:skuInventoryManage:export-skuInventoryManage')")
	public void exportSkuInventoryManage(@ApiIgnore @RequestParam Map<String, Object> skuInventoryManage, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SkuInventoryManageEntity> queryWrapper = Condition.getQueryWrapper(skuInventoryManage, SkuInventoryManageEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SkuInventoryManage::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(SkuInventoryManageEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SkuInventoryManageExcel> list = skuInventoryManageService.exportSkuInventoryManage(queryWrapper);
		ExcelUtil.export(response, "物料库存管理，sku唯一，数量发货时扣除，新增时累加数据" + DateUtil.time(), "物料库存管理，sku唯一，数量发货时扣除，新增时累加数据表", list, SkuInventoryManageExcel.class);
	}

}
