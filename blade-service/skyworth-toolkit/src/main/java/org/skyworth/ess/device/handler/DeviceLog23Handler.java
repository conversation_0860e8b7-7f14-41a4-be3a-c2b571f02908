package org.skyworth.ess.device.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.BinaryConvertUtils2;
import org.skyworth.ess.device.DeviceLogService;
import org.skyworth.ess.device.client.IDeviceLog23Client;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.DeviceLog23;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.skyworth.ess.log.service.OriginalDataRecordService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 处理所上报2.3的数据
 * */
@Component("DeviceLog2_" + Constants.THIRD)
@Slf4j
@AllArgsConstructor
public class DeviceLog23Handler  implements DeviceLogService {

	private BinaryConvertUtils2 binaryConvertUtils;

	private IDeviceLog23Client deviceLog23Client;

	private OriginalDataRecordService recordService;


	@Override
	public void handler(List<JSONObject> dataList) {
			List<DeviceLog23> deviceLog23List=new ArrayList<>();
			dataList.stream().parallel().forEach(data->{
				try {
					long plantId = Long.parseLong(data.getString("plantID"));
					String modbusProtocolVersion = data.getString("commuVersion");
					String deviceSerialNumber = data.getString("deviceSn");
					String content = data.getString("content");
					Long timeStamp = data.getLong("timeStamp");
					String timeZone = data.getString("timeZone");
					Date deviceDateTime = DateUtil.convertLongTimeZoneForDeviceUpload(timeStamp,timeZone,CommonConstant.COMMON_DEFAULT_TIME_ZONE);
					JSONObject result = binaryConvertUtils.getData(content, Constants.THIRD, modbusProtocolVersion);
					if (result != null) {
						DeviceLog23 deviceLog23 = JSON.toJavaObject(result, DeviceLog23.class);
						deviceLog23.setPlantId(plantId);
						deviceLog23.setDeviceDateTime(deviceDateTime);
						deviceLog23.setModbusProtocolVersion(modbusProtocolVersion);
						deviceLog23.setDeviceSerialNumber(deviceSerialNumber);
						Date currentTime = new Date();
						deviceLog23.setCreateTime(currentTime);
						deviceLog23.setUpdateTime(currentTime);
						deviceLog23.setIsDeleted(0);
						deviceLog23.setSynchStatus("Y");
						deviceLog23.setTenantId(CommonConstant.CLIENT_TENANT_ID);
						deviceLog23List.add(deviceLog23);
					}
				}catch (Exception e){
					//将数据插入记录表中  保留异常信息
					OriginalDataRecord record=new OriginalDataRecord();
					record.setData(data.toJSONString());
					record.setType("1");
					record.setExceptionInfo(e.getMessage());
					recordService.save(record);
				}
			});

			try{
				if(!deviceLog23List.isEmpty()){
					deviceLog23Client.insertBatchDeviceLog23Column(deviceLog23List);
				}
			}catch (Exception e){
				throw new RuntimeException("deviceLog23Handler is exception");
			}
	}
}
