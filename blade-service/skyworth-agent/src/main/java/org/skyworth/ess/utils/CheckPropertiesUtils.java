package org.skyworth.ess.utils;

import org.apache.commons.collections4.map.HashedMap;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 属性验证 工具类
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
public class CheckPropertiesUtils {
	public static void checkProperties(Object object, List<String> checkProperties) {
		Class<?> clazz = object.getClass();
		Field[] fields = clazz.getDeclaredFields();
		Map<String, Object> properties = new HashedMap<String, Object>();
		if (fields.length > 0) {
			getField(object, fields, properties);
		}
		checkProperties.forEach(e -> {
			if (properties.get(e) instanceof String) {
				if (Objects.isNull(properties.get(e)) || StringUtils.isEmpty(properties.get(e).toString())) {
					throw new BusinessException(e + " is empty");
				}
			} else {
				if (Objects.isNull(properties.get(e))) {
					throw new BusinessException(e + " is empty");
				}
			}

		});
	}

	private static void getField(Object object, Field[] fields, Map<String, Object> properties) {
		try {
			for (Field field : fields) {
				field.setAccessible(true);
				properties.put(field.getName(), field.get(object));
			}
		} catch (Exception e) {
			throw new BusinessException("parse properties failure");
		}
	}

}
