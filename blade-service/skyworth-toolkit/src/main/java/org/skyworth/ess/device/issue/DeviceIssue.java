package org.skyworth.ess.device.issue;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.handler.DeviceLog22Handler;
import org.skyworth.ess.enums.QosEnum;
import org.skyworth.ess.kafka.consumer.ErrorInfoKafkaHandler;
import org.skyworth.ess.mqtt.MqttClientFactory;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备下发
 * */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@Slf4j
public class DeviceIssue implements IDeviceIssueBiz {

    private MqttClientFactory mqttClientFactory;

	private BladeRedis bladeRedis;

	private DeviceLog22Handler deviceLog22Handler;

	private ErrorInfoKafkaHandler kafkaDataHandler;

	/**
	 * 下发工作模式
	 * */
    @PostMapping(address)
    @Override
    public Map<String,String> dataIssueToDevice(@RequestBody JSONObject jsonObject) {
		Map<String,String> map=new HashMap<>();
        try {
			commonInstruct(jsonObject);
		} catch (Exception e) {
			e.printStackTrace();
            log.error("dataIssueToDevice " + e.getMessage());
			map.put("500","error");
            return map;
        }
		map.put("0","success");
        return map;
    }

	private void commonInstruct(JSONObject jsonObject) throws Exception {
		String topic= jsonObject.getString("topic");
		jsonObject.remove("topic");
		mqttClientFactory.getClient().publish(topic,JSON.toJSONString(jsonObject), QosEnum.QOS2.getCategory());
	}


	/**
	 * 下发设备配置
	 * */
	@PostMapping(setting)
	@Override
	public Map<String, String> setting(JSONObject jsonObject) {
		try {
			return ThreadPoolCustom.getCustomThreadPool().submit(() -> ordinarySettingFunction(jsonObject)).get();
		} catch (Exception e) {
			log.error("DeviceIssue error {} ",e.getMessage());
		}
		return Map.of("500","Service error");
	}

	@NotNull
	private Map<String, String> ordinarySettingFunction(JSONObject jsonObject) {
		Map<String,String> map=new HashMap<>();
		String requestId= jsonObject.getString("requestId");
		try {
			commonInstruct(jsonObject);
			int num=20;
			int i=1;
			boolean flag=false;
			while (i<=num){
				Integer code=bladeRedis.hGet(requestId,"code");
				if(code==null){
					try {
						Thread.sleep(800
						);
					} catch (InterruptedException e) {
						throw new RuntimeException(e);
					}
					++i;
				}else {
					flag=true;
					if(code==1){//成功
						map.put("200","setting success");
					}else {//失败
						String errorAddress=bladeRedis.hGet(requestId,"errorAddress");
						if(StringUtils.isNotEmpty(errorAddress)){
							map.put("401",errorAddress);
						}else {
							map.put("402","setting fail");
						}
					}
					break;
				}
			}
			if(!flag){
				map.put("400","request timeout");
			}
		} catch (Exception e) {
			log.error("DeviceIssue error {} ",e.getMessage());
			map.put("500","Service error");
			return map;
		}finally {
			bladeRedis.del(requestId);
		}
		return map;
	}


	/**
	 * 获取设备时间
	 * */
	@PostMapping(time)
	@Override
	public Map<String, String> getDeviceTime(JSONObject jsonObject) {
		Map<String,String> map=new HashMap<>();
		String requestId=jsonObject.getString("requestId");
		try {
			commonInstruct(jsonObject);
			int num=80;
			int i=1;
			boolean flag=false;
			while (i<=num){
				String timeStamp=bladeRedis.get(requestId);
				if(StringUtils.isEmpty(timeStamp)){
					try {
						Thread.sleep(200
						);
					} catch (InterruptedException e) {
						throw new RuntimeException(e);
					}
					++i;
				}else {
					flag=true;
					map.put("200",timeStamp);
					break;
				}
			}
			if(!flag){
				map.put("400","request timeout");
			}
		} catch (Exception e) {
			log.error("DeviceIssue error {} ",e.getMessage());
			map.put("500","Service error");
			return map;
		}finally {
			bladeRedis.del(requestId);
		}
		return map;
	}

	@Override
	@PostMapping(setDeviceTime)
	public R setDeviceTime(List<JSONObject> jsonObject) {
		deviceLog22Handler.checkEquipmentTime(jsonObject);
		return R.status(true);
	}
	@Override
	@PostMapping(errorInfo)
	public R erororInfo(List<JSONObject> jsonObject){
		kafkaDataHandler.handlerData(jsonObject);
		return R.status(true);
	}
}
