package org.springblade.resource.rate;


import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class RateLimitAndBlacklistFilter implements Filter {
	private final RateLimiterService rateLimiterService;
	private final IPBlacklist ipBlacklist;

	public RateLimitAndBlacklistFilter(RateLimiterService rateLimiterService, IPBlacklist ipBlacklist) {
		this.rateLimiterService = rateLimiterService;
		this.ipBlacklist = ipBlacklist;
	}

//	@Override
//	public void init(FilterConfig filterConfig) throws ServletException {
//		// 初始化操作
//	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
		throws IOException, ServletException {
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;

		String clientIP = getClientIP(httpRequest);

		if (ipBlacklist.isBlacklisted(clientIP)) {
			httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
			httpResponse.getWriter().write("temporarily out of service!");
			return;
		}

		if (!rateLimiterService.tryAcquire(clientIP)) {
			httpResponse.setStatus(HttpServletResponse.SC_EXPECTATION_FAILED);
			httpResponse.getWriter().write("Too many requests!");
			return;
		}


		chain.doFilter(request, response);
	}

	@Override
	public void destroy() {
		// 销毁操作
	}

	private String getClientIP(HttpServletRequest request) {
		String ip = request.getHeader("X-Forwarded-For");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}
}
