/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.impl.AppServiceImpl;
import org.skyworth.ess.constant.ClientConstant;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity;
import org.skyworth.ess.ota.excel.DeviceSoftwareVersionInfoExcel;
import org.skyworth.ess.ota.feign.IDistributeUpgradeClient;
import org.skyworth.ess.ota.mapper.DeviceSoftwareVersionInfoMapper;
import org.skyworth.ess.ota.service.IDeviceSoftwareVersionInfoService;
import org.skyworth.ess.ota.service.ISoftwareUpgradeRecordService;
import org.skyworth.ess.ota.vo.DeviceSoftwareVersionInfoVO;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.skyworth.ess.ota.wrapper.DeviceSoftwareVersionInfoWrapper;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 设备软件版本信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeviceSoftwareVersionInfoServiceImpl extends BaseServiceImpl<DeviceSoftwareVersionInfoMapper, DeviceSoftwareVersionInfoEntity> implements IDeviceSoftwareVersionInfoService {
	private final ConcurrentMap<Long, AttachmentInfoEntity> attachmentInfoEntityConcurrentHashMap = new ConcurrentHashMap<>();
	private final ISoftwareUpgradeRecordService softwareUpgradeRecordService;
	@Qualifier("org.skyworth.ess.ota.feign.IDistributeUpgradeClient")
	private final IDistributeUpgradeClient distributeUpgradeClient;
	private final IAttachmentInfoClient attachmentInfoService;
	private final IDevice21Service device21Service;
	private final IDeviceIssueBiz deviceIssue;
	private final BladeRedis bladeRedis;
	@Value("${oss.domestic:false}")
	private boolean ossDomestic;
	@Override
	public IPage<DeviceSoftwareVersionInfoVO> selectDeviceSoftwareVersionInfoPage(IPage<DeviceSoftwareVersionInfoVO> page, DeviceSoftwareVersionInfoVO deviceSoftwareVersionInfo) {
		return page.setRecords(baseMapper.selectDeviceSoftwareVersionInfoPage(page, deviceSoftwareVersionInfo));
	}


	@Override
	public List<DeviceSoftwareVersionInfoExcel> exportDeviceSoftwareVersionInfo(Wrapper<DeviceSoftwareVersionInfoEntity> queryWrapper) {
		return baseMapper.exportDeviceSoftwareVersionInfo(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void circularProcessingUpgradeRecords() {
		log.info("circularProcessingUpgradeRecords-->OTA upgrade begins.");
		clearMap();
		// 内存存储ota数据，从触发任务开始后2h失效
		IPage<DeviceSoftwareVersionInfoEntity> page = new Page<>();
		int currentPage = 0;
		int pageSize = 100;
		page.setCurrent(currentPage);
		page.setSize(pageSize);
		boolean flag = true;
		while (flag) {
			List<String> serialNumberList = null;
			try {
				// 分页获取需要升级的设备SN号
				serialNumberList = baseMapper.findSerialNumberList(page);
				if (CollectionUtils.isNullOrEmpty(serialNumberList)) {
					return;
				}
				log.info("circularProcessingUpgradeRecords-->The number of SN upgrades this time,{}", serialNumberList.size());
				List<OtaUpdatePackVO> otaUpdatePackVOList = baseMapper.findDeviceUpgradeListBySn(serialNumberList);
				extracted(otaUpdatePackVOList);
				log.info("circularProcessingUpgradeRecords-->This round of data upgrade is completed.");
				int recordSize = serialNumberList.size();
				if (recordSize == 0) {
					break;
				}
				if (recordSize < pageSize) {
					flag = false;
				} else {
					currentPage++;
					page.setCurrent(currentPage);
				}
			} catch (Exception e) {
				String serialNumberString = CollectionUtils.isNullOrEmpty(serialNumberList) ? "" : String.join(",", serialNumberList);
				log.error("circularProcessingUpgradeRecords-->error:The SN for this upgrade failure is as follows:{};The abnormal information is as follows:{}", serialNumberString, e.getMessage());
			}
		}
		clearMap();
		log.info("circularProcessingUpgradeRecords-->OTA upgrade completed.");
	}


	public void extracted(List<OtaUpdatePackVO> otaUpdatePackVOList) {
		// 给对象设置sasToken参数
		log.info("extracted---->Getting file sasToken started.");
		getSasTokenAndSetUrl(otaUpdatePackVOList);
		List<JSONObject> distributeUpgradeVOList = new ArrayList<>();
		// 插入升级记录表,生成recordId
		log.info("extracted---->Add upgrade record.");
		softwareUpgradeRecordService.batchInsert(otaUpdatePackVOList);
		// 修改升级版本+状态
		log.info("extracted---->Modify upgrade version and status.");
		this.updateStatusAndLatestReleasedVersion(otaUpdatePackVOList);
		// 根据获取的升级id插入升级记录表
		Map<String, List<OtaUpdatePackVO>> map = otaUpdatePackVOList.stream().collect(Collectors.groupingBy(OtaUpdatePackVO::getSerialNumber));
		// 封装升级软件包报文信息
		map.forEach((key, value) -> {
			distributeUpgradeVOList.add(toJsonValue(key, value));
		});
		// 数据推送mqtt
		log.info("extracted---->Distribute upgrade package.");
		distributeUpgradeClient.deviceUpgrade(distributeUpgradeVOList);
	}

	private JSONObject toJsonValue(String key, List<OtaUpdatePackVO> otaUpdatePackVOList) {
		JSONObject distributeUpgradeVO = new JSONObject();
		distributeUpgradeVO.put("requestId", TimeUtils.generateRequestId());
		distributeUpgradeVO.put("timestamp", new Date());
		distributeUpgradeVO.put("deviceSn", key);
		List<JSONObject> jsonObjectList = new ArrayList<>(otaUpdatePackVOList.size());
		otaUpdatePackVOList.forEach(a -> {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("id", a.getId().toString());
			jsonObject.put("currentVersionNumber", a.getCurrentVersionNumber());
			jsonObject.put("bigType", a.getBigType());
			jsonObject.put("smallType", a.getSmallType());
			jsonObject.put("versionNumber", a.getVersionNumber());
			jsonObject.put("company", a.getCompany());
			jsonObject.put("packCdnUrl", a.getPackCdnUrl());
			jsonObject.put("isNewVersion", a.getIsNewVersion());
			jsonObject.put("checkSum", a.getCheckSum());
			jsonObjectList.add(jsonObject);
		});
		distributeUpgradeVO.put("otaUpdatePackVOList", jsonObjectList);
		return distributeUpgradeVO;
	}

	/**
	 * 设置sasToken参数container和azureKey
	 *
	 * @param otaUpdatePackVOList 入参
	 * <AUTHOR>
	 * @since 2023/10/10 10:02
	 **/
	private void getSasTokenAndSetUrl(List<OtaUpdatePackVO> otaUpdatePackVOList) {
		List<Long> businessIds = new ArrayList<>();
		// 获取不在内存存储对象的业务id集合
		otaUpdatePackVOList.forEach(a -> {
			Long businessId = a.getBusinessId();
			if (!attachmentInfoEntityConcurrentHashMap.containsKey(businessId)) {
				businessIds.add(businessId);
			} else {
				// 确认时间是否到期,到期需要重新获取sasToken
				AttachmentInfoEntity attachmentInfoEntity = attachmentInfoEntityConcurrentHashMap.get(businessId);
				String redisKey = attachmentInfoEntity.getContainer() + attachmentInfoEntity.getAzureKey();
				String sasToken = bladeRedis.get(redisKey);
				if (StringUtils.isBlank(sasToken)) {
					businessIds.add(businessId);
					attachmentInfoEntityConcurrentHashMap.remove(businessId);
				}
			}
		});
		// 根据业务id查询附件明细
		if (!CollectionUtils.isNullOrEmpty(businessIds)) {
			Map<Long, List<AttachmentInfoEntity>> attachmentInfoEntityMap = attachmentInfoService.findByBusinessIds(businessIds).getData();
			Optional.ofNullable(attachmentInfoEntityMap).orElse(new HashMap<>(0)).forEach((key, value) -> {
				attachmentInfoEntityConcurrentHashMap.put(key, value.get(0));
			});
		}
		otaUpdatePackVOList.forEach(a -> {
			Long businessId = a.getBusinessId();
			if (attachmentInfoEntityConcurrentHashMap.containsKey(businessId)) {
				AttachmentInfoEntity attachmentInfoEntity = attachmentInfoEntityConcurrentHashMap.get(businessId);
				// 华为云CDN地址不需要签名token，微软云CDN地址需要在链接后面追加签名
				if (!ossDomestic) {
					a.setPackCdnUrl(a.getPackCdnUrl() + CommonConstant.SYMBOL_QUESTION_MARK + attachmentInfoEntity.getSasToken());
				}
			}
		});
	}

	@Override
	public void obtainUpgradeResults(Map<String, String> map) {
		baseMapper.obtainUpgradeResults(map);
	}

	@Override
	public void obtainUpgradePushResults(List<String> deviceSns) {
		baseMapper.obtainUpgradePushResults(deviceSns);
	}


	@Override
	public void processingTimeoutStatus() {
		baseMapper.processingTimeoutStatus();
	}

	@Override
	public void updateStatusAndLatestReleasedVersion(List<OtaUpdatePackVO> otaUpdatePackVOList) {
		baseMapper.updateStatusAndLatestReleasedVersion(otaUpdatePackVOList);
	}

	/**
	 * 手动重试
	 *
	 * @param deviceSoftwareVersionIds 入参
	 * <AUTHOR>
	 * @since 2023/9/25 15:06
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void retry(List<Long> deviceSoftwareVersionIds) {
		// 查询当前设备升级信息
		List<OtaUpdatePackVO> otaUpdatePackVOList = baseMapper.findDeviceUpgradeListByVersionIds(deviceSoftwareVersionIds);
		if (otaUpdatePackVOList.size() != deviceSoftwareVersionIds.size()) {
			throw new BusinessException("client.ota.record.count.error");
		}
		// 限制不能勾选同样sn的数据
		List<String> repeatSerialNumberList = otaUpdatePackVOList.stream().collect(Collectors.toMap(OtaUpdatePackVO::getSerialNumber, e1 -> 1, Integer::sum)).entrySet().stream().filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
		if (!CollectionUtils.isNullOrEmpty(repeatSerialNumberList)) {
			throw new BusinessException("client.ota.record.upgrade.one", String.join(",", repeatSerialNumberList));
		}
		// 判断是否有升级中的，有升级中的SN，则SN的单都不能选中，解决用户开多个页面升级同一个设备问题
		List<String> serialNumberList =
			otaUpdatePackVOList.stream().map(OtaUpdatePackVO::getSerialNumber).distinct().collect(Collectors.toList());
		List<String> canNotUpgradedSn = baseMapper.queryCanNotUpgradedSN(serialNumberList);
		if (!CollectionUtils.isNullOrEmpty(canNotUpgradedSn)) {
			throw new BusinessException("client.ota.record.upgrading", String.join(",", canNotUpgradedSn));
		}
		// 判断当前版本和最新版本是否一致，一致则不允许升级
		String result = otaUpdatePackVOList.stream()
			.filter(item -> item.getCurrentVersionNumber().equals(item.getVersionNumber()))
			.map(OtaUpdatePackVO::getSerialNumber)
			.collect(Collectors.joining(","));
		if(StringUtils.isNotEmpty(result)){
			throw new BusinessException("client.ota.record.already.latest", result);
		}
		// 验证所选设备是否在线
		clearMap();
		// 软件升级
		extracted(otaUpdatePackVOList);
		// 清理掉内存存储的升级信息
		clearMap();
	}

	@Override
	public Set<Long> validDeviceIsLatestVersion(List<Long> ids) {
		return baseMapper.validDeviceIsLatestVersion(ids);
	}

	@Override
	public R<IPage<DeviceSoftwareVersionInfoVO>> queryList(Map<String, Object> deviceSoftwareVersionInfo, Query query) {
		IPage<DeviceSoftwareVersionInfoEntity> pages = Condition.getPage(query);
		BladeUser userAuth = AuthUtil.getUser();
		String deptId = AppServiceImpl.inspectInnerRole(userAuth);
		deviceSoftwareVersionInfo.put("deptId", deptId);
		List<DeviceSoftwareVersionInfoEntity> list = baseMapper.queryPage(deviceSoftwareVersionInfo, pages, userAuth.getUserId());
		pages.setRecords(list);
		List<DeviceSoftwareVersionInfoEntity> resultRecords = pages.getRecords();
		if (!CollectionUtils.isNullOrEmpty(resultRecords)) {
			// 批量获取设备SN
			List<String> serialNumbers = resultRecords.stream().map(DeviceSoftwareVersionInfoEntity::getSerialNumber).collect(Collectors.toList());
			// 查询逆变器+wifi棒+bms设备状态
			List<Device21Entity> device21EntityList = device21Service.queryDevice21InfoBySerialNumber(serialNumbers);
			// 最新版本无需升级的+ota处无最新版本包无需升级的所有id
			List<Long> ids = resultRecords.stream().map(DeviceSoftwareVersionInfoEntity::getId).collect(Collectors.toList());
			Set<Long> set = this.validDeviceIsLatestVersion(ids);
			// 设置设备状态
			if (!CollectionUtils.isNullOrEmpty(device21EntityList)) {
				// 获取wifi棒状态
				Map<String, String> wifiBomStatusMap = getDeviceStatusMap(device21EntityList, ClientConstant.BIG_TYPE_WIFIBOM);
				// 获取逆变器状态
				Map<String, String> inverterStatusMap = getDeviceStatusMap(device21EntityList, ClientConstant.BIG_TYPE_INVERTER);
				// 获取电池状态
				Map<String, String> batteryStatusMap = getDeviceStatusMap(device21EntityList, ClientConstant.BIG_TYPE_BATTERY);
				resultRecords.forEach(a -> {
					String key = a.getSerialNumber();
					String bigType = a.getBigType();
					// 设置设备状态
					if (ClientConstant.BIG_TYPE_WIFIBOM.equals(bigType)) {
						a.setDeviceStatus(wifiBomStatusMap.get(key));
					} else if (ClientConstant.BIG_TYPE_INVERTER.equals(bigType)) {
						a.setDeviceStatus(inverterStatusMap.get(key));
					} else if (ClientConstant.BIG_TYPE_BATTERY.equals(bigType)) {
						a.setDeviceStatus(batteryStatusMap.get(key));
					}
					//  设置是否可勾选
					//  set:存储的是当前包已经是最新版的+ota不存在最新升级包的业务id
					//	1.wifi棒在线
					//	2.如果设备为wifi棒，则设置为勾选状态为：是否最新版+是否存在最新版本升级包+该SN的是否存在固件正在升级中
					//  3.如果设备为逆变器和电池，且状态不为离线，设置勾选状态为：是否最先版+是否存在最新升级包+该SN的是否存在固件正在升级中
					if (BizConstant.CHAR_ONE.equals(wifiBomStatusMap.get(key))) {
						if (ClientConstant.BIG_TYPE_WIFIBOM.equals(bigType)) {
							a.setCanCheck(!set.contains(a.getId()));
						} else if (ClientConstant.BIG_TYPE_INVERTER.equals(bigType)) {
							a.setCanCheck(!BizConstant.CHAR_ZERO.equals(inverterStatusMap.get(key)) && !set.contains(a.getId()));
						} else if (ClientConstant.BIG_TYPE_BATTERY.equals(bigType)) {
							a.setCanCheck(!BizConstant.CHAR_FIVE.equals(batteryStatusMap.get(key)) && !set.contains(a.getId()));
						}
					}
				});
			}
		}
		return R.data(DeviceSoftwareVersionInfoWrapper.build().pageVO(pages));
	}

	private Map<String, String> getDeviceStatusMap(List<Device21Entity> device21EntityList, String deviceType) {
		if (!CollectionUtils.isNullOrEmpty(device21EntityList)) {
			return device21EntityList.stream().
				filter(a -> deviceType.equals(a.getDeviceType())).
				filter(a -> ValidationUtil.isNotEmpty(a.getDeviceStatus())).
				collect(Collectors.toMap(
					Device21Entity::getDeviceSerialNumber
					, Device21Entity::getDeviceStatus, (a, b) -> a));
		} else {
			return new HashMap<>();
		}

	}

	@Override
	public void publishOtaUpgradeResultToApp(Map<String, Object> map) {
		JSONObject appObj = new JSONObject();
		String deviceSerialNumber = "";
		Object message = map.get("message");
		Object code = map.get("code");
		String id = (String) map.get("id");
		//包含checkExpired则为定时任务调用
		if (map.containsKey("checkExpired")) {
			deviceSerialNumber = (String) map.get("deviceSn");
			appObj.put("deviceSn", deviceSerialNumber);
			appObj.put("code", code);
			appObj.put("message", message);
			appObj.put("topic", Constants.PUB_OTA_OBTAINING_RESULTS_APP);
			deviceIssue.dataIssueToDevice(appObj);
		} else {
			SoftwareUpgradeRecordEntity softwareUpgradeRecord = softwareUpgradeRecordService.getById(id);
			deviceSerialNumber = softwareUpgradeRecord.getSerialNumber();
			Boolean exists = bladeRedis.exists("ota:app:issue:upgrading:" + deviceSerialNumber);
			// 目前改为：无论有没有key，只要收到硬件升级结果，都走mqtt告知app升级成功or失败
			appObj.put("deviceSn", deviceSerialNumber);
			appObj.put("code", code);
			appObj.put("message", message);
			appObj.put("topic", Constants.PUB_OTA_OBTAINING_RESULTS_APP);
			deviceIssue.dataIssueToDevice(appObj);
			if (Boolean.TRUE.equals(exists)) {
				// 升级成功or失败后则删除这个key
				bladeRedis.del("ota:app:issue:upgrading:" + deviceSerialNumber);
			}
		}
	}

	@Override
	public void otaOfflineUpgradeException(JSONObject jsonObject) {
		baseMapper.otaOfflineUpgradeException(jsonObject);
	}

	/**
	 * ota升级包放入内存，避免重复请求数据库，刚进入方法或者方法执行完后，需要清理，避免无法获取到最新升级包
	 *
	 * <AUTHOR>
	 * @since 2023/12/14 10:20
	 **/
	private void clearMap() {
		attachmentInfoEntityConcurrentHashMap.clear();
	}

	@Override
	public void batchDeleteByDeviceSn(List<String> listDeviceSn) {
		baseMapper.batchDeleteByDeviceSn(listDeviceSn);
	}
}
