//package org.skyworth.ess.cache;
//
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//import redis.clients.jedis.JedisPool;
//import redis.clients.jedis.JedisPoolConfig;
//
//import javax.annotation.PostConstruct;
//import java.time.Duration;
///** 单例模式优化Jedis连接池 */
//
//@Component
//public class JedisPoolUtil {
//	// Redis服务器地址(域名或IP)
//	private static String host;
//	// Redis服务器连接端⼝(Redis默认端⼝号是6379)
//	private static String port;
//	// Redis服务器连接密码(默认为空)
//	private static String password;
//	// 最⼤连接数
//	private static Integer maxTotal;
//	// 最⼤空闲连接数
//	private static Integer maxIdle;
//	// 最⼤的阻塞时⻓
//	private static Integer maxWait;
//	// 向资源池借⽤连接时是否做连接有效性检测（ping）。检测到的⽆效连接将会被移除
//	private static Boolean testOnBorrow;
//
//	// Redis服务器地址(域名或IP)
//	@Value("${jedis.host}")
//	private String hostValue;
//	// Redis服务器连接端⼝(Redis默认端⼝号是6379)
//	@Value("${jedis.port}")
//	private String portValue;
//	// Redis服务器连接密码(默认为空)
//	@Value("${jedis.password}")
//	private String passwordValue;
//	// 最⼤连接数
//	@Value("${jedis.maxTotal}")
//	private Integer maxTotalValue;
//	// 最⼤空闲连接数
//	@Value("${jedis.maxIdle}")
//	private Integer maxIdleValue;
//	// 最⼤的阻塞时⻓
//	@Value("${jedis.maxWait}")
//	private Integer maxWaitValue;
//	// 向资源池借⽤连接时是否做连接有效性检测（ping）。检测到的⽆效连接将会被移除
//	@Value("${jedis.testOnBorrow}")
//	private Boolean testOnBorrowValue;
//
//	@PostConstruct
//	public void initStatic(){
//		host = this.hostValue;
//		port = this.portValue;
//		password = this.passwordValue;
//		maxTotal = this.maxTotalValue;
//		maxIdle = this.maxIdleValue;
//		maxWait = this.maxWaitValue;
//		testOnBorrow = this.testOnBorrowValue;
//	}
//
//
//	private volatile static JedisPool jedisPool = null;
//	private volatile static Jedis jedis = null;
//
//
//	private JedisPoolUtil() {
//	}
//
//	private static JedisPool getInstance() {
//		// 单例模式实现：双检锁/双重校验锁。这种⽅式采⽤双锁机制，安全且在多线程情况下能保持⾼性能
//		if(jedisPool == null) {
//			synchronized (JedisPoolUtil.class) {
//				if(jedisPool == null) {
//					// 创建⼀个配置对象
//					JedisPoolConfig config = new JedisPoolConfig();
//					config.setMaxTotal(maxTotal); // 资源池中的最⼤连接数
//					config.setMaxIdle(maxIdle); // 资源池允许的最⼤空闲连接数
//					// 当资源池连接⽤尽后，调⽤者的最⼤等待时间(单位为毫秒)
//					config.setMaxWait(Duration.ofMillis(maxWait));
//					// 向资源池借⽤连接时是否做连接有效性检测(业务量很⼤时候建议设置为false，减少⼀次ping的开销)
//					config.setTestOnBorrow(testOnBorrow);
//					// 初始化JedisPool
//					jedisPool = new JedisPool(config, host, Integer.parseInt(port),1000,password);
//				}
//			}
//		}
//		return jedisPool;
//	}
//	/** 获取连接⽅法 */
//	public static Jedis getJedis() {
//		if (jedis == null) {
//			// 获取连接
//			jedis = getInstance().getResource();
//		}
//		return jedis;
//	}
//
//	public static void release(Jedis jd) {
//		if (jd != null) {
//			jd.close();
//		}
//	}
//}
