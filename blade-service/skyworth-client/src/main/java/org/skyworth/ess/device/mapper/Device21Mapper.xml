<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.Device21Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="Device21ResultMap" type="org.skyworth.ess.device.entity.Device21Entity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="modbus_protocol_version" property="modbusProtocolVersion"/>
        <result column="device_model" property="deviceModel"/>
        <result column="master_software_version" property="masterSoftwareVersion"/>
        <result column="master_software_build_date" property="masterSoftwareBuildDate"/>
        <result column="slave_firmware_version" property="slaveFirmwareVersion"/>
        <result column="slave_firmware_build_date" property="slaveFirmwareBuildDate"/>
        <result column="mppt_number" property="mpptNumber"/>
        <result column="rated_voltage" property="ratedVoltage"/>
        <result column="rated_frequency" property="ratedFrequency"/>
        <result column="rated_power" property="ratedPower"/>
        <result column="grid_phase_number" property="gridPhaseNumber"/>
        <result column="ems_firmware_version" property="emsFirmwareVersion"/>
        <result column="ems_firmware_build_date" property="emsFirmwareBuildDate"/>
        <result column="dcdc_firmware_version" property="dcdcFirmwareVersion"/>
        <result column="dcdc_firmware_build_date" property="dcdcFirmwareBuildDate"/>
        <result column="wifi_stick_serial_number" property="wifiStickSerialNumber"/>
        <result column="device_status" property="deviceStatus"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="deviceType" property="deviceType"/>
    </resultMap>

    <update id="updateByDeviceId">
        update device_21 set device_status=#{status} where device_serial_number=#{deviceSn} and ( device_status!='2' or
        device_status is null )
    </update>


    <select id="selectDevice21Page" resultMap="Device21ResultMap">
        select * from device_21 where is_deleted = 0
    </select>


    <select id="exportDevice21" resultType="org.skyworth.ess.device.excel.Device21Excel">
        SELECT * FROM device_21 ${ew.customSqlSegment}
    </select>

    <select id="getEntity" resultMap="Device21ResultMap">
        SELECT * FROM device_21 ${ew.customSqlSegment}
    </select>

    <select id="getEntityIsDelete" resultMap="Device21ResultMap">
        SELECT * FROM device_21
        WHERE plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber}
    </select>

    <select id="queryAppInverterInfo" resultType="org.skyworth.ess.app.vo.AppDeviceInfo">
        select p.plant_name as plantName,s.device_serial_number as deviceSerialNumber ,
        f.device_type as deviceModel ,d.rated_voltage as ratedVoltage ,d.rated_power as ratedPower ,
        ifnull(f.new_quality_guarantee_year,f.quality_guarantee_year) as newQualityQuaranteeYear
        ,d.device_status as onLineStatus,p.install_date as installDate,
        d24.inverter_control as inverterControl,
        f.warranty_deadline as warrantyDeadline
        from wifi_stick_plant s inner join plant p on s.plant_id =p.id and p.is_deleted =0
        left join device_exit_factory_info f on f.device_serial_number = s.device_serial_number and p.is_deleted = 0
        left join device_21 d on d.device_serial_number = f.device_serial_number and d.plant_id =s.plant_id
        left join device_24 d24 on d24.plant_id = s.plant_id and d24.device_serial_number = s.device_serial_number
        and f.is_deleted =0
        where s.is_deleted =0
        and s.plant_id = #{plantId} and s.device_serial_number = #{deviceSerialNumber}
        and d.is_deleted = 0
        and d24.is_deleted = 0
    </select>

    <select id="queryAppInverterInfoV2" resultType="org.skyworth.ess.app.vo.AppDeviceInfo">
        select
            p.plant_name as plantName,
            s.device_serial_number as deviceSerialNumber ,
            f.device_type as deviceModel ,
            ifnull(f.new_quality_guarantee_year, f.quality_guarantee_year) as newQualityGuaranteeYear,
            p.install_date as installDate,
            f.quality_guarantee_year as qualityGuaranteeYear,
            f.warranty_start_date as warrantyStartDate,
            f.warranty_deadline as warrantyDeadline,
            d.device_status as onLineStatus,
            d.exist_user_type_alarm  as existUserTypeAlarm,
            d.exist_agent_type_alarm as existAgentTypeAlarm
        from wifi_stick_plant s
                 inner join plant p on s.plant_id = p.id and p.is_deleted = 0
                 left join device_exit_factory_info f on f.device_serial_number = s.device_serial_number and f.is_deleted = 0
                 left join device_21 d on d.device_serial_number = f.device_serial_number and d.plant_id = s.plant_id and d.is_deleted = 0
        where s.is_deleted = 0
          and s.plant_id = #{plantId} and s.device_serial_number = #{deviceSerialNumber}
    </select>

    <select id="getCreateTime" resultMap="Device21ResultMap">
        select create_time from device_21 where device_serial_number = #{de.deviceSerialNumber}
        and plant_id = #{de.plantId} and is_deleted = 0
    </select>

    <update id="updateSetup">
        UPDATE device_21 SET
        update_time = now(),
        <foreach collection="device21Map" item="val" index="key" separator=",">
            ${key} = #{val}
        </foreach>
        WHERE plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber}
    </update>

    <select id="queryDevice21InfoBySerialNumber" resultMap="Device21ResultMap">
        select
        d.device_status,
        d.device_serial_number,
        'inverter' as deviceType
        from
        device_21 d
        left join plant p on
        d.plant_id = p.id
        where
        d.is_deleted = 0
        and p.is_deleted = 0
        and d.device_serial_number in
        <foreach collection="serialNumbers" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        union all
        select
        wsp.wifi_stick_status AS device_status,
        wsp.device_serial_number ,
        'wifibom' as deviceType
        from wifi_stick_plant wsp
        where
        wsp.is_deleted = 0
        and wsp.wifi_stick_status is not null
        and wsp.device_serial_number in
        <foreach collection="serialNumbers" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        union all
        select
        bcs.battery_status as device_status,
        wsp2.device_serial_number,
        'battery' as deviceType
        from
        plant bmd
        left join wifi_stick_plant wsp2 on
        wsp2.plant_id = bmd.id
        left join battery_current_status bcs on
        wsp2.plant_id = bcs.plant_id
        and wsp2.device_serial_number = bcs.device_serial_number
        where
        bmd.is_deleted = 0
        and
        wsp2.is_deleted = 0
        and
        bcs.is_deleted = 0
        and
        bcs.battery_status is not null
        and wsp2.device_serial_number in
        <foreach collection="serialNumbers" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getListByDeviceSerialNumberCollect" resultMap="Device21ResultMap">
        SELECT id, device_serial_number, grid_phase_number
        FROM device_21
        WHERE is_deleted = 0
        AND device_serial_number IN
        <if test="list != null and list.size() > 0">
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size() == 0">
            ('1')
        </if>
    </select>
</mapper>
