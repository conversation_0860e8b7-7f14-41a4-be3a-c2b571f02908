/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.fegin;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.device.client.IDeviceLog21Client;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.entity.DeviceLog21;
import org.skyworth.ess.device.mapper.DeviceLog21Mapper;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.LockType;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 字典服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@Slf4j
public class DeviceLog21BizClient extends BaseServiceImpl<DeviceLog21Mapper, DeviceLog21> implements IDeviceLog21Client {

	private BladeRedis redis;

	private IDevice21Service device21Service;

	private RedisLockClient client;
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final String DEVICE21_REDIS_KEY = "device21RedisKey:";
	@PostMapping(device21)
	@Override
	public void insertBatchDeviceLog21Column(@RequestBody List<DeviceLog21> deviceLog21List) {
		baseMapper.insertBatchSomeColumn(deviceLog21List);
		List<String> snList = deviceLog21List.stream()
			.map(DeviceLog21::getDeviceSerialNumber)
			.collect(Collectors.toList());
		deviceLog21List.forEach(p -> {
			judgeDataAtTheSameTime(p.getDeviceSerialNumber(), p.getPlantId(),"server_iot_realTime_data");
		});

		//查出所有sn的信息
		Map<String, Device21Entity> finalDeviceMap = getEntityMap(snList);
		ThreadPoolCustom.getCustomThreadPool().submit(() -> deviceLog21List.stream().parallel().forEach(v->{
            String deviceSn=v.getDeviceSerialNumber();
            long plantId=v.getPlantId();
            String key= CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSn+"_1";
			try {
				boolean redisLock=client.tryLock(key+"_Lock", LockType.REENTRANT,0,0, TimeUnit.SECONDS);
				if(redisLock){
					redis.setEx(key,"1", Duration.ofMinutes(5));
					common(v, deviceSn, finalDeviceMap);
					client.unLock(key+"_Lock", LockType.REENTRANT);
					redis.del( DEVICE21_REDIS_KEY + deviceSn + ":" + plantId);
				}
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
        }));
	}

	private long judgeDataAtTheSameTime(String deviceSn, long plantId, String msg) {
		// 判断配网成功，设备发的 topic ： server_distribution_network 中的 content1 和 topic ： server_iot_realTime_data type为1时处于并发状态，
		// 这2个topic同时推送过来，导致 device21 表中重复写入
		if("server_iot_realTime_data".equals(msg)) {
			// 防止 server_iot_realTime_data 先推送过来，导致 server_distribution_network 后面得不到锁，否则不会下发配网成功的 topic
			try {
					log.info("insertBatchDeviceLog21ForNetWork server_iot_realTime_data sleep : {}, {}, {}  " ,plantId,deviceSn,msg);
					Thread.sleep(5000);
			}catch (InterruptedException e) {
				log.error("insertBatchDeviceLog21ForNetWork error! -> "+ msg + "----", e);
			}
		}
		long systemTime = System.currentTimeMillis();
		String key = DEVICE21_REDIS_KEY + deviceSn + ":" + plantId;
		Object exists = redis.getSet(key,systemTime);
		try {
			log.info("insertBatchDeviceLog21ForNetWork before get redis key topic : {}, {}, {} , redis value : {} , current date : {} " ,plantId,deviceSn,msg, exists,systemTime);
			// 如果配网的先拿到锁，则 解析实时数据的先等待，等配网写入 device21表后 在解析实时的
			if (exists != null && "server_iot_realTime_data".equals(msg)) {
				log.info("insertBatchDeviceLog21ForNetWork get redis key : {} , {}  " ,msg, exists);
				Thread.sleep(30000);
			}
		}catch (InterruptedException e) {
			log.error("insertBatchDeviceLog21ForNetWork error! -> "+ msg + "----", e);
		}
		return systemTime;
	}

	private void common(DeviceLog21 v, String deviceSn,Map<String, Device21Entity> deviceMap) {
		LambdaQueryWrapper<DeviceExitFactoryInfoEntity> factoryQueryWrapper = new LambdaQueryWrapper<>();
		factoryQueryWrapper.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, deviceSn);
		DeviceExitFactoryInfoEntity exitFactory = deviceExitFactoryInfoService.getOne(factoryQueryWrapper);
		if(deviceMap.isEmpty()){
			Device21Entity device21Entity=new Device21Entity();
			org.springframework.beans.BeanUtils.copyProperties(v,device21Entity);
			device21Entity.setId(null);
			device21Entity.setDeviceModel(exitFactory.getDeviceType());
			device21Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			device21Entity.setCreateTime(new Date());
			device21Service.save(device21Entity);
		}else {
			Device21Entity device21Entity=deviceMap.get(deviceSn);

			if(ValidationUtil.isNotEmpty(device21Entity)){
				long id=device21Entity.getId();
				Date createTime = device21Entity.getCreateTime();
				String status=device21Entity.getDeviceStatus();
				org.springframework.beans.BeanUtils.copyProperties(v,device21Entity);
				device21Entity.setId(id);
				device21Entity.setDeviceStatus(status);
				device21Entity.setDeviceModel(exitFactory.getDeviceType());
				device21Entity.setCreateTime(createTime);
				device21Service.updateById(device21Entity);
			}else {
				device21Entity=new Device21Entity();
				org.springframework.beans.BeanUtils.copyProperties(v,device21Entity);
				device21Entity.setId(null);
				device21Entity.setDeviceModel(exitFactory.getDeviceType());
				device21Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				device21Service.save(device21Entity);
			}

		}
	}
	/**
	 * 新版本配网逻辑
	 * */
	@PostMapping(NETWORK)
	@Override
	public void insertBatchDeviceLog21ForNetWork(@RequestBody List<DeviceLog21> deviceLog21List) {
		baseMapper.insertBatchSomeColumn(deviceLog21List);
		List<String> snList = deviceLog21List.stream()
			.map(DeviceLog21::getDeviceSerialNumber)
			.collect(Collectors.toList());
		// 判断配网成功，设备发的 topic ： server_distribution_network 中的 content1 和 topic ： server_iot_realTime_data type为1时处于并发状态，
		// 这2个topic同时推送过来，导致 device21 表中重复写入
		deviceLog21List.forEach(p -> {
			judgeDataAtTheSameTime(p.getDeviceSerialNumber(), p.getPlantId(),"server_distribution_network");
		});
		Map<String, Device21Entity> finalDeviceMap = getEntityMap(snList);
		deviceLog21List.stream().parallel().forEach(v->{
			String deviceSn=v.getDeviceSerialNumber();
			long plantId=v.getPlantId();
			common(v, deviceSn, finalDeviceMap);
			String key= CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSn+"_v2_1";
			redis.setEx(key,"1",Duration.ofSeconds(60));

			log.info("insertBatchDeviceLog21ForNetWork del redis  result : {} , {}",deviceSn,plantId);
		});
	}

	@NotNull
	private Map<String, Device21Entity> getEntityMap(List<String> snList) {
		//查出所有sn的信息
		LambdaQueryWrapper<Device21Entity> eq = Wrappers.<Device21Entity>query().lambda()
			.in(Device21Entity::getDeviceSerialNumber, snList).eq(Device21Entity::getIsDeleted,0) ;
		List<Device21Entity> device21EntityList=device21Service.list(eq);
		Map<String, Device21Entity> deviceMap=new HashMap<>();
		if(ValidationUtil.isNotEmpty(device21EntityList)&&!device21EntityList.isEmpty()){
			deviceMap = device21EntityList.stream()
				.collect(Collectors.toMap(Device21Entity::getDeviceSerialNumber, Function.identity()));
		}

        return deviceMap;
	}

	@PostMapping(GETDEVICE21_BY_PLANT_ID)
	@Override
	public Device21Entity getDevice21ByPlantId(@RequestParam("plantId") long plantId,@RequestParam("deviceSerialNumber") String deviceSerialNumber) {
		Device21Entity device21Entity = new Device21Entity();
		device21Entity.setPlantId(plantId);
		device21Entity.setDeviceSerialNumber(deviceSerialNumber);
		device21Entity.setIsDeleted(0);
		return device21Service.getOne(Condition.getQueryWrapper(device21Entity));
	}

}
