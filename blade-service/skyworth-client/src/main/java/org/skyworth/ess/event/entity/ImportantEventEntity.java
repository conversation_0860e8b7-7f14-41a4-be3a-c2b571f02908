/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.event.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 重要事件表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@TableName("important_event")
@ApiModel(value = "ImportantEvent对象", description = "重要事件表")
@EqualsAndHashCode(callSuper = true)
public class ImportantEventEntity extends TenantEntity {

	/**
	 * 事件类型（device逆变器；battery电池；plant站点）
	 */
	@ApiModelProperty(value = "事件类型（device逆变器；battery电池；plant站点）")
	private String eventType;
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * SN号
	 */
	@ApiModelProperty(value = "SN号")
	private String serialNumber;
	/**
	 * 日期
	 */
	@ApiModelProperty(value = "日期")
	private Date eventDate;
	/**
	 * 事件内容
	 */
	@ApiModelProperty(value = "事件内容")
	private String eventContent;
	/**
	 * 事件备注
	 */
	@ApiModelProperty(value = "事件备注")
	private String eventRemark;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@TableField(exist = false)
	private String timeZone;

}
