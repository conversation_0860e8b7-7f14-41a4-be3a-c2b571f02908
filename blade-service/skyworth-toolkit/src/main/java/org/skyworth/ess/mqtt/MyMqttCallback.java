package org.skyworth.ess.mqtt;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.skyworth.ess.mqtt.dataProcess.MqttCallBackProcess;
import org.skyworth.ess.device.entity.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * mqtt回调类
 * <AUTHOR>
 */
public class MyMqttCallback implements MqttCallbackExtended {

	private final Logger logger = LoggerFactory.getLogger(MyMqttCallback.class);

	private MqttClientDef mqttClient;


	private MqttCallBackProcess mqttCallBackProcess;

	public MyMqttCallback(MqttClientDef mqttClient, MqttCallBackProcess mqttCallBackProcess) {
		this.mqttClient = mqttClient;
		this.mqttCallBackProcess = mqttCallBackProcess;
	}

	/**
	 * 连接异常断开后调用,包括messageArrived和deliveryComplete异常导致的断线
	 */
	@Override
	public void connectionLost(Throwable throwable) {
		logger.error("MQTT connection lost.", throwable);
		//消息异常处理逻辑
	}

	/**
	 * 订阅到消息之后
	 */
	@Override
	public void messageArrived(String topic, MqttMessage message) {

		String result = new String(message.getPayload());
		logger.info("topic : {}, result : {}" ,topic,result);
		//处理数据
		switch (topic) {
			//将实时数据转发至kafka
			case Constants.REAL_TOPIC:
				mqttCallBackProcess.publish(Constants.REAL_TOPIC, result);
				break;
			//心跳数据
			case Constants.HEART_BEAT_TOPIC:
				mqttCallBackProcess.heartBeatWifiToKafka(Constants.HEART_BEAT_TOPIC,result);
				break;
			//订阅下发模式结果返回
			case Constants.SUBSCRIBE_MODE_TOPIC:
				mqttCallBackProcess.subscribeMode(result);
				break;
			//订阅异常消息日志
			case Constants.abnormal_data_server:
				mqttCallBackProcess.errorInfoToKafka(Constants.abnormal_data_server,result);
				break;
			//ota升级结果获取
			case Constants.SUB_OTA_OBTAINING_RESULTS:
				mqttCallBackProcess.obtainUpgradeResults(result);
				break;
			//订阅高级设置下发处理结果
			case Constants.SETTING_RESULTS:
				mqttCallBackProcess.subscribeDeviceIssueRes(result);
				break;
			//订阅获取设备时间结果
			case Constants.DEVICE_GET_TIME_RESULT:
				mqttCallBackProcess.subscribeDeviceTimeRes(result);
				break;
			//订阅WiFi棒发送联网成功
			case Constants.PUBLIC_WIFI_CONNECTED:
				mqttCallBackProcess.subscribeWifiConnectedRes(result);
				break;
			//配网成功至kafka
			case Constants.SERVER_DISTRIBUTION_NETWORK:
				mqttCallBackProcess.distributionNetwork(Constants.SERVER_DISTRIBUTION_NETWORK, result);
				break;
			case Constants.OTA_OFFLINE_UPGRADE_EXCEPTION:
				mqttCallBackProcess.otaOfflineUpgradeException(result);
				break;
			// 订阅逆变器主动获取时间topic
			case Constants.INVERTER_GET_SERVER_TIME:
				mqttCallBackProcess.subscribeInverterGetServerTime(result);
				break;
			default:
				logger.error("未找到topic {}", topic);
				break;
		}
	}


	/**
	 * 消息发布成功，并由EMQX转发至订阅主题中
	 */
	@Override
	public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
		String[] topics = iMqttDeliveryToken.getTopics();
		List<String> deviceSnList = new ArrayList<>();
		if (topics != null) {
			String otaTopicStart = Constants.OTA_DEVICE_UPGRADE.replace("$Sn", "");
			Arrays.stream(topics).forEach(a -> {
				if (a.startsWith(otaTopicStart)) {
					deviceSnList.add(a.substring(otaTopicStart.length()));
				}
			});
		}
		// 在此处处理消息传递完成事件   可以记录日志
		if (iMqttDeliveryToken.isComplete()) {
			// 修改软件升级状态
			if (!CollectionUtils.isNullOrEmpty(deviceSnList)) {
				mqttCallBackProcess.obtainUpgradePushResults(deviceSnList);
			}
		} else {
			logger.info("Message delivery failed");
		}
	}


	/**
	 * 重连成功后调用
	 */
	@Override
	public void connectComplete(boolean b, String s) {
		mqttClient.doSubscribe();
	}
}
