package org.skyworth.ess.mqtt;

import com.alibaba.fastjson.JSONObject;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.pkcs.PKCS8EncryptedPrivateKeyInfo;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.mqtt.dataProcess.MqttCallBackProcess;
import org.skyworth.ess.mqtt.ssl.LinkSSLConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.utils.tool.TimeUtils;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.net.URI;
import java.security.*;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.MessageFormat;

/**
 * <AUTHOR>
 * */
public class MqttClientDef {

	private static final Logger logger = LoggerFactory.getLogger(MqttClientDef.class);

	private MqttAsyncClient client;

	private static final Random R = new SecureRandom();

	private MqttConfig mqttConfig;

	private static final String PRE_REAL="$share/realTime/";

	private static final String PRE_HEART="$share/heart/";

	private static final String PRE_OTA="$share/ota/";

	private static final String PRE_SETTING="$share/setting/";

	private static final String PRE_TIME="$share/time/";

	private static final String PRE_WIFI="$share/wifi/";

	private static final String PRE_NETWORK="$share/network/";

	private static final String PRE_ERROR_INFO="$share/errorInfo/";

	private static final String PRE_OFFLINE_EXCEPTION="$share/offLineException/";

	private static final String PRE_INVERTER="$share/inverter/";
	private MqttCallBackProcess mqttCallBackProcess;


	public MqttClientDef(MqttConfig mqttConfig,MqttCallBackProcess mqttCallBackProcess){
		this.mqttConfig=mqttConfig;
		this.mqttCallBackProcess=mqttCallBackProcess;
	}

	/**
	 * 主题订阅
	 * */
	protected static final List<String> SUB_TOPICS = Arrays.asList(
		PRE_REAL + Constants.REAL_TOPIC,
		PRE_HEART + Constants.HEART_BEAT_TOPIC,
		PRE_OTA + Constants.SUB_OTA_OBTAINING_RESULTS,
		PRE_SETTING + Constants.SETTING_RESULTS,
		PRE_TIME + Constants.DEVICE_GET_TIME_RESULT,
		PRE_WIFI + Constants.PUBLIC_WIFI_CONNECTED,
		PRE_NETWORK + Constants.SERVER_DISTRIBUTION_NETWORK,
		PRE_ERROR_INFO + Constants.abnormal_data_server,
		PRE_OFFLINE_EXCEPTION + Constants.OTA_OFFLINE_UPGRADE_EXCEPTION,
		PRE_INVERTER + Constants.INVERTER_GET_SERVER_TIME
	);



	public void init() {
		try {
			client = new MqttAsyncClient(mqttConfig.getServerURI(), "skyWorth_energy_"+TimeUtils.getRandomNum(10), new MemoryPersistence());
			logger.info("Broker: {}, 连接中...", mqttConfig.getServerURI());
			MqttConnectOptions options = getMqttConnectOptions();
			// 设置回调
			client.setCallback(new MyMqttCallback(this,mqttCallBackProcess));
			client.connect(options);
			logger.info("连接成功.");
		} catch (Exception e) {
			logger.error("MQTT client init error: {}", e.getMessage());
		}
	}

	/**
	 * 构造mqtt参数
	 * */
	@NotNull
	private MqttConnectOptions getMqttConnectOptions() throws Exception {
		MqttConnectOptions options = new MqttConnectOptions();
		// 设置是否清空session,false:服务器会保留客户端的连接会话（需保持clientId不变），true:每次连接到服务器都以新的身份连接
		options.setCleanSession(mqttConfig.isCleanSession());
		// 设置连接的用户名
		options.setUserName(mqttConfig.getUsername());
		options.setMaxInflight(200);
		// 设置连接的密码
		options.setPassword(mqttConfig.getPassword().toCharArray());
		// 设置超时时间 单位为秒,默认30秒
		options.setConnectionTimeout(mqttConfig.getTimeout());
		// 设置会话心跳时间 单位为秒
		options.setKeepAliveInterval(mqttConfig.getKeepAliveInterval());
		// 设置自动重连
		options.setAutomaticReconnect(mqttConfig.isAutoReconnect());
		URI uri = new URI(mqttConfig.getServerURI());
		String scheme = uri.getScheme();
		if("ssl".equals(scheme)) {
			String certPath = mqttConfig.getPath();
			options.setHttpsHostnameVerificationEnabled(true);
			options.setSocketFactory(getSocketFactory(certPath+"cacert.pem",certPath+"client-cert.pem",certPath+"client-key.pem"));
		}
		return options;
	}


	/**
	 * 发布订阅主题
	 * */
	public void doSubscribe() {
		try {
			// 订阅topic
			for (String topic : SUB_TOPICS) {
				client.subscribe(topic, 2);
				logger.info("已订阅Topic: {}", topic);
			}
		} catch (Exception e) {
			logger.error("doSubscribe error: {}", e.getMessage());
		}
	}

	/**
	 * 发布
	 * @param topic
	 * @param message
	 */
	public void publish(String topic, String message,int qos) throws Exception {
		JSONObject msg=JSONObject.parseObject(message);
		String deviceSn=msg.getString("deviceSn");
		topic=topic.replace("$Sn",deviceSn);
		publish(topic, message.getBytes(), qos);
	}

	/**
	 * 发布
	 * @param topic
	 * @param message
	 */
	public void publish(String topic, byte[] message,int qos) throws Exception {
		MqttMessage mqms = new MqttMessage(message);
		try {
			mqms.setQos(qos);
			client.publish(topic, mqms);
		} catch (MqttException e) {
			throw new Exception("Publish message to mqtt exception", e);
		}
	}


	/**
	 * 生成一个指定位数的数字字符串
	 * @param digit
	 * @return
	 */
	public static String randomStr(int digit) {
		StringBuilder enStr = new StringBuilder();
		while(digit > 0) {
			int i = R.nextInt(10);
			enStr.append(i);
			digit--;
		}
		return enStr.toString();
	}

	public static SSLSocketFactory getSocketFactory(String caCertPath) throws Exception {
		CertificateFactory certFactory = CertificateFactory.getInstance("X.509");

		// load CA certificate into keystore to authenticate server
		Certificate caCert = certFactory.generateCertificate(new FileInputStream(caCertPath));
		X509Certificate x509CaCert = (X509Certificate) caCert;

		KeyStore caKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
		caKeyStore.load(null, null);
		caKeyStore.setCertificateEntry("cacert", x509CaCert);

		TrustManagerFactory tmFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
		tmFactory.init(caKeyStore);

		SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
		sslContext.init(null, tmFactory.getTrustManagers(), null);

		return sslContext.getSocketFactory();
	}



	public static SSLSocketFactory getSocketFactory(String caCertPath, String clientCertPath, String clientKeyPath) throws Exception {
		CertificateFactory certFactory = CertificateFactory.getInstance("X.509");

		// load CA certificate into keystore to authenticate server
		Certificate caCert = certFactory.generateCertificate(new FileInputStream(caCertPath));
		X509Certificate x509CaCert = (X509Certificate) caCert;

		KeyStore caKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
		caKeyStore.load(null, null);
		caKeyStore.setCertificateEntry("cacert", x509CaCert);

		TrustManagerFactory tmFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
		tmFactory.init(caKeyStore);

		// load client certificate and private key to be used by server to authenticate client
		Certificate clientCert = certFactory.generateCertificate(new FileInputStream(clientCertPath));
		X509Certificate x509ClientCert = (X509Certificate) clientCert;

		String password = "keypass";

		KeyStore clientKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
		clientKeyStore.load(null, null);
		clientKeyStore.setCertificateEntry("cert", x509ClientCert);
		clientKeyStore.setKeyEntry("key", loadKey(new File(clientKeyPath)), password.toCharArray(), new Certificate[] {x509ClientCert});

		KeyManagerFactory kmFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
		kmFactory.init(clientKeyStore, password.toCharArray());

		SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
		sslContext.init(kmFactory.getKeyManagers(), tmFactory.getTrustManagers(), null);

		return sslContext.getSocketFactory();
	}

	/*
	 * Load private key from PEM with help of bouncy castle
	 * RSA/DSA private keys are supported
	 */
	public static PrivateKey loadKey(File keyFile) throws Exception {
		PEMParser pemParser = null;
		PrivateKey key = null;
		String bcProvider = "BC";
		try {
			if (Security.getProvider(bcProvider) == null) {
				Security.addProvider(new BouncyCastleProvider());
			}

			pemParser = new PEMParser(new FileReader(keyFile));
			Object keyObj = pemParser.readObject();
			if (keyObj instanceof PEMKeyPair) {
				JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider(bcProvider);
				KeyPair keyPair = converter.getKeyPair((PEMKeyPair) keyObj);
				key = keyPair.getPrivate();
			} else {
				byte[] keyEncoded = null;
				if (keyObj instanceof PKCS8EncryptedPrivateKeyInfo) {
					keyEncoded = ((PKCS8EncryptedPrivateKeyInfo) keyObj).getEncoded();
				} else if (keyObj instanceof PrivateKeyInfo) {
					keyEncoded = ((PrivateKeyInfo) keyObj).getEncoded();
				}
				if (keyEncoded != null) {
					KeyFactory keyFactory = KeyFactory.getInstance("RSA");
					KeySpec keySpec = new PKCS8EncodedKeySpec(keyEncoded);
					key = keyFactory.generatePrivate(keySpec);
				}
			}
		} catch (Exception e) {
			throw e;
		} finally {
			if (pemParser != null) {
				pemParser.close();
			}
		}
		return key;
	}


}
