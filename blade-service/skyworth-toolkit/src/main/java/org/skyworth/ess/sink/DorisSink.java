package org.skyworth.ess.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultRedirectStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.utils.tool.TimeUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;

public class DorisSink implements DataSink {
    private static final Logger logger = LoggerFactory.getLogger(DorisSink.class);
    public static final String USER_NAME = "userName";
    public static final String URL = "url";
    public static final String PASSWORD = "password";
    public static final String LABEL_PRE = "labelPre";
    private String userName;
    private String passwd;
    private String url;
    private String labelPre;
    private JSONObject properties;
    private ArrayBlockingQueue<Object> cacheData;
    private int cacheSize;
    private Long cacheTime;
    private boolean isAsyncWrite;
    private volatile boolean isStop = false;
    private Thread thread;
    private ThreadPoolExecutor threadPoolExecutor;

    public DorisSink(JSONObject params, int cacheSize, Long cacheTime) {
        this.cacheSize = cacheSize;
        this.cacheTime = cacheTime;
        this.isAsyncWrite = isAsyncWrite;
        threadPoolExecutor =
                new ThreadPoolExecutor(
                        5,
                        5,
                        5,
                        TimeUnit.SECONDS,
                        new ArrayBlockingQueue<>(5),
                        Executors.defaultThreadFactory(),
                        new ThreadPoolExecutor.DiscardOldestPolicy());

        cacheData = new ArrayBlockingQueue<>(cacheSize * 2);
        this.userName = params.getString(USER_NAME);
        //http://172.28.17.212:8030/api/test/table1/_stream_load
        this.url = params.getString(URL);
        this.passwd = params.getString(PASSWORD);
        this.labelPre = params.getString(LABEL_PRE);
        this.properties = params;
        final HttpClientBuilder httpClientBuilder = HttpClients.custom()
                .setRedirectStrategy(new DefaultRedirectStrategy() {
                    @Override
                    protected boolean isRedirectable(String method) {
                        return true;
                    }
                });

        thread = new Thread(() -> {
            List<Object> list = new ArrayList<>();
            Date lastWriteTime = new Date();
            int failCnt = 0;
            while (!isStop) {
                try {
                    Date currentTime = new Date();
                    if (list.size() >= cacheSize || currentTime.getTime() - lastWriteTime.getTime() > cacheTime) {
                        if (!list.isEmpty()) {
                            List<Object> writeList = new ArrayList<>();
                            writeList.addAll(list);
                            writeAndFlush(writeList, true);
                            lastWriteTime = currentTime;
                            list.clear();
                        }
                    }
                    Object poll = cacheData.poll();
                    if (poll != null) {
                        list.add(poll);
                    } else {
                        Thread.sleep(1000L);

                    }
                } catch (Exception e) {
                    logger.warn(e.getMessage(), e);
                    failCnt++;
                    if (failCnt >= 3) {
                        failCnt = 0;
                        list.clear();
                    }
                    logger.warn("doris数据导入失败");
                    try {
                        Thread.sleep(3000L);
                    } catch (InterruptedException interruptedException) {
                        interruptedException.printStackTrace();
                    }
                }

            }
            threadPoolExecutor.shutdown();
            if (!cacheData.isEmpty()) {
                while (!cacheData.isEmpty()) {
                    list.add(cacheData.poll());
                }
            }
            if (!list.isEmpty()) {
                writeAndFlush(list, false);
                list.clear();
            }
        });
        thread.start();
    }

    public void commit() {

    }


    public void writeAndFlush(List<Object> list, boolean isAsync) {
        if (isAsync) {
            CompletableFuture.runAsync(() -> writeAndFlushAsync(list), threadPoolExecutor).exceptionally(
                    e -> {
                        logger.error(e.getMessage(), e);
                        return null;
                    });
        } else {
            writeAndFlushAsync(list);
        }

    }

    private void writeAndFlushAsync(List<Object> list) {
        try {
            final HttpClientBuilder httpClientBuilder = HttpClients.custom()
                    .setRedirectStrategy(new DefaultRedirectStrategy() {
                        @Override
                        protected boolean isRedirectable(String method) {
                            return true;
                        }
                    });
            CloseableHttpClient httpclient = httpClientBuilder.build();
            HttpPut httpPut = new HttpPut(url);
            httpPut.removeHeaders(HttpHeaders.CONTENT_LENGTH);
            httpPut.removeHeaders(HttpHeaders.TRANSFER_ENCODING);
            String label = labelPre + UUID.randomUUID();
            httpPut.setHeader("Expect", "100-continue");
            httpPut.setHeader("label", label);//导入标签
            httpPut.setHeader("two_phase_commit", "false");
            httpPut.setHeader("format", "json");
            httpPut.setHeader("strip_outer_array", "true");//导入array
            httpPut.setHeader("Authorization", getBasicAuthHeader(userName, passwd));
            String jsonStr = JSONObject.toJSONString(list);
            ByteArrayEntity byteArrayEntity = new ByteArrayEntity(jsonStr.getBytes(StandardCharsets.UTF_8));
            httpPut.setEntity(byteArrayEntity);
            httpPut.setConfig(RequestConfig.custom().setRedirectsEnabled(true).build());
            try (CloseableHttpResponse resp = httpclient.execute(httpPut)) {
                HttpEntity entity = resp.getEntity();
                JSONObject result = JSON.parseObject(EntityUtils.toString(entity));
                String status = result.getString("Status");
                if (!"Success".equals(status)) {
                    logger.warn("doris数据导入失败，doris接口返回结果：" + result.toJSONString());
                    throw new DataSinkException("doris data import failure," + url + "," + result.toJSONString());
                }
            }
        } catch (IOException e) {
            logger.warn(e.getMessage(), e);
            throw new DataSinkException("doris interface access exception!");
        }
    }

    private void checkStop() {
        if (isStop) {
            throw new RuntimeException("Data source is closed");
        }
    }

    @Override
    public void write(List<JSONObject> list) {
        checkStop();
        if (list == null) return;
        for (Object o : list) {
            try {
                cacheData.put(o);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void writes(List<Object> list) {
        checkStop();
        if (list == null) return;
        for (Object o : list) {
            try {
                cacheData.put(o);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void write(Object obj) {
        checkStop();
        try {
            cacheData.put(obj);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private String getBasicAuthHeader(String username, String password) {
        String auth = username + ":" + password;
        byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.UTF_8));
        return new StringBuilder("Basic ").append(new String(encodedAuth)).toString();
    }

    @Override
    public void close() {
        isStop = true;
    }

    public static void main(String[] args) {
        byte[] a = new byte[1024 * 7 * 100000];
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(a);

        JSONObject dorisConf = new JSONObject();
        dorisConf.put(URL, "http://102.37.32.53:8041/api/bladex/device_log22/_stream_load");
        dorisConf.put(USER_NAME, "root");
        dorisConf.put(PASSWORD, "skyworth@123");
        dorisConf.put(LABEL_PRE, "test-write");
        List<JSONObject> list = new ArrayList<>();
        DorisSink dorisSink = new DorisSink(dorisConf, 10, 5000L);
        JSONObject result=new JSONObject();
        IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
        Number number= identifierGenerator.nextId(new Object());
		result.put("id",number.longValue());
		result.put("modbus_protocol_version","1");
		result.put("device_serial_number","900");
		result.put("device_date_time","2023-10-08 22:10:15");
		result.put("plant_id",3);
		result.put("today_energy",1500);
		result.put("total_energy",100);
		result.put("battery_daily_charge_energy",20);
		result.put("battery_daily_discharge_energy",30);
		result.put("battery_accumulated_charge_energy",60);
		result.put("battery_maximum_cell_voltage",69);
		result.put("battery_minimum_cell_voltage",50);
		result.put("battery_accumulated_discharge_energy",63);
		result.put("battery_maximum_cell_temperature",90);
		result.put("battery_soc",50);
		result.put("battery_voltage",60);
		result.put("battery_current",30);
		result.put("battery_power",80);
		result.put("battery_temperature",50);
		result.put("accumulated_energy_of_positive",40);
		result.put("accumulated_energy_of_negative",30);
		result.put("battery_minimum_cell_temperature",50);
		result.put("accumulated_energy_of_load",60);
		result.put("today_import_energy",9999);
		result.put("today_export_energy",5555);
		result.put("today_load_energy",600);
		result.put("accumulated_energy_to_eps",63);
		result.put("inverter_mode","425566");
		result.put("pv3_voltage",50);
		result.put("phase_c_voltage",90);
		result.put("phase_s_voltage_of_eps",35);
		result.put("phase_t_current_of_eps",50);
		Date currentTime = new Date();
		result.put("createTime",currentTime);
		result.put("updateTime",currentTime);

        list.add(result);
        dorisSink.write(list);
        dorisSink.close();
    }
}
