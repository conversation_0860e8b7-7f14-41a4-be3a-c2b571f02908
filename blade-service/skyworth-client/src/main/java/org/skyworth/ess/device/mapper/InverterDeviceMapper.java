/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.device.vo.InverterDetailHeadVO;
import org.skyworth.ess.device.vo.InverterDevicePageVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface InverterDeviceMapper extends BaseMapper<InverterDevicePageVO> {

	List<InverterDevicePageVO> selectInverterDevicePageByCountry(IPage<InverterDevicePageVO> page,
																 @Param("PageVO") InverterDevicePageVO inverterDevicePageVO,@Param("userType") String userType);

	JSONObject getInverterOverviewByCountry(@Param("plantIds") List<Long> plantIds, @Param("deleteFlag") String deleteFlag);

	InverterDetailHeadVO getInverterDetailHead(@Param("detail") Map<String, Object> detail);

	InverterDetailHeadVO getInverterDetailHeadIsDelete(@Param("detail") Map<String, Object> detail);

	HashMap<String,Object> getPlantInstallInfo(@Param("deviceSerialNumber") String deviceSerialNumber,@Param("plantId")  String plantId);

	HashMap<String,Object> getPlantInstallInfoByIsDelete(@Param("deviceSerialNumber") String deviceSerialNumber,@Param("plantId")  String plantId);
}
