/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service.impl;

import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.vo.DeviceCustomModeVO;
import org.skyworth.ess.device.excel.DeviceCustomModeExcel;
import org.skyworth.ess.device.mapper.DeviceCustomModeMapper;
import org.skyworth.ess.device.service.IDeviceCustomModeService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 设备/逆变器自定义模式表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
public class DeviceCustomModeServiceImpl extends BaseServiceImpl<DeviceCustomModeMapper, DeviceCustomModeEntity> implements IDeviceCustomModeService {

	@Override
	public IPage<DeviceCustomModeVO> selectDeviceCustomModePage(IPage<DeviceCustomModeVO> page, DeviceCustomModeVO deviceCustomMode) {
		return page.setRecords(baseMapper.selectDeviceCustomModePage(page, deviceCustomMode));
	}


	@Override
	public List<DeviceCustomModeExcel> exportDeviceCustomMode(Wrapper<DeviceCustomModeEntity> queryWrapper) {
		List<DeviceCustomModeExcel> deviceCustomModeList = baseMapper.exportDeviceCustomMode(queryWrapper);
		//deviceCustomModeList.forEach(deviceCustomMode -> {
		//	deviceCustomMode.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceCustomMode.getType()));
		//});
		return deviceCustomModeList;
	}

	@Override
	public int updateCustomMode(DeviceCustomModeEntity entity) {
		return baseMapper.updateCustomMode(entity);
	}
}
