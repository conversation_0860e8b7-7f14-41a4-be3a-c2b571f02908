/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.mapper;

import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.vo.WifiStickPlantVO;
import org.skyworth.ess.plant.excel.WifiStickPlantExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * wifi棒对应站点表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface WifiStickPlantMapper extends BaseMapper<WifiStickPlantEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param WifiStickPlant
	 * @return
	 */
	List<WifiStickPlantVO> selectWifiStickPlantPage(IPage page, WifiStickPlantVO WifiStickPlant);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<WifiStickPlantExcel> exportWifiStickPlant(@Param("ew") Wrapper<WifiStickPlantEntity> queryWrapper);

	/**
	 * 根据站点id信息查询设备厂商
	 * @param appVO
	 * @return
	 */
	List<WifiStickPlantVO> queryDeviceCompany(@Param("params")AppVO appVO);
	List<WifiStickPlantEntity> queryDeviceSerialNumberList(@Param("list")List<Long> list);

	/**
	 * 批量删除站点和逆变器关系
	 * @param plantIdList
	 * @return
	 */
	int batchDeleteLogicByPlantId(@Param("list")List<Long> plantIdList,@Param("updateUserAccount") String updateUserAccount);

	List<WifiStickPlantEntity> queryByDeviceSerialNumberList(@Param("list")List<String> list);
	List<WifiStickPlantEntity> queryOwnerData(@Param("createUser")Long createUser);

	int updateDataByCondition(@Param("params")WifiStickPlantEntity updateOwner);

	List<WifiStickPlantEntity> getWifiStickInfo(@Param("list")List<WifiStickPlantEntity> wifiStickPlantEntities);

	void updateStartupByBackstage(@Param("params")WifiStickPlantEntity wifiStickPlant);

	void batchDeleteLogicByPlantIdAndSn(@Param("plantId")Long plantId,@Param("deviceSerialNumber") String deviceSerialNumber,@Param("updateUserAccount") String account);

	List<Long> getWifiStickInfoAndBatteryInfo(@Param("list")List<Long> longPlantIdList, @Param("deviceSerialNumber")String deviceSerialNumber);
}
