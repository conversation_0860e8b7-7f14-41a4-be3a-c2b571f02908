<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.company.mapper.AgentUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentUserInfoResultMap" type="org.skyworth.ess.entity.AgentUserInfoEntity">
        <result column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="user_type" property="userType"/>
        <result column="user_id" property="userId"/>
        <result column="user_img_biz_key" property="userImgBizKey"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_id" property="deptId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <delete id="deleteByAgentId">
        delete
        from agent_user_info a
        where a.agent_id = #{agentId}
    </delete>

    <delete id="deleteUserByJsonList">
        <foreach collection="list" item="item" separator=";">
            delete
            from agent_user_info
            where user_id = #{item.userId}
            and user_type in
            <foreach collection="item.userType.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </foreach>
    </delete>

    <select id="queryAgentUserList" resultMap="agentUserInfoResultMap">
        select
        aui.user_id ,
        aui.user_type,
        aci.dept_id
        from
        agent_user_info aui
        inner join agent_company_info aci on
        aui.agent_id = aci.id
        where
        aui.is_deleted = 0
        and aci.is_deleted = 0
        and aci.id in
        <foreach collection="ids" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <delete id="updateUserInfo4Delete">
        update agent_user_info set is_deleted = 1,update_time=now()
        where user_id in
        <foreach collection="userIdList" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
