<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.RoleMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleMenuResultMap" type="org.springblade.system.entity.RoleMenu">
        <id column="id" property="id"/>
        <result column="menu_id" property="menuId"/>
        <result column="role_id" property="roleId"/>
    </resultMap>

    <select id="selectRoleMenuPage" resultMap="roleMenuResultMap">
        select * from blade_role_menu where is_deleted = 0
    </select>

    <select id="roleTreeKeys" resultMap="roleMenuResultMap">
        select * from blade_role_menu where menu_id IN
        ( select id from blade_menu
        where is_deleted = 0
        and platform_type = #{platformType}
        and id IN
            ( SELECT menu_id FROM blade_role_menu WHERE role_id IN
                <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
        )
    </select>

</mapper>
