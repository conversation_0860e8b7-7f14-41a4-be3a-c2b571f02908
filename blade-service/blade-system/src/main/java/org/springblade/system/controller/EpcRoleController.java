package org.springblade.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.Role;
import org.springblade.system.service.IRoleService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("/epc-role")
@Api(value = "epc角色", tags = "epc角色")
public class EpcRoleController extends BladeController {
	private final IRoleService roleService;

	@GetMapping("/select/role-type")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "角色列表", notes = "根据角色类型查询角色清单")
	public R<List<Role>> selectByRoleType(@RequestParam String roleType) {
		List<Role> list = roleService.list(Wrappers.<Role>lambdaQuery().eq(Role::getRoleType, roleType).eq(Role::getTenantId, CommonConstant.AGENT_TENANT_ID));
		return R.data(list);
	}
}
