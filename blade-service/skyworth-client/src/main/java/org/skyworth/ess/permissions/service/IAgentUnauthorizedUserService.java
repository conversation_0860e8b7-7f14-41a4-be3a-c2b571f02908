/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.permissions.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.permissions.entity.AgentUnauthorizedUserEntity;
import org.skyworth.ess.permissions.vo.AgentUnauthorizedUserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 站点挂的代理商下，无权限操作的用户id 服务类
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
public interface IAgentUnauthorizedUserService extends BaseService<AgentUnauthorizedUserEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param agentUnauthorizedUser
	 * @return
	 */
	IPage<AgentUnauthorizedUserVO> selectAgentUnauthorizedUserPage(IPage<AgentUnauthorizedUserVO> page, AgentUnauthorizedUserVO agentUnauthorizedUser);

	/**
	 * 根据站点id和用户id删除记录
	 *
	 * @return
	 */
	boolean removeByPlantId(Long plantId);

}
