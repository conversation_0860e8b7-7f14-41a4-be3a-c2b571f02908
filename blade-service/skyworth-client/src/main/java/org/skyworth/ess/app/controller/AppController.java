package org.skyworth.ess.app.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.*;
import org.skyworth.ess.aspect.AdvancedSettingsOperationLog;
import org.skyworth.ess.company.fegin.AgentClientBiz;
import org.skyworth.ess.constant.SourceEnum;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.vo.PlantDetailVO;
import org.skyworth.ess.plant.vo.PlantListVO;
import org.skyworth.ess.plant.vo.PlantVO;
import org.skyworth.ess.plant.vo.PlantChangeUserVO;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.feign.ISysClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/app")
@Api(value = "app接口", tags = "app接口")
@Slf4j
public class AppController extends BladeController {

	@Resource
	private IAppService appService;

	@Resource
	private IAppSetupService appSetupService;

	@Resource
	private AgentClientBiz agentClient;

	@Resource
	private BladeRedis bladeRedis;

	@Resource
	private ISysClient sysClient;
	@PostMapping("/plant/add")
	@ApiOperation(value = "新增站点", notes = "新增站点")
	@ApiLog("app新增站点")
	public R<String> addPlant(@RequestBody @Validated PlantEntity plant) {
		R<String> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		try {
			if (StringUtil.isBlank(plant.getPlantName()) || StringUtil.isBlank(plant.getCountryCode())
				|| StringUtil.isBlank(plant.getDetailAddress())) {
				r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.autoGetMessage(currentLanguage));
				return r;
			}
			return R.data(appService.addPlant(plant));
		} catch (Exception e) {
			if (e instanceof BusinessException) {
				r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.getCode());
				r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.autoGetMessage(currentLanguage), plant.getPlantName()));
				return r;
			} else {
				r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.autoGetMessage(currentLanguage));
				return r;
			}
		}

	}

	@PostMapping("/wifidongle/add")
	@ApiOperation(value = "新增逆变器信息", notes = "新增逆变器信息")
	@ApiLog("app新增逆变器信息")
	public R<String> addWifiDongle(@RequestBody WifiStickPlantEntity wifiStickPlantEntity) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<String> r = new R<>();
		Long id = appService.addWifiDongle(wifiStickPlantEntity);
		if (-100L == id) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100003.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100003.autoGetMessage(currentLanguage), wifiStickPlantEntity.getDeviceSerialNumber()));
			return r;
		}
		if (-200L == id) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100022.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100022.autoGetMessage(currentLanguage), wifiStickPlantEntity.getDeviceSerialNumber()));
			return r;
		}
		if (-300L == id) {
			throw new BusinessException("client.inverter.max.parallel.limit");
		}
		if (-400L == id) {
			throw new BusinessException("client.inverter.bind.same.parallel");
		}
		if (-500L == id) {
			log.error("处理手机扫码添加逆变器连续调用接口BUG，ID：{}:",id);
			return R.success("");
		}

		return R.data(StrUtil.toString(id));
	}

	@PostMapping("/wifidongle/unbind")
	@ApiOperation(value = "解绑逆变器信息", notes = "解绑逆变器信息")
	@ApiLog("app解绑逆变器信息")
	public R<String> unbindWifiDongle(@RequestBody WifiStickPlantEntity wifiStickPlantEntity) {
		return appService.unbindWifiDongle(wifiStickPlantEntity);
	}

	@PostMapping("/inverter/parallel/enable")
	@ApiOperation(value = "查询当前站点的逆变器是否可以进行并机", notes = "查询当前站点的逆变器是否可以进行并机")
	@ApiLog("查询当前站点的逆变器是否可以进行并机")
	public R<JSONObject> inverterParallelEnable(@RequestBody AppVO appVO) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("inverterParallelEnable", appService.inverterParallelEnable(appVO));
		return R.data(jsonObject);
	}

	@PostMapping("/inverter")
	@ApiOperation(value = "查询逆变器sn号", notes = "查询逆变器sn号")
	@ApiLog("app查询逆变器sn号")
	@Deprecated
	public R<AppVO> inverterByWifiStickSn(@RequestBody AppVO appVO) {
		return R.data(appService.inverterByWifiStickSn(appVO));
	}

	@PostMapping("/inverter/match")
	@ApiOperation(value = "扫描电池查询逆变器和电池匹配", notes = "扫描电池查询逆变器和电池匹配")
	@ApiLog("app扫描电池查询逆变器和电池匹配")
	public R<AppVO> inverterMatch(@RequestBody AppVO appVO) {
		AppVO vo = new AppVO();
		vo.setMatchOrNot(appService.inverterMatch(appVO));
		return R.data(vo);
	}

	@PostMapping("/batteryDevice/add")
	@ApiOperation(value = "增加电池", notes = "增加电池")
	@ApiLog("app增加电池")
	public R<String> addBatteryDevice(@RequestBody AppVO appVO) {
		return appService.addBatteryDevice(appVO);
	}

	@PostMapping("/plant/queryBySelf/{pageSize}/{current}")
	@ApiOperation(value = "查询用户站点列表信息", notes = "查询用户站点列表信息")
	@ApiLog("app查询用户站点列表信息")
	public R<IPage<PlantVO>> queryPlantBySelf(@RequestBody AppVO appVO,
											  @ApiParam(value = "每页大小", required = true) @PathVariable("pageSize") int pageSize,
											  @ApiParam(value = "当前页", required = true) @PathVariable("current") int current) {
		Query query = new Query();
		query.setCurrent(current);
		query.setSize(pageSize);
		return R.data(appService.queryPlantBySelf(appVO, query));
	}

	@PostMapping("/plant/changeUser")
	@ApiOperation(value = "更改站点所属用户", notes = "更改站点所属用户")
	@ApiLog("app更改站点所属用户")
	public R<Boolean> changeUser(@RequestBody PlantChangeUserVO plantChangeUserVO) {
		return R.data(appService.changeUser(plantChangeUserVO).getData());
	}

	@GetMapping("/plant/sendMessage")
	@ApiOperation(value = "更改站点所属用户", notes = "更改站点所属用户")
	@ApiLog("app更改站点所属用户")
	public R<Boolean> sendMessage(String phone, String phoneDiallingCode, String email) {
		appService.sendMessage(phone, phoneDiallingCode, email);
		return R.data(true);
	}
	@PostMapping("/plant/queryBySelf/v2/{pageSize}/{current}")
	@ApiOperation(value = "查询用户站点列表信息", notes = "查询用户站点列表信息")
	@ApiLog("app查询用户站点列表信息")
	public R<IPage<PlantListVO>> queryPlantBySelfV2(@RequestBody AppVO appVO,
													@ApiParam(value = "每页大小", required = false) @PathVariable("pageSize") int pageSize,
													@ApiParam(value = "当前页", required = false) @PathVariable("current") int current) {
		Query query = new Query();
		// 兼容旧版本
		if(appVO.getPageSize() == null) {
			query.setCurrent(current);
			query.setSize(pageSize);
		} else {
			query.setCurrent(appVO.getCurrent());
			query.setSize(appVO.getPageSize());
		}

		return R.data(appService.queryPlantBySelfV2(appVO, query));
	}

	@PostMapping("/plant/detail")
	@ApiOperation(value = "查询电站代理商信息", notes = "查询电站代理商信息")
	@ApiLog("查询电站代理商信息")
	public R<PlantDetailVO> plantDetail(@RequestBody AppVO appVO) {
		return R.data(appService.getPlantDetail(appVO));
	}
// todo 暂时保留一个版本
//	@PostMapping("/plant/queryRunningState")
//	@ApiOperation(value = "查询电站运行状态报表信息", notes = "查询电站运行状态报表信息")
//	@ApiLog("app查询电站运行状态报表信息")
//	public R<AppReportHeaderVO> queryPlantRunningState(@RequestBody AppVO appVO) {
//		return R.data(appService.queryPlantRunningState(appVO));
//	}

//	@PostMapping("/plant/queryRunningState/v2")
//	@ApiOperation(value = "查询电站运行状态报表信息", notes = "查询电站运行状态报表信息")
//	@ApiLog("app查询电站运行状态报表信息")
//	public R<AppReportHeaderVO> queryPlantRunningStateV2(@RequestBody AppVO appVO) {
//		return R.data(appService.queryPlantRunningStateV2(appVO));
//	}

//	@PostMapping("/plant/queryRunningState/v3")
//	@ApiOperation(value = "查询电站运行状态报表信息", notes = "查询电站运行状态报表信息")
//	@ApiLog("app查询电站运行状态报表信息")
//	public R<AppReportHeaderVO> queryPlantRunningStateV3(@RequestBody AppVO appVO) {
//		appVO.setInterfaceVersion("v3");
//		return R.data(appService.queryPlantRunningStateV2(appVO));
//	}
//	@PostMapping("/plant/queryRunningStateChart/v2")
//	@ApiOperation(value = "查询电站运行状态报表信息折线图数据", notes = "查询电站运行状态报表信息折线图数据")
//	@ApiLog("app查询电站运行状态报表信息折线图数据")
//	public R<AppReportHeaderVO> queryRunningStateChartV2(@RequestBody AppVO appVO) {
//		return R.data(appService.queryRunningStateChartV2(appVO));
//	}
	// todo end
	@PostMapping("/plant/queryRunningStateHeader/v2")
	@ApiOperation(value = "查询电站运行状态报表信息头部数据", notes = "查询电站运行状态报表信息头部数据")
	@ApiLog("app查询电站运行状态报表信息头部数据")
	public R<AppReportHeaderVO> queryPlantRunningStateHeaderV2(@RequestBody AppVO appVO) {
		return R.data(appService.queryPlantRunningStateHeaderV2(appVO));
	}

	@PostMapping("/plant/queryRunningStateChart/v3")
	@ApiOperation(value = "查询电站运行状态报表信息折线图数据", notes = "查询电站运行状态报表信息折线图数据")
	@ApiLog("app查询电站运行状态报表信息折线图数据")
	public R<AppReportHeaderVO> queryRunningStateChartV3(@RequestBody AppVO appVO) {
		appVO.setInterfaceVersion("v3");
		return R.data(appService.queryRunningStateChartV2(appVO));
	}
	@PostMapping("/device/detail")
	@ApiOperation(value = "查询逆变器详细信息", notes = "查询逆变器详细信息")
	@ApiLog("app查询逆变器详细信息")
	public R<AppDeviceDetail> queryDeviceDetail(@RequestBody AppVO appVO) {
		return R.data(appService.queryDeviceDetail(appVO));
	}

	@PostMapping("/device/detail/v2")
	@ApiOperation(value = "查询逆变器详细信息", notes = "查询逆变器详细信息")
	@ApiLog("app查询逆变器详细信息")
	public R<AppDeviceDetail> queryDeviceDetailV2(@RequestBody AppVO appVO) {
		return R.data(appService.queryDeviceDetailV2(appVO));
	}

	@PostMapping("/battery/list")
	@ApiOperation(value = "查询电池出厂信息", notes = "查询电池出厂信息")
	@ApiLog("app查询电池出厂信息")
	public R<List<AppBatteryExitFactoryInfoVO>> queryBatteryExitFactoryList(@RequestBody AppVO appVO) {
		return R.data(appService.queryBatteryExitFactoryList(appVO));
	}

	@PostMapping("/battery/delete")
	@ApiOperation(value = "删除电池", notes = "删除电池")
	@ApiLog("app删除电池")
	public R<Boolean> deleteBattery(@RequestBody AppVO appVO) {
		return R.data(appService.deleteBattery(appVO.getBatteryMapDeviceId()));
	}

	/**
	 * inverterModeSetup 分时设置查询
	 *
	 * @param appSetRequestVO
	 * @return
	 */
	@GetMapping("/inverter/mode")
	@ApiOperation(value = "查询当前模式", notes = "查询当前模式")
	@ApiLog("app查询当前模式")
	public R<JSONObject> queryInverterMode(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//默认查询逆变器类型
		requestVO.setDeviceType(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE) == null ? 0 : Integer.parseInt(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE)));
		//2：分时设置
		requestVO.setSetCategory(2);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}


	@PostMapping("/plant/delete")
	@ApiOperation(value = "删除站点", notes = "删除站点")
	@ApiLog("app删除站点")
	public R<Boolean> deletePlant(@RequestParam String ids) {
		return appService.deletePlant(ids);
	}


	@PostMapping("/inverter/list")
	@ApiOperation(value = "根据站点获取不同公司模式", notes = "根据站点获取不同公司模式")
	@ApiLog("app根据站点获取不同公司模式")
	public R<List<InverterModeVO>> inverterList(@RequestBody AppVO appVO) {
		return R.data(appService.inverterList(appVO));
	}

	@GetMapping("/advancedSetup/queryAll")
	@ApiOperation(value = "查询高级设置", notes = "查询设置")
	@ApiLog("app查询高级设置")
	public R<JSONObject> getAdvancedSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//默认查询逆变器类型
		requestVO.setDeviceType(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE) == null ? 0 : Integer.parseInt(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE)));
		//0：高级设置
		requestVO.setSetCategory(0);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/quickSetup/queryAll")
	@ApiOperation(value = "查询开机快速设置", notes = "查询设置")
	@ApiLog("app查询开机快速设置")
	public R<JSONObject> getQuickSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//默认查询逆变器类型
		requestVO.setDeviceType(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE) == null ? 0 : Integer.parseInt(appSetRequestVO.get(EntityFieldConstant.DEVICE_TYPE)));
		//1：开机设置
		requestVO.setSetCategory(1);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}


	@GetMapping("/advancedSetup/queryPwd")
	@ApiOperation(value = "查询高级设置密码", notes = "查询设置")
	@ApiLog("app查询高级设置密码")
	public R<JSONObject> getAdvancedPwd() {
		return R.data(appSetupService.getAdvancedPwd());
	}

	@PostMapping("/commonSetup/issue")
	@ApiOperation(value = "下发设置", notes = "下发设置")
	@ApiLog("app下发设置")
	@AdvancedSettingsOperationLog(type = SourceEnum.APP)
	public R issueSetup(@RequestBody @Validated AppAdvancedSetup deviceAdvancedSetup) {
		return appSetupService.issueSetup(deviceAdvancedSetup);
	}

	@PostMapping("/commonSetup/setTimeZone")
	@ApiOperation(value = "下发时区设置", notes = "下发时区设置")
	@ApiLog("下发时区设置")
	public R setTimeZone(@RequestBody AppAdvancedSetup deviceAdvancedSetup) {
		return appSetupService.setTimeZone(deviceAdvancedSetup);
	}

	@PostMapping("/plant/edit")
	@ApiOperation(value = "站点编辑", notes = "站点编辑")
	@ApiLog("app站点编辑")
	public R<String> editPlant(@RequestBody @Validated PlantEntity plant) {
		R<String> r = new R<>();
		try {
			return appService.editPlant(plant);
		} catch (Exception e) {
			String currentLanguage = CommonUtil.getCurrentLanguage();
			r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.autoGetMessage(currentLanguage));
			return r;
		}

	}

	@PostMapping("/user/delete")
	@ApiOperation(value = "删除用户", notes = "删除用户")
	@ApiLog("app删除用户")
	public R<Boolean> deleteUser(@RequestBody AppVO appVO) {
		return appService.deleteUser(appVO);
	}


	@GetMapping("/operation/company")
	@ApiOperation(value = "获取代理商信息", notes = "获取代理商信息")
	@ApiLog("获取代理商信息")
	public R<List<AgentCompanyVO>> getAgentCompanyInfo() {
		return R.data(agentClient.agentCompanyInfoByIds(new ArrayList<>()).getData());
	}

	@GetMapping("/operation/agentUser")
	@ApiOperation(value = "获取代理商所属人员信息信息", notes = "获取代理商所属人员信息信息")
	@ApiLog("获取代理商所属人员信息信息")
	public R<List<AgentUserVo>> getAgentUserInfo(@RequestParam("agentId") Long agentId, @RequestParam("deptId") Long deptId) {
		return R.data(agentClient.agentUserInfo(agentId, deptId).getData());
	}

	/**
	 * 保存当前用户的语言
	 */
	@PostMapping("/language/change")
	@ApiOperation(value = "用户当前语言", notes = "用户当前语言")
	@ApiLog("用户当前语言")
	public R<String> switchLang(@RequestParam("userId") Long userId, @RequestParam("language") String language) {
		bladeRedis.set("JPush:" + userId, language);
		return R.success("switch success");
	}

	/**
	 * 保存当前用户的语言
	 */
	@GetMapping("/plant/queryDeviceDateAndTimeZone")
	@ApiOperation(value = "用户当前语言", notes = "用户当前语言")
	@ApiLog("用户当前语言")
	public R<JSONObject> queryDeviceDateAndTimeZone(@ApiParam(value = "站点ID", required = true) @RequestParam Long plantId) {
		return R.data(appService.queryDeviceDateAndTimeZone(plantId));
	}

	@GetMapping("/dict/alarmConfigList")
	@ApiOperation(value = "告警信息配置", notes = "告警信息配置")
	@ApiLog("告警信息配置")
	public R<JSONObject> alarmConfigList() {
		String language = CommonUtil.getCurrentLanguage();
		String role = AuthUtil.getUserRole();
		String deptId = CommonConstant.DEFAULT_VALUE_MINUS_ONE.equals(AuthUtil.getDeptId())?"":AuthUtil.getDeptId();
		return sysClient.alarmConfigList(language,role,deptId);
	}
}
