toolkit.azure.blob.file.download.fail=AzureBlobFileService, Datei herunterladen: Ausnahme %s
toolkit.azure.blob.file.record.upload.fail=AzureBlobFileService,upload: Die hochgeladene Aufzeichnung ist fehlgeschlagen
toolkit.azure.paramater.cannot.empty=Die Parameter \u201Econtainer\u201C und \u201EazureKey\u201C d\u00FCrfen nicht leer sein.
toolkit.azure.thumbnail.upload.error=Ungew\u00F6hnliches Hochladen der Miniaturansicht, Fehler: %s
toolkit.blob.get.exception=Ausnahme beim Abrufen von BlobContainer, <PERSON>hler: %s
toolkit.file.upload.error=Ausnahme beim Ho<PERSON>laden der Datei, Fehler: %s
toolkit.sastoken.get.error=SasToken abrufen, <PERSON>hler: %s
