package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.skyworth.ess.util.BigDecimalSerializer;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器日志表，记录22数据;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-14
 */
@ApiModel(value = "设备/逆变器日志表，记录22数据",description = "")
@TableName("device_log22")
@Data
public class DeviceLog22 extends BaseEntity implements Serializable,Cloneable{

	@ApiModelProperty(name = "同步状态（N未同步；Y已同步）",notes = "")
	private String synchStatus ;

	/** 电压 phase a */
	@ApiModelProperty(name = "电压 phase a",notes = "")
	private BigDecimal phaseAVoltage ;
	/** 电流 phase a */
	@ApiModelProperty(name = "电流 phase a",notes = "")
	private BigDecimal phaseACurrent ;
	/** 功率 phase a */
	@ApiModelProperty(name = "功率 phase a",notes = "")
	private BigDecimal phaseAPower ;
	/** 频率 phase  a */
	@ApiModelProperty(name = "频率 phase  a",notes = "")
	private BigDecimal phaseAFrequency ;
	/** 电压 phase b */
	@ApiModelProperty(name = "电压 phase b",notes = "")
	private BigDecimal phaseBVoltage ;
	/** 电流 phase b */
	@ApiModelProperty(name = "电流 phase b",notes = "")
	private BigDecimal phaseBCurrent ;
	/** 功率 phase b */
	@ApiModelProperty(name = "功率 phase b",notes = "")
	private BigDecimal phaseBPower ;
	/** 频率 phase  b */
	@ApiModelProperty(name = "频率 phase  b",notes = "")
	private BigDecimal phaseBFrequency ;
	/** 电压 phase c */
	@ApiModelProperty(name = "电压 phase c",notes = "")
	private BigDecimal phaseCVoltage ;
	/** 电流 phase c */
	@ApiModelProperty(name = "电流 phase c",notes = "")
	private BigDecimal phaseCCurrent ;
	/** 功率 phase  c */
	@ApiModelProperty(name = "功率 phase  c",notes = "")
	private BigDecimal phaseCPower ;
	/** 频率 phase  c */
	@ApiModelProperty(name = "频率 phase  c",notes = "")
	private BigDecimal phaseCFrequency ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv1Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv1Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt1Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv2Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv2Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt2Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv3Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv3Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt3Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal innerTemperature ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private String inverterMode ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private String errorCode ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal totalEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal totalGenerationTime ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal activePower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal reactivePower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayPeakPower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal powerFactor ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv4Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv4Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt4Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRWattOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSWattOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTWattOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfPositive ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfNegative ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRWattOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSWattOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTWattOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1NPhaseVoltageOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2NPhaseVoltageOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3NPhaseVoltageOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1CurrentOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2CurrentOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3CurrentOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1NPhaseVoltageOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2NPhaseVoltageOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3NPhaseVoltageOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1CurrentOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2CurrentOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3CurrentOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayImportEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayExportEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayLoadEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal frequencyOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRVoltageOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRCurrentOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRWattOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal frequencyOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSVoltageOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSCurrentOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSWattOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTVoltageOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTCurrentOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTWattOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal dailyEnergyToEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyToEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batterySoc ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryTemperature ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryVoltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryCurrent ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryPower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryDailyChargeEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedChargeEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryDailyDischargeEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedDischargeEnergy ;
	/** 错误信息 */
	@ApiModelProperty(name = "错误信息",notes = "")
	private String errorMessage4 ;


	@ApiModelProperty(name = "",notes = "")
	private BigDecimal numberOfBattery ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal soh ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pvTotalInputPower ;

	public DeviceLog22(){

	}

	/** 站点ID */
	@ApiModelProperty(name = "站点ID",notes = "")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private long plantId ;

	/** 设备时间，设备上报时时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(name = "设备时间，设备上报时时间",notes = "")
	private Date deviceDateTime ;

	/** modbus协议版本 */
	@ApiModelProperty(name = "modbus协议版本",notes = "")
	private String modbusProtocolVersion ;


	/** 逆变器/设备SN */
	@ApiModelProperty(name = "逆变器/设备SN",notes = "")
	private String deviceSerialNumber ;

	/** 创建人账号 */
	@ApiModelProperty(name = "创建人账号",notes = "")
	private String createUserAccount ;
	/** 更新人账号 */
	@ApiModelProperty(name = "更新人账号",notes = "")
	private String updateUserAccount ;

	@ApiModelProperty(name = "单体电池最高电压",notes = "")
	private BigDecimal batteryMaximumCellVoltage;

	@ApiModelProperty(name = "单体电池最低电压",notes = "")
	private BigDecimal batteryMinimumCellVoltage;

	@ApiModelProperty(name = "单体电池最高温度",notes = "")
	private BigDecimal batteryMaximumCellTemperature;

	@ApiModelProperty(name = "单体电池最低温度",notes = "")
	private BigDecimal batteryMinimumCellTemperature;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhaseCurrentOfGenerator ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhaseCurrentOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhaseCurrentOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorFrequency;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTodayEnergy;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTotalEnergy;

	@ApiModelProperty(value = "电池循环次数")
	private BigDecimal batteryMaximumCyclesTimes;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L1相交流耦合电压，L1 phase voltage of AC Couple")
	private BigDecimal l1PhaseVoltageOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L1相交流耦合电流，L1 phase current of AC Couple")
	private BigDecimal l1PhaseCurrentOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L1相交流耦合功率，L1 phase power of AC Couple")
	private BigDecimal l1PhasePowerOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L2相交流耦合电压，L2 phase voltage of AC Couple")
	private BigDecimal l2PhaseVoltageOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L2相交流耦合电流，L2 phase current of AC Couple")
	private BigDecimal l2PhaseCurrentOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L2相交流耦合功率，L2 phase power of AC Couple")
	private BigDecimal l2PhasePowerOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L3相交流耦合电压，L3 phase voltage of AC Couple")
	private BigDecimal l3PhaseVoltageOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L3相交流耦合电流，L3 phase current of AC Couple")
	private BigDecimal l3PhaseCurrentOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L3相交流耦合功率，L3 phase power of AC Couple")
	private BigDecimal l3PhasePowerOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "交流耦合频率，Frequency of AC Couple")
	private BigDecimal frequencyOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "今日交流耦合能量（单位kWh），Energy today of AC Couple in kWh")
	private BigDecimal energyTodayOfAcCoupleKwh;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "总交流耦合能量，Energy total of AC Couple")
	private BigDecimal energyTotalOfAcCouple;
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "今日交流耦合能量（单位wh)，Energy today of AC Couple in wh")
	private BigDecimal energyTodayOfAcCoupleWh;
	// 104A 中的 4-5位
	private String batteryStatus;
	// 并机 begin
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	// 13AC
	private BigDecimal dailyEnergyOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal monthlyEnergyOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1ApparentPowerSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2ApparentPowerSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3ApparentPowerSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	// 13C0
	private BigDecimal dailySupportEnergySumToBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedSupportEnergySumToBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1ApparentPowerSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2ApparentPowerSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3ApparentPowerSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTodayEnergySum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTotalEnergySum;

	@ApiModelProperty(name = "",notes = "")
	// 13D8
	private BigDecimal pvlDailyGeneratingEnergySum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pvlAccumulatedEnergySum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal totallyInputDcWattSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryPowerSum;

	@ApiModelProperty(name = "",notes = "")
	// 13E0
	private BigDecimal batteryDailyChargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedChargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryDailyDischargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedDischargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private String isParallelMode;
	// 并机 end
}
