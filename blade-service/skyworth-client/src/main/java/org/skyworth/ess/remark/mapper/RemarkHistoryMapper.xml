<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.remark.mapper.RemarkHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="remarkHistoryResultMap" type="org.skyworth.ess.remark.entity.RemarkHistoryEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="remark" property="remark"/>
        <result column="business_id" property="businessId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectRemarkHistoryPage" resultMap="remarkHistoryResultMap">
        select * from plant_remark_history where is_deleted = 0
    </select>


    <select id="exportRemarkHistory" resultType="org.skyworth.ess.remark.excel.RemarkHistoryExcel">
        SELECT * FROM plant_remark_history ${ew.customSqlSegment}
    </select>

</mapper>
