package org.springblade.gateway.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.NettyWriteResponseFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.RequestPath;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class LoginResponseFilter implements GlobalFilter, Ordered {

	private final BladeRedis bladeRedis;

	@Override
	public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
		RequestPath path = exchange.getRequest().getPath();
		List<String> list = exchange.getRequest().getHeaders().get("User-Type");
		String sourceFrom = "";
		if (CollectionUtil.isNotEmpty(list)) {
			sourceFrom = list.get(0);
		}
		ServerHttpResponse originalResponse = exchange.getResponse();
		if ("/oauth/token".equalsIgnoreCase(path.value())) {
			originalResponse.getHeaders().setContentType(MediaType.APPLICATION_JSON);
			DataBufferFactory bufferFactory = originalResponse.bufferFactory();
			ServerHttpResponseDecorator response = new ServerHttpResponseDecorator(originalResponse) {
				@Override
				public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
					if (getStatusCode().equals(HttpStatus.OK) && body instanceof Flux) {
						Flux<? extends DataBuffer> fluxBody = Flux.from(body);
						return super.writeWith(fluxBody.buffer().map(dataBuffers -> {
							DataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();
							DataBuffer join = dataBufferFactory.join(dataBuffers);
							byte[] content = new byte[join.readableByteCount()];
							join.read(content);
							DataBufferUtils.release(join);
							// 流转为字符串
							String responseData = new String(content, Charsets.UTF_8);
							R<Object> r = new R<>();
							r.setCode(ResultCode.SUCCESS.getCode());
							Map map = JSONObject.parseObject(responseData, Map.class);
							String errorDescription = (String) map.get("error_description");
							if (StringUtil.isBlank(errorDescription)) {
								Object userId = map.get("user_id");
								log.info("get user id : {}", userId);
								if (userId != null) {
									Boolean del = bladeRedis.del(CommonConstant.BLADE_USER_CHANGE_PRE + userId.toString());
									log.info("delete user change info result : {}", del);
								}
								r.setData(map);
							} else {
								List<String> strings = exchange.getRequest().getHeaders().get("Accept-Language");
								String languageStr = "";
								if (CollectionUtil.isNotEmpty(strings)) {
									languageStr = strings.get(0);
									Locale locale = Locale.lookup(Locale.LanguageRange.parse(languageStr), List.of(Locale.getAvailableLocales()));
									// 获取语言类型
									languageStr = locale.getLanguage();
								} else {
									strings = exchange.getRequest().getHeaders().get("accept-language");
									if (CollectionUtil.isNotEmpty(strings)) {
										languageStr = strings.get(0);
										Locale locale = Locale.lookup(Locale.LanguageRange.parse(languageStr), List.of(Locale.getAvailableLocales()));
										// 获取语言类型
										languageStr = locale.getLanguage();
									}
								}
//                                if (languageStr.contains(CommonConstant.CURRENT_LANGUAGE_ZH)) {
//                                    setZhMsg(errorDescription, r);
//                                } else {
//                                    setEnMsg(errorDescription, r);
//                                }

//								String currentLanguage = CommonUtil.getCurrentLanguage();
								autoSetMsg(errorDescription, r, languageStr);
							}
							byte[] uppedContent = JSON.toJSONString(r).getBytes(Charsets.UTF_8);
							originalResponse.getHeaders().setContentLength(uppedContent.length);
							return bufferFactory.wrap(uppedContent);
						}));
					}
					return chain.filter(exchange);
				}

				private void autoSetMsg(String errorDescription, R<Object> r, String language) {
					if ("用户名或密码错误".equalsIgnoreCase(errorDescription) || "Bad credentials".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100013.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100013.autoGetMessage(language));
					} else if ("Token authorization has expired".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100014.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100014.autoGetMessage(language));
					} else if ("User's tenant information not obtained".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100015.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100015.autoGetMessage(language));
					} else if ("Too many login errors, please try again later".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100016.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100016.autoGetMessage(language));
					} else if ("100020".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.autoGetMessage(language));
					} else if ("100113".equalsIgnoreCase(errorDescription) || "The user has not registered, please register before login.".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.autoGetMessage(language));
					} else if ("100006".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(language));
					} else if ("100103".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.autoGetMessage(language));
					} else if ("Wrong Verification Code".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(language));
					} else if ("100119".equalsIgnoreCase(errorDescription)) {
						r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100119.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100119.autoGetMessage(language));
					} else {
						r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
						r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.autoGetMessage(language));
					}
				}

//                private void setZhMsg(String errorDescription, R<Object> r) {
//                    if ("用户名或密码错误".equalsIgnoreCase(errorDescription) || "Bad credentials".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100013.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100013.getMessageZh());
//                    } else if ("Token authorization has expired".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100014.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100014.getMessageZh());
//                    } else if ("User's tenant information not obtained".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100015.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100015.getMessageZh());
//                    } else if ("Too many login errors, please try again later".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100016.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100016.getMessageZh());
//                    } else if ("100020".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.getMessageZh());
//                    } else if ("100113".equalsIgnoreCase(errorDescription) || "The user has not registered, please register before login.".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.getMessageZh());
//                    } else if ("100006".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getMessageZh());
//                    } else if ("100103".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getMessageZh());
//                    } else if ("Wrong Verification Code".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getMessageZh());
//                    }else if ("100119".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100119.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100119.getMessageZh());
//                    } else {
//                        r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getMessageZh());
//                    }
//                }

				//                private void setEnMsg(String errorDescription, R<Object> r) {
//                    if ("用户名或密码错误".equalsIgnoreCase(errorDescription) || "Bad credentials".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100013.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100013.getMessage());
//                    } else if ("Token authorization has expired".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100014.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100014.getMessage());
//                    } else if ("User's tenant information not obtained".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100015.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100015.getMessage());
//                    } else if ("Too many login errors, please try again later".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100016.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100016.getMessage());
//                    } else if ("100020".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100020.getMessage());
//                    } else if ("100113".equalsIgnoreCase(errorDescription)|| "The user has not registered, please register before login.".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100113.getMessage());
//                    } else if ("100006".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getMessage());
//                    } else if ("100103".equalsIgnoreCase(errorDescription)){
//                        r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getMessage());
//                    } else if ("Wrong Verification Code".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getMessage());
//                    }else if ("100119".equalsIgnoreCase(errorDescription)) {
//                        r.setCode(I18nMsgCode.SKYWORTH_CLIENT_USER_100119.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_USER_100119.getMessage());
//                    } else {
//                        r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
//                        r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getMessage());
//                    }
//                }
				@Override
				public Mono<Void> writeAndFlushWith(Publisher<? extends Publisher<? extends DataBuffer>> body) {
					return writeWith(Flux.from(body).flatMapSequential(p -> p));
				}

				@Override
				public boolean setStatusCode(@Nullable HttpStatus status) {
					return this.getDelegate().setStatusCode(HttpStatus.OK);
				}
			};

			return chain.filter(exchange.mutate().response(response).build());
		} else {
			return chain.filter(exchange);
		}


	}

	@Override
	public int getOrder() {
		//WRITE_RESPONSE_FILTER 之前执行
		return NettyWriteResponseFilter.WRITE_RESPONSE_FILTER_ORDER - 1;
	}

}
