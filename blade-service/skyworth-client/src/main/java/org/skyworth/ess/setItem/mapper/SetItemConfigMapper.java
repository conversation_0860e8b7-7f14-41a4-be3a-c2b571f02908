/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.mapper;

import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.vo.SetItemConfigVO;
import org.skyworth.ess.setItem.excel.SetItemConfigExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * APP设置项配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public interface SetItemConfigMapper extends BaseMapper<SetItemConfigEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param setItemConfig
	 * @return
	 */
	List<SetItemConfigVO> selectSetItemConfigPage(IPage page, SetItemConfigVO setItemConfig);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SetItemConfigExcel> exportSetItemConfig(@Param("ew") Wrapper<SetItemConfigEntity> queryWrapper);

    List<SetItemConfigEntity> getSetupConfig(@Param("appSetRequestVO") AppSetRequestVO appSetRequestVO);
}
