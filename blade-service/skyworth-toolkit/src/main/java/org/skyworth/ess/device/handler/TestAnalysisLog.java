package org.skyworth.ess.device.handler;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.device.BinaryConvertUtils2;
import org.skyworth.ess.timeUtil.TimeZoneInverter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;

//@Component
//@AllArgsConstructor
public class TestAnalysisLog {//implements InitializingBean {

//	@Override
	public static void main(String[] args) throws Exception {
		String str = "{\n" +
			"        \"plantID\":      \"1950864652854366209\",\n" +
			"        \"company\":      \"snj\",\n" +
			"        \"commuVersion\": \"0.0.1\",\n" +
			"        \"deviceSn\":     \"CVGJSTSJA00000164\",\n" +
			"        \"timeStamp\":    1754041724,\n" +
			"        \"type\": 2,\n" +
			"        \"content\":      \"1001004c0936009bffffff60138c094800a8fffffd77138c09360098ffffffc1138c0000000000000000000000000000000000000000000000000027000300000000000000000000000001cb00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008203c600000000000000000000000000000000000000000000000011630000000013a0004800000000000002f80000000000000000000000000000000000000000000000000000000000002ac7000000000000000000000000000000000000000000000000000000000000159b000000000000000000000000ffffffff00000096ffffffff000000000000000000008f43000049b20000000000000000000000000000000000000000000092be000000000000ac0b13000064000000000000017cfffffe840000527000002d420000000000000000000000000000182800000000000000000000000000000000094a094a0941000000000000000ffffffff0094a094a0941000000000000000000000000000000000000000000000000000000000000000000000000138c138c0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008a3136a00140000000000000000000000000000000000000000000000000000000000000000000000000000000015000016000000000000000000000000000000000000000000000000000000000000138c0000000000000024000000002000001f004a000014c000000000000007d300000000000000000000000000004d7600000000000068ca0000000000000064014d001c014c00210019002400010021209a00010001\"\n" +
			"}\n";
		JSONObject data = JSONObject.parseObject(str);
		long plantId = Long.parseLong(data.getString("plantID"));
		String modbusProtocolVersion = data.getString("commuVersion");
		String deviceSerialNumber = data.getString("deviceSn");
		String content = data.getString("content");
		Date deviceDateTime = TimeZoneInverter.timeInverterToDate("UTC-0", data.getLong("timeStamp") * 1000);
		//用于方便排查数据解析是否有问题
		Connection conn = null;
		Statement stmt = null;
		ResultSet rs = null;
		try {
			stmt = connectToDatabase(conn, stmt);
			TestBinaryConvertUtils2 testBinaryConvertUtils2 = new TestBinaryConvertUtils2();
			JSONObject result = testBinaryConvertUtils2.getData(content, "3", modbusProtocolVersion, stmt, rs);
			System.out.println("===解析后的内容===" + result);
//			System.out.println(JSONObject.toJSONString(result));
		} finally {
			closeResource(rs, stmt, conn);
		}
	}


		private static Statement connectToDatabase(Connection conn, Statement stmt) throws ClassNotFoundException, SQLException {
				// 1. 加载MySQL JDBC驱动
				Class.forName("com.mysql.cj.jdbc.Driver");
				// 2. 建立数据库连接（替换为你的数据库信息）
				String url = "**************************************************************************************************************************************************************************************************************************************";
				String user = "root";
				String password = "skyworth@123";
				conn = DriverManager.getConnection(url, user, password);
				// 3. 创建Statement对象
				return conn.createStatement();

        }

	private static void closeResource(ResultSet rs, Statement stmt, Connection conn) {
		try {
			if (rs != null) {
				rs.close();}
			if (stmt != null) {
				stmt.close();}
			if (conn != null) {
				conn.close();}
		} catch (SQLException e) {
			System.err.println("关闭资源时出错: " + e.getMessage());
		}
	}
	}
