/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.sku.entity.SkuHistoryEntity;
import org.skyworth.ess.sku.vo.SkuHistoryVO;
import org.skyworth.ess.sku.excel.SkuHistoryExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.List;
import java.util.Set;

/**
 * 物料记录表，每次新增写入一条 服务类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public interface ISkuHistoryService extends BaseService<SkuHistoryEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param skuHistory
	 * @return
	 */
	IPage<SkuHistoryVO> selectSkuHistoryPage(IPage<SkuHistoryVO> page, SkuHistoryVO skuHistory);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SkuHistoryExcel> exportSkuHistory(Wrapper<SkuHistoryEntity> queryWrapper);

	R<String> saveSkuHistory(SkuHistoryEntity skuHistoryEntity);

	/**
	 * 获取 sku对应的剩余库存
 	 * @param skuList sku集合
	 * @return sku对应的剩余库存
	 */
	List<SkuHistoryVO> getSurplusQuantityBySku(Set<String> skuList);
}
