package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.service.IAddressMapDefinitionService;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.service.IDevice24Service;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.entity.SetItemEntity;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.Dept;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 高级设置子类
 * @create-time 2023/12/1 10:20:04
 */
@Slf4j
public class AppSetupTemplateAdvanced extends AppSetupTemplate {


	private IDevice24Service device24Service = SpringUtil.getBean(IDevice24Service.class);

	private IDevice23Service device23Service = SpringUtil.getBean(IDevice23Service.class);

	private IDevice21Service device21Service = SpringUtil.getBean(IDevice21Service.class);

	private IDeviceIssueBiz deviceIssueBiz = SpringUtil.getBean(org.skyworth.ess.device.client.IDeviceIssueBiz.class);

	private ISetItemConfigService setItemConfigService = SpringUtil.getBean(ISetItemConfigService.class);

	public IAddressMapDefinitionService addressMapDefinitionService = SpringUtil.getBean(IAddressMapDefinitionService.class);

	@Override
	public Map<String, Object> getSetupDataByDb(Long plantId, String deviceSerialNumber) {
		return device24Service.getAllAdvancedSetup(plantId, deviceSerialNumber);
	}

//	@Override
//	public SetItemEntity getSetupConfig(AppSetRequestVO appSetRequestVO) {
//		//查询设置项
//		List<SetItemConfigEntity> setItemConfig = setItemConfigService.getSetupConfig(appSetRequestVO);
//		SetItemEntity setItemEntity = new SetItemEntity();
//		setItemEntity.setSetItemConfigEntityList(setItemConfig);
//		return setItemEntity;
//	}

	@Override
	public void completeConfigItemBySubTemplate(Map<String, Object> setupDataByDb, SetItemEntity setupItemConfig, JSONObject dataObject, AppSetRequestVO appSetRequestVO) {
		if (ObjectUtil.isNotNull(setupDataByDb) && !setupDataByDb.isEmpty()) {
			StringBuilder stringBuilder = new StringBuilder();
			String year = (String) setupDataByDb.get("year");
			String monthDay = (String) setupDataByDb.get("monthDay");
			String hoursMin = (String) setupDataByDb.get("hoursMin");
			String seconds = (String) setupDataByDb.get("seconds");
			StringBuilder append = stringBuilder.append(year).append("-").append(monthDay).append(" ").append(hoursMin).append(":").append(seconds);
			setupDataByDb.put(DATETIME, append.toString());
			R<String> r = super.determineDeviceStatus(appSetRequestVO.getPlantId());
			// 设备状态正常才去下发设备获取时间
			if (r == null) {
				// 最新的时间数据，下发请求从设备端获取；取不到则取数据库的旧数据
				JSONObject issueObj = new JSONObject();
				String requestId = TimeUtils.generateRequestId();
				issueObj.put("deviceSn", appSetRequestVO.getDeviceSerialNumber());
				issueObj.put("requestId", requestId);
				issueObj.put("topic", Constants.DEVICE_GET_TIME);
				Map<String, String> deviceTime = deviceIssueBiz.getDeviceTime(issueObj);
				if (ObjectUtil.isNotNull(deviceTime) && !deviceTime.isEmpty() && deviceTime.containsKey("200")) {
					setupDataByDb.put(DATETIME, deviceTime.get("200"));
				}
			}

			for (Map.Entry<String, Object> entry : setupDataByDb.entrySet()) {

				super.buildSetupItemInfo(entry, setupItemConfig, dataObject);
			}
			// 设置输入框小数位数
			this.setInputUnit(setupItemConfig, dataObject);
			super.buildTimeZoneInfo(dataObject,appSetRequestVO);
		}
	}

	private void setInputUnit(SetItemEntity setupItemConfig, JSONObject dataObject) {
		List<String> protocolAddressList = setupItemConfig.getSetItemConfigEntityList().stream().map(SetItemConfigEntity::getSetItemProtocolAddress)
			.filter(setItemProtocolAddress -> !StringUtils.isEmpty(setItemProtocolAddress)).collect(Collectors.toList());
		List<AddressMapDefinitionEntity> definitionEntityList = new ArrayList<>();
		if(CollectionUtil.isNotEmpty(protocolAddressList)) {
			definitionEntityList = addressMapDefinitionService.list(Wrappers.<AddressMapDefinitionEntity>lambdaQuery()
				.in(AddressMapDefinitionEntity::getAddress, protocolAddressList));
			for(AddressMapDefinitionEntity definitionEntity : definitionEntityList) {
				String setItemDefinition = definitionEntity.getSetItemDefinition();
				if(StringUtils.isEmpty(setItemDefinition) || "ASCII".equalsIgnoreCase(definitionEntity.getUnit())) {
					continue;
				}
				JSONObject singleItemConfig = (JSONObject) dataObject.get(setItemDefinition);
				if(ObjectUtil.isNotEmpty(singleItemConfig)) {
					singleItemConfig.put("unit", definitionEntity.getUnit());
				}
			}
		}
	}

	// ==================================================================================
	@Override
	public R<String> issueSetupToToolKit(AppAdvancedSetup deviceAdvancedSetup) {
		R<String> result = device24Service.issueAdvancedSetup(deviceAdvancedSetup);
		log.info("set Advanced Setup : {}", result);
		return result;
	}

	@Override
	public R<?> getReturnByMqttResult(AppAdvancedSetup deviceAdvancedSetup, R<String> result, Table<String, String, Object> table, List<AppAdvancedSetup.SetupItem> items) {
		if (result.getCode() == 200) {
			// 设置成功以后要对device21\23\24表进行修改，记录状态
			Map<String, Object> device24 = table.row("device_24");
			if (ObjectUtil.isNotEmpty(device24) && !device24.isEmpty()) {
				device24Service.updateSetup(device24, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}
			Map<String, Object> device23 = table.row("device_23");
			if (ObjectUtil.isNotEmpty(device23) && !device23.isEmpty()) {
				device23Service.updateSetup(device23, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}
			Map<String, Object> device21 = table.row("device_21");
			if (ObjectUtil.isNotEmpty(device21) && !device21.isEmpty()) {
				device21Service.updateSetup(device21, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}


			String currentLanguage = CommonUtil.getCurrentLanguage();
			return R.success(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100123.autoGetMessage(currentLanguage));
		} else if (result.getCode() == 100027) {
			// 把未设置成功设置项返回前端
			List<AppAdvancedSetup.SetupItem> errorItems = new ArrayList<>();
			String msg = result.getMsg();
			String[] arr = msg.split(",");
			HashSet<String> set = new HashSet<>(Arrays.asList(arr));
			String errorDateTime = "";
			for (AppAdvancedSetup.SetupItem item : items) {
				if (set.contains(item.getAddress().toString())) {
					errorItems.add(item);
				}
				if (item.getDefinition().equals(DATETIME)) {
					errorDateTime = (String) item.getData();
				}
			}
			if (set.contains(12288 + "")) {
				AppAdvancedSetup.SetupItem setupItem = new AppAdvancedSetup.SetupItem();
				setupItem.setDefinition(DATETIME);
				setupItem.setData(errorDateTime);
				errorItems.add(setupItem);
			}
			return R.data(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100027.getCode(), errorItems, I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100027.getMessage());
		}
		return result;
	}
}
