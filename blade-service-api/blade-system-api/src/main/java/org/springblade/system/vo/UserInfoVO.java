package org.springblade.system.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "UserInfoVO", description = "UserInfoVO")
public class UserInfoVO  implements Serializable {
	private static final long serialVersionUID = 1L;
	private String userAccount;
	private String phone;
	private String newPassword;
	private String oldPassword;
	private String confirmPassword;
	/**
	 * 类型 0：邮箱，1：手机
	 */
	private String type;
	/**
	 * 验证码
	 */
	private String verificationCode;
	/**
	 * 获取验证码返回的id
	 */
	private String smsId;
	/**
	 * 区号
	 */
	private String phoneDiallingCode;
	/**
	 * 租户
	 */
	private String tenantId;
	/**
	 * app类型
	 */
	private String appType;
	// 角色id
	private Long roleId;
	// 角色
	private String roleCode;
	// 代理商编码
	private String agentNumber;

}
