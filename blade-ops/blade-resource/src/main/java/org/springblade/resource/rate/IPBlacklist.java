package org.springblade.resource.rate;

import cn.hutool.extra.spring.SpringUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.redis.cache.BladeRedis;

import java.time.Duration;

/**
 * <AUTHOR>
 */
public class IPBlacklist {
	private final BladeRedis bladeRedis = SpringUtil.getBean(BladeRedis.class);


	public void addToBlacklist(String ip) {
		// ip黑名单一个小时自动解锁
		bladeRedis.setEx(CommonConstant.RESOURCE_BLACKLIST_PREFIX + ip, ip, Duration.ofMinutes(60));
	}

	public boolean isBlacklisted(String ip) {
		Object object = bladeRedis.get(CommonConstant.RESOURCE_BLACKLIST_PREFIX + ip);
		if (object == null) {
			return false;
		}
		return true;
	}
}
