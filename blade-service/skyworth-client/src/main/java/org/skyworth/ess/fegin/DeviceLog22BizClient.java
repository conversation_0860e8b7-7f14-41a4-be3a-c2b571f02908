
package org.skyworth.ess.fegin;


import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.client.IDeviceLog22Client;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.skyworth.ess.device.mapper.DeviceLog22Mapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * deviceLog22 Feign实现类
 *
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@Slf4j
public class DeviceLog22BizClient extends BaseServiceImpl<DeviceLog22Mapper, DeviceLog22> implements IDeviceLog22Client {


	@PostMapping(device22)
	@Override
	public List<DeviceLog22> queryDeviceLog22list(List<DeviceLog22> deviceLog22List) {
		return baseMapper.queryDeviceLog22list(deviceLog22List);
	}


}
