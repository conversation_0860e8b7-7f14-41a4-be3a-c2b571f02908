package org.skyworth.ess.mqtt;

import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Component
public class MqttClientFactory {

    private List<MqttClientDef> clientList = new ArrayList<>();

    private static Random random = new SecureRandom();
    /**
     * 返回客户端
     * @return
     */
    public MqttClientDef getClient() {
        int rd = random.nextInt(clientList.size());
        return this.clientList.get(rd);
    }

    /**
     * 设置客户端
     * @param mqttClient
     */
    public void addClient(MqttClientDef mqttClient) {
        this.clientList.add(mqttClient);
    }

}
