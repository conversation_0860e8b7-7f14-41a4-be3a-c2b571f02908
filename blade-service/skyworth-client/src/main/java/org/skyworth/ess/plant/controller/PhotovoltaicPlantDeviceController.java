/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.plant.entity.PhotovoltaicPlantDeviceEntity;
import org.skyworth.ess.plant.vo.PhotovoltaicPlantDeviceVO;
import org.skyworth.ess.plant.excel.PhotovoltaicPlantDeviceExcel;
import org.skyworth.ess.plant.wrapper.PhotovoltaicPlantDeviceWrapper;
import org.skyworth.ess.plant.service.IPhotovoltaicPlantDeviceService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 光伏板站点设备/逆变器表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("skyworth-PhotovoltaicPlantDevice/PhotovoltaicPlantDevice")
@Api(value = "光伏板站点设备/逆变器表", tags = "光伏板站点设备/逆变器表接口")
public class PhotovoltaicPlantDeviceController extends BladeController {

	private final IPhotovoltaicPlantDeviceService PhotovoltaicPlantDeviceService;

	/**
	 * 光伏板站点设备/逆变器表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入PhotovoltaicPlantDevice")
	public R<PhotovoltaicPlantDeviceVO> detail(PhotovoltaicPlantDeviceEntity PhotovoltaicPlantDevice) {
		PhotovoltaicPlantDeviceEntity detail = PhotovoltaicPlantDeviceService.getOne(Condition.getQueryWrapper(PhotovoltaicPlantDevice));
		return R.data(PhotovoltaicPlantDeviceWrapper.build().entityVO(detail));
	}
	/**
	 * 光伏板站点设备/逆变器表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入PhotovoltaicPlantDevice")
	public R<IPage<PhotovoltaicPlantDeviceVO>> list(@ApiIgnore @RequestParam Map<String, Object> PhotovoltaicPlantDevice, Query query) {
		IPage<PhotovoltaicPlantDeviceEntity> pages = PhotovoltaicPlantDeviceService.page(Condition.getPage(query), Condition.getQueryWrapper(PhotovoltaicPlantDevice, PhotovoltaicPlantDeviceEntity.class));
		return R.data(PhotovoltaicPlantDeviceWrapper.build().pageVO(pages));
	}

	/**
	 * 光伏板站点设备/逆变器表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入PhotovoltaicPlantDevice")
	public R<IPage<PhotovoltaicPlantDeviceVO>> page(PhotovoltaicPlantDeviceVO PhotovoltaicPlantDevice, Query query) {
		IPage<PhotovoltaicPlantDeviceVO> pages = PhotovoltaicPlantDeviceService.selectPhotovoltaicPlantDevicePage(Condition.getPage(query), PhotovoltaicPlantDevice);
		return R.data(pages);
	}

	/**
	 * 光伏板站点设备/逆变器表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入PhotovoltaicPlantDevice")
	public R save(@Valid @RequestBody PhotovoltaicPlantDeviceEntity PhotovoltaicPlantDevice) {
		return R.status(PhotovoltaicPlantDeviceService.save(PhotovoltaicPlantDevice));
	}

	/**
	 * 光伏板站点设备/逆变器表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入PhotovoltaicPlantDevice")
	public R update(@Valid @RequestBody PhotovoltaicPlantDeviceEntity PhotovoltaicPlantDevice) {
		return R.status(PhotovoltaicPlantDeviceService.updateById(PhotovoltaicPlantDevice));
	}

	/**
	 * 光伏板站点设备/逆变器表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入PhotovoltaicPlantDevice")
	public R submit(@Valid @RequestBody PhotovoltaicPlantDeviceEntity PhotovoltaicPlantDevice) {
		return R.status(PhotovoltaicPlantDeviceService.saveOrUpdate(PhotovoltaicPlantDevice));
	}

	/**
	 * 光伏板站点设备/逆变器表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(PhotovoltaicPlantDeviceService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-PhotovoltaicPlantDevice")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入PhotovoltaicPlantDevice")
	public void exportPhotovoltaicPlantDevice(@ApiIgnore @RequestParam Map<String, Object> PhotovoltaicPlantDevice, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<PhotovoltaicPlantDeviceEntity> queryWrapper = Condition.getQueryWrapper(PhotovoltaicPlantDevice, PhotovoltaicPlantDeviceEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(PhotovoltaicPlantDevice::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(PhotovoltaicPlantDeviceEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<PhotovoltaicPlantDeviceExcel> list = PhotovoltaicPlantDeviceService.exportPhotovoltaicPlantDevice(queryWrapper);
		ExcelUtil.export(response, "光伏板站点设备/逆变器表数据" + DateUtil.time(), "光伏板站点设备/逆变器表数据表", list, PhotovoltaicPlantDeviceExcel.class);
	}

}
