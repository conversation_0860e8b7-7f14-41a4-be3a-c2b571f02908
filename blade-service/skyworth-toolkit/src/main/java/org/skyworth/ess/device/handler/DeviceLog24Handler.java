package org.skyworth.ess.device.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.BinaryConvertUtils2;
import org.skyworth.ess.device.DeviceLogService;
import org.skyworth.ess.device.client.IDeviceLog24Client;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.DeviceLog24Entity;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.skyworth.ess.log.service.OriginalDataRecordService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 处理所上报2.4的数据
 * */
@Component("DeviceLog2_" + Constants.FOUR)
@Slf4j
@AllArgsConstructor
public class Device<PERSON>og24<PERSON>andler implements DeviceLogService {

	private BinaryConvertUtils2 binaryConvertUtils;

	private OriginalDataRecordService recordService;

	private IDeviceLog24Client iDeviceLog24Client;


	@Override
	public void handler(List<JSONObject> dataList) {
			List<DeviceLog24Entity> deviceLog24EntityList=new ArrayList<>();
			dataList.stream().parallel().forEach(data->{
				try {
					long plantId = Long.parseLong(data.getString("plantID"));
					String modbusProtocolVersion = data.getString("commuVersion");
					String deviceSerialNumber = data.getString("deviceSn");
					String content = data.getString("content");
					Long timeStamp = data.getLong("timeStamp");
					String timeZone = data.getString("timeZone");
					Date deviceDateTime = DateUtil.convertLongTimeZoneForDeviceUpload(timeStamp,timeZone,CommonConstant.COMMON_DEFAULT_TIME_ZONE);
					JSONObject result = binaryConvertUtils.getData(content, Constants.FOUR, modbusProtocolVersion);
					log.info("binaryConvertUtils data : {} ",result);
					if (result != null) {
						log.info("DeviceLog24Handler getData is not null ");
						DeviceLog24Entity deviceLog24 = JSON.toJavaObject(result, DeviceLog24Entity.class);
						deviceLog24.setPlantId(plantId);
						deviceLog24.setDeviceDateTime(deviceDateTime);
						deviceLog24.setModbusProtocolVersion(modbusProtocolVersion);
						deviceLog24.setDeviceSerialNumber(deviceSerialNumber);
						Date currentTime = new Date();
						deviceLog24.setCreateTime(currentTime);
						deviceLog24.setUpdateTime(currentTime);
						deviceLog24.setIsDeleted(0);
						deviceLog24.setTenantId(CommonConstant.CLIENT_TENANT_ID);
						deviceLog24.setSynchStatus("Y");
						deviceLog24EntityList.add(deviceLog24);
					}
				}catch (Exception e){
					log.error("DeviceLog24Handler error ",e);
					//将数据插入记录表中  保留异常信息
					OriginalDataRecord record=new OriginalDataRecord();
					record.setData(data.toJSONString());
					record.setType("1");
					record.setExceptionInfo(e.getMessage());
					recordService.save(record);
				}
			});
			try{
				log.info("iDeviceLog24Client deviceLog24EntityList : {} ",deviceLog24EntityList);
				if(!deviceLog24EntityList.isEmpty()){
					iDeviceLog24Client.insertBatchDeviceLog24Column(deviceLog24EntityList);
				}
			}catch (Exception e){
				throw new RuntimeException("deviceLog24Handler is exception");
			}



	}


}
