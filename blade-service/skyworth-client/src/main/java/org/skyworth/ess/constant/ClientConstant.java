/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.constant;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-10-07 10:31
 **/
public interface ClientConstant {
	String OTA_DEVICE_TYPE = "ota_device_type";
	String OTA_COMPANY = "device_company";
	String BIG_TYPE_INVERTER = "inverter";
	String BIG_TYPE_WIFIBOM = "wifibom";
	String BIG_TYPE_BATTERY = "battery";
	// app上传图片备注定义管理员
	String APP_REMARK_ROLE_NAME_ADMIN = "admin";
	// app上传图片备注定义普通人员
	String APP_REMARK_ROLE_NAME_ORDINARY = "ordinary";
}
