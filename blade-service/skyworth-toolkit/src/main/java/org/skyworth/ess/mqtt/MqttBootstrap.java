package org.skyworth.ess.mqtt;

import lombok.AllArgsConstructor;
import org.skyworth.ess.mqtt.dataProcess.MqttCallBackProcess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.eventCode.IotApplicationEvent;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MqttBootstrap implements IotApplicationEvent, Ordered {

	private static final Logger logger = LoggerFactory.getLogger(MqttBootstrap.class);

    private MqttConfig mqttConfig;

	private MqttClientFactory mqttClientFactory;

	private MqttCallBackProcess mqttCallBackProcess;

	private void start() {
		try {
			for(int i = 0; i < 1; i++) {
				MqttClientDef subClient = new MqttClientDef(mqttConfig,mqttCallBackProcess);
				subClient.init();
				mqttClientFactory.addClient(subClient);
			}
		} catch (Exception e) {
			logger.error("MQTT bootstrap start error: {}", e.getMessage());
		}
	}


	@Override
	public int getOrder() {
		return IotApplicationEvent.super.getOrder();
	}

	@Override
	public void onApplicationEvent() {
		start();
	}
}
