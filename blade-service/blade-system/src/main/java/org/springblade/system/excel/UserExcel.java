/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * UserExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class UserExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ColumnWidth(15)
	@ExcelProperty("Tenant number")
	private String tenantId;

	@ExcelIgnore
	@ExcelProperty("userType")
	private String userType;

	@ColumnWidth(20)
	@ExcelProperty("userTypeName")
	private String userTypeName;

	@ColumnWidth(15)
	@ExcelProperty("account")
	private String account;

	@ColumnWidth(10)
	@ExcelProperty("name")
	private String name;

	@ColumnWidth(10)
	@ExcelProperty("realName")
	private String realName;

	@ExcelProperty("email")
	private String email;

	@ColumnWidth(15)
	@ExcelProperty("phone")
	private String phone;

	@ExcelIgnore
	@ExcelProperty("roleId")
	private String roleId;

	@ExcelIgnore
	@ExcelProperty("deptId")
	private String deptId;

	@ExcelIgnore
	@ExcelProperty("postId")
	private String postId;

	@ExcelProperty("roleName")
	private String roleName;

	@ExcelProperty("deptName")
	private String deptName;

	@ExcelProperty("postName")
	private String postName;

	@ColumnWidth(20)
	@ExcelProperty("birthday")
	private Date birthday;

}
