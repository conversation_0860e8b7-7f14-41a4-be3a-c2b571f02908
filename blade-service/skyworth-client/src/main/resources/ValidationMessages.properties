client.battery.query.connot.empty=查询条件不能为空
client.setItem.delete.fail=先删除子配置项节点
client.system.error=系统正在开小差，请稍后再试
client.email.address.error=邮箱地址不正确
client.parameter.error.empty=参数异常:参数不能为空
client.parameter.error.record.duplicate=有重复的记录，请确认
client.parameter.error.record.empty=请选择一条记录
client.invert.serial.number.exist=逆变器序列号 : %s 已存在
client.plant.name.exist=站点名称:[%s]已存在
client.ota.record.count.error=所选记录总数和服务器总数不一致
client.plant.change.user.phone.empty=手机验证码为空
client.plant.change.user.phone.error=输入手机验证码有误
client.plant.change.user.email.empty=邮箱验证码为空
client.plant.change.user.email.error=输入邮箱验证码有误
client.plant.change.user.phone.email.empty=手机邮箱不能全为空
client.plant.change.user.check=当前用户密码错误
client.plant.change.user.phone.same=如果您不想更改账户绑定的手机号码，则无法继续操作
client.plant.change.user.email.same=如果您不想更改账户绑定的邮箱号码，则无法继续操作
client.ota.record.upgrade.one=同一个sn一次只能更新一种类型的固件，异常勾选的sn如下:%s
client.email.code.exist=邮箱验证码已发送，请检查您的收件箱，如果没有收到请查看垃圾箱或者检查是否被拦截
client.please.register.account.first=请先注册账号。
client.user.email.is.exits=该邮箱已经被注册
client.inverter.sn.exist=逆变器SN被使用，不能删除
client.battery.serial.number.exist=电池序列号 : %s 已存在
client.battery.exit.add.sn.exist=该电池SN在出厂信息中已存在
client.ota.record.upgrading=如下设备存在升级中的固件，请检查:%s
client.ota.record.already.latest=选择升级的固件已经是最新版本，请检查或刷新页面:%s
client.parameter.error.invalidDateFormat=时间格式不正确
client.battery.sn.is.used=电池SN被使用，不能删除
client.inverter.max.parallel.limit=超出该型号逆变器并机数量限制。
client.inverter.bind.same.parallel=请添加一个与之前逆变器规格相同的逆变器。
client.inverter.latest.unbind.limit=站点只有一台逆变器时，无法解除绑定
client.inverter.unable.unbind.limit=该逆变器无法解除绑定
client.inverter.sn.not.exists=如下逆变器序列号不存在：%s，请检查。
client.inverter.sn.has.used=如下逆变器已经被使用：%s，不能删除。
client.battery.sn.not.exists=如下电池序列号不存在：%s，请检查。
client.battery.sn.has.used=如下电池已经被使用：%s，不能删除。
client.inverter.sn.empty=逆变器序列号不能为空。
client.battery.sn.empty=电池序列号不能为空。
client.import.record.not.empty=导入记录不能为空。
