system.agent.agentNumber.can.not.empty=\u4EE3\u7406\u5546\u4EA4\u4ED8\u7ECF\u7406\u7684\u4EE3\u7406\u5546\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
system.agent.agentNumber.is.not.exits=\u4EE3\u7406\u5546\u7F16\u53F7\u4E0D\u5B58\u5728
system.error=\u7CFB\u7EDF\u9519\u8BEF
system.user.account.is.exits=\u7528\u6237\u8D26\u53F7\u5DF2\u5B58\u5728
system.user.phone.is.exits=\u624B\u673A\u53F7\u5DF2\u5B58\u5728
system.user.phone.is.exits.to.another.tenant=\u6B64\u53F7\u7801\u5DF2\u7ED1\u5B9A\u5230\u53E6\u4E00\u4E2A\u5E10\u6237\uFF0C\u8BF7\u8054\u7CFB\u7CFB\u7EDF\u7BA1\u7406\u5458\u3002
system.user.the.verification.code.is.error=\u9A8C\u8BC1\u7801\u9519\u8BEF
system.user.the.is.not.exist=\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25
system.user.email.is.exits=\u8BE5\u90AE\u7BB1\u5DF2\u7ECF\u88AB\u6CE8\u518C
system.dict.key.value.exist=\u5F53\u524D\u7684\u5B57\u5178\u952E\u503C\u5DF2\u7ECF\u5B58\u5728!
system.exception.delete.child.nodes=\u8BF7\u5148\u5220\u9664\u5B50\u8282\u70B9
system.exception.delete.child.nodes.interface=\u8BF7\u5148\u5220\u9664\u5F53\u524D\u83DC\u5355\u4E0B\u7684\u63A5\u53E3\u6743\u9650!
system.exception.not.resubmit=\u8BF7\u52FF\u91CD\u590D\u63D0\u4EA4
system.attach.info.empty=\u9644\u4EF6\u4FE1\u606F\u4E3A\u7A7A
system.exception.code.execution=\u4E1A\u52A1\u4EE3\u7801\u6267\u884C\u5F02\u5E38
system.exception.cannot.select.parent.itself=\u7236\u8282\u70B9\u4E0D\u80FD\u9009\u62E9\u81EA\u5DF1
system.exception.cannot.config.superadmin.role=\u65E0\u6743\u9650\u914D\u7F6E\u8D85\u7EA7\u7BA1\u7406\u5458\u89D2\u8272!
system.exception.cannot.config.admin.role=\u65E0\u6743\u9650\u914D\u7F6E\u7BA1\u7406\u5458\u89D2\u8272!
system.exception.cannot.create.superadmin.role=\u65E0\u6743\u9650\u521B\u5EFA\u8D85\u7EA7\u7BA1\u7406\u5458\u89D2\u8272!
system.exception.cannot.delete.super.tenant=\u8D85\u7EA7\u7BA1\u7406\u5458\u79DF\u6237\u4E0D\u80FD\u88AB\u5220\u9664!
system.exception.menu.name.number.exist=\u83DC\u5355\u540D\u79F0\u6216\u7F16\u53F7\u5DF2\u7ECF\u5B58\u5728!
system.exception.menu.parent.only.menu.type=\u7236\u8282\u70B9\u53EA\u80FD\u9009\u62E9\u83DC\u5355\u7C7B\u578B!
system.user.password.cannot.empty=\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A
system.user.tenant.quota.max=\u5F53\u524D\u79DF\u6237\u5DF2\u8FBE\u5230\u6700\u5927\u5E10\u6237\u914D\u989D
system.user.enter.confirm.password=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u786E\u8BA4\u5BC6\u7801!
system.user.original.password.incorrect=\u539F\u5BC6\u7801\u9519\u8BEF!
system.user.tenant.info.error=\u79DF\u6237\u4FE1\u606F\u9519\u8BEF
system.user.third.login.error=\u7B2C\u4E09\u65B9\u767B\u9646\u5931\u8D25
system.user.account.connot.delete=\u4E0D\u80FD\u5220\u9664\u672C\u8D26\u53F7!
system.user.account.abnormal=\u5F53\u524D\u7528\u6237[{%s}]\u8D26\u53F7\u5F02\u5E38!
system.user.account.not.exist=\u5F53\u524D\u7528\u6237[{%s}]\u4E0D\u5B58\u5728!
system.user.account.is.exist=\u5F53\u524D\u7528\u6237[{%s}]\u5DF2\u5B58\u5728!
