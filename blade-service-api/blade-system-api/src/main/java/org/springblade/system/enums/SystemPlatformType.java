package org.springblade.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName SystemPlatformType
 * @Description 系统平台类型枚举
 * @Date 2024/6/6 下午2:48
 */
@Getter
@AllArgsConstructor
public enum SystemPlatformType {

    /**
     * 系统平台类型枚举
     */
    WEB("web", "web端"),
    APP("app", "app端"),
    WEB_AND_APP("web_app", "web和app端"),
    ;

    final String type;
    final String remarks;

    // 匹配枚举值
    public static SystemPlatformType of(String type) {
        if (type == null) {
            return null;
        }
        SystemPlatformType[] values = SystemPlatformType.values();
        for (SystemPlatformType platformType : values) {
            if (platformType.type.equals(type)) {
                return platformType;
            }
        }
        return null;
    }

}
