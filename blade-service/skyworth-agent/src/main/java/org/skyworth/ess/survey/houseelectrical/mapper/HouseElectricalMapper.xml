<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.survey.houseelectrical.mapper.HouseElectricalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="houseElectricalResultMap" type="org.skyworth.ess.survey.houseelectrical.entity.HouseElectricalEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="infrastructure" property="infrastructure"/>
        <result column="infrastructure_connect_type" property="infrastructureConnectType"/>
        <result column="infrastructure_connect_type_other" property="infrastructureConnectTypeOther"/>
        <result column="infrastructure_img_biz_key" property="infrastructureImgBizKey"/>
        <result column="backup_generator" property="backupGenerator"/>
        <result column="backup_generator_img_biz_key" property="backupGeneratorImgBizKey"/>
        <result column="backup_power_source" property="backupPowerSource"/>
        <result column="backup_power_source_img_biz_key" property="backupPowerSourceImgBizKey"/>
        <result column="electrical_condition" property="electricalCondition"/>
        <result column="electrical_condition_img_biz_key" property="electricalConditionImgBizKey"/>
        <result column="routing_confirmation" property="routingConfirmation"/>
        <result column="routing_confirmation_img_biz_key" property="routingConfirmationImgBizKey"/>
        <result column="routing_draft" property="routingDraft"/>
        <result column="routing_draft_img_biz_key" property="routingDraftImgBizKey"/>
        <result column="inspect_wiring" property="inspectWiring"/>
        <result column="inspect_wiring_img_biz_key" property="inspectWiringImgBizKey"/>
        <result column="upgrade_required" property="upgradeRequired"/>
        <result column="upgrade_required_img_biz_key" property="upgradeRequiredImgBizKey"/>
        <result column="power_monthly_img_biz_key" property="powerMonthlyImgBizKey"/>
        <result column="power_annual_img_biz_key" property="powerAnnualImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectHouseElectricalPage" resultMap="houseElectricalResultMap">
        select * from survey_house_electrical where is_deleted = 0
    </select>


    <select id="exportHouseElectrical" resultType="org.skyworth.ess.survey.houseelectrical.excel.HouseElectricalExcel">
        SELECT * FROM survey_house_electrical ${ew.customSqlSegment}
    </select>

</mapper>
