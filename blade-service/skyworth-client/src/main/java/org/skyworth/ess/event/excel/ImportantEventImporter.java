package org.skyworth.ess.event.excel;

import lombok.RequiredArgsConstructor;
import org.skyworth.ess.event.service.IImportantEventService;
import org.springblade.core.excel.support.ExcelImporter;

import java.util.List;

@RequiredArgsConstructor
public class ImportantEventImporter implements ExcelImporter<ImportantEventExcel> {
    private final IImportantEventService importantEventService;
    private final Boolean isCovered;
    @Override
    public void save(List<ImportantEventExcel> data) {
        importantEventService.importExcel(data,isCovered);
    }
}
