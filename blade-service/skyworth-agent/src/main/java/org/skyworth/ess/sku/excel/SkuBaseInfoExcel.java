/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

import java.io.Serializable;


/**
 * 物料基础信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SkuBaseInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * sku编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("sku编码")
	private String skuCode;
	/**
	 * 设备类型（取数据字典sku_device_type）
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备类型（取数据字典sku_device_type）")
	private String skuDeviceType;
	/**
	 * 物料名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("物料名称")
	private String skuName;
	/**
	 * 物料厂商（sku_company）
	 */
	@ColumnWidth(20)
	@ExcelProperty("物料厂商（sku_company）")
	private String skuCompany;
	/**
	 * 规格
	 */
	@ColumnWidth(20)
	@ExcelProperty("规格")
	private String standards;
	/**
	 * 型号
	 */
	@ColumnWidth(20)
	@ExcelProperty("型号")
	private String model;
	/**
	 * 单位
	 */
	@ColumnWidth(20)
	@ExcelProperty("单位")
	private String unit;
	
	@ColumnWidth(20)
	@ExcelProperty("价格")
	private BigDecimal price;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}
