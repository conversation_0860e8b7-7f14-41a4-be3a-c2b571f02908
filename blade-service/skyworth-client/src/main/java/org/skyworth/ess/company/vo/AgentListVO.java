/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理商公司信息表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentListVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 注册时间
	 */
	@ApiModelProperty(value = "注册时间")
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(pattern = DateUtil.PATTERN_DATE)
	private Date registrationTime;

	/**
	 * 代理区域
	 */
	@ApiModelProperty(value = "代理区域")
	private String agentArea;

	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;

	/**
	 * 代理商姓名
	 */
	@ApiModelProperty(value = "代理商姓名")
	private String controllerName;

	/**
	 * 代理商联系方式
	 */
	@ApiModelProperty(value = "代理商联系方式")
	private String controllerContact;

	/**
	 * 代理商对应部门deptId
	 */
	@ApiModelProperty(value = "代理商对应部门id-deptId")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long deptId;

}
