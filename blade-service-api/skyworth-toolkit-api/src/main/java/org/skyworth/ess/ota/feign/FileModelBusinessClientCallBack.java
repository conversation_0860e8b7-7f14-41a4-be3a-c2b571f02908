package org.skyworth.ess.ota.feign;

import org.skyworth.ess.ota.vo.FileResultVO;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName FileModelBusinessClientCallBack
 * @Description
 * @Date 2024/3/29 10:58
 */
@Component
public class FileModelBusinessClientCallBack implements IFileModelBusinessClient {

    @Override
    public R<FileResultVO> uploadFile(String container, int type, Long businessId, MultipartFile[] file) {
        return R.fail("File upload failed");
    }
}
