package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class DistributeUpgradeClientCallBack implements IDistributeUpgradeClient {
	@Override
	public R<String> deviceUpgrade(List<JSONObject> distributeUpgradeVOList) {
		return R.fail("下发ota升级指令失败");
	}

	@Override
	public R<Map<String, String>> getSasToken(Map<String, String> map) {
		return R.fail("获取soaToken失败");
	}

	@Override
	public void deleteAzureAttachment(Map<String, JSONObject> map) {
		R.fail("删除容器文件失败");
	}

	@Override
	public R<String> portableDeviceOffline(List<JSONObject> jsonObjectList) {
		return R.fail("设备下线操作失败");
	}
}
