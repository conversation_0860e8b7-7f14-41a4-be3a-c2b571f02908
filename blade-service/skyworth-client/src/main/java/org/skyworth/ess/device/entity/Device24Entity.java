/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer;
import org.skyworth.ess.util.BigDecimalSerializer2Scale;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Device24 实体类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@TableName("device_24")
@ApiModel(value = "Device24对象", description = "Device24")
@EqualsAndHashCode(callSuper = true)
public class Device24Entity extends TenantEntity {

	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String year;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String monthDay;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String hoursMin;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String seconds;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerDeratingPercentByModbus;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String modbusAddress;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String rs485BaudRate;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String wifiStaSsid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String wifiStaPassword;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal digitalMeterModbusAddress;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String digitalMeterType;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerFlowDirection;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerLimitFunction;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerLimitCtRatio;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String meterLocation;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal maximumFeedInGridPower;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal firstConnectStartTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal reconnectTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	@TableField(value = "ten_min_average_sustained_voltage")
	private BigDecimal tenMinAverageSustainedVoltage;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal reconnectSoftOutputPowerPercent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal overFrequencyPowerReductionDroop;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal insulationResistanceActiveLimit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridOverVoltageDeratingPoint;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridOverFrequencyDeratingStartPoint;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridOverFrequencyDeratingEndPoint;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal firstConnectSoftStartOutputPowerPercent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal overVoltageDeratingSettlingTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal connectionAndReconnectionPowerRampRate;
	/**
	 *  5030
	 */
	@ApiModelProperty(value = "")
	private BigDecimal outputReactivePowerMode;
	/**
	 *  5031
	 */
	@ApiModelProperty(value = "")
	private BigDecimal powerFactorSetting;
	/**
	 *  5033
	 */
	@ApiModelProperty(value = "")
	private BigDecimal reactiveControlResponseTime;
	/**
	 *  503C
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode1Percent;
	/**
	 *  503D
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode2Percent;
	/**
	 *  503E
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode3Percent;
	/**
	 *  503F
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode4Percent;
	/**
	 *  5040
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode1ValueSetting;
	/**
	 *  5041
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode2ValueSetting;
	/**
	 *  5042
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode3ValueSetting;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String regulationCode;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String inverterControl;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String factoryReset;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String clearHistoricalInfo;
	/**
	 * 站点id
	 */
	@ApiModelProperty(value = "站点id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * modbus协议版本
	 */
	@ApiModelProperty(value = "modbus协议版本")
	private String modbusProtocolVersion;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;

	/**
	 * afd开关
	 */
	@ApiModelProperty(value = "afd开关")
	private String  arcFaultDetectFunction;

	/**
	 * afd重置
	 */
	@ApiModelProperty(value = "afd重置")
	private String  arcFaultResetStatus;

	/**
	 * afd强度
	 */
	@ApiModelProperty(value = "afd强度")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal  arcFaultStrengthSettingValue;

	@ApiModelProperty(value = "功率降额控制方式")
	private BigDecimal powerDeratingControlMode;

	private String powerSwitching;

	/**
	 * cosp曲线节点1的百分比   5034
	 */
	private BigDecimal cospCurveNode1Percent;
	/**
	 * cosp曲线节点2的百分比   5035
	 */
	private BigDecimal cospCurveNode2Percent;
	/**
	 * cosp曲线节点3的百分比   5036
	 */
	private BigDecimal cospCurveNode3Percent;
	/**
	 * cosp曲线节点4的百分比  5037
	 */
	private BigDecimal cospCurveNode4Percent;
	/**
	 * cosp曲线节点1的值   5038
	 */
	private BigDecimal cospCurveNode1Value;
	/**
	 * cosp曲线节点2的值  5039
	 */
	private BigDecimal cospCurveNode2Value;
	/**
	 * cosp曲线节点3的值  503A
	 */
	private BigDecimal cospCurveNode3Value;
	/**
	 * cosp曲线节点4的值   503B
	 */
	private BigDecimal cospCurveNode4Value;
	/**
	 * 曲线节点4的值设置  5043
	 */
	private BigDecimal curveNode4ValueSetting;
	/**
	 * 曲线节点1的百分比设置  505A
	 */
	private BigDecimal curveNode1PercentageSetting;
	/**
	 * 曲线节点2的百分比设置  505B
	 */
	private BigDecimal curveNode2PercentageSetting;
	/**
	 * 曲线节点3的百分比设置  505C
	 */
	private BigDecimal curveNode3PercentageSetting;
	/**
	 * 曲线节点4的百分比设置  505D
	 */
	private BigDecimal curveNode4PercentageSetting;
	/**
	 * 曲线节点1的值设置（Pn）  505E
	 */
	private BigDecimal curveNode1ValueSettingPn;
	/**
	 * 曲线节点2的值设置（Pn）  505F
	 */
	private BigDecimal curveNode2ValueSettingPn;
	/**
	 * 曲线节点3的值设置（Pn） 5060
	 */
	private BigDecimal curveNode3ValueSettingPn;
	/**
	 * 曲线节点4的值设置（Pn）  5061
	 */
	private BigDecimal curveNode4ValueSettingPn;
	/**
	 * 无功功率百分比  5121
	 */
	private BigDecimal reactivePowerPercent;
}
