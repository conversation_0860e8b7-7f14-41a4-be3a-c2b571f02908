package org.skyworth.ess.device.service.impl;

import org.skyworth.ess.device.entity.DeviceLog21;
import org.skyworth.ess.device.entity.DeviceLog23;
import org.skyworth.ess.device.mapper.DeviceLog21Mapper;
import org.skyworth.ess.device.mapper.DeviceLog23Mapper;
import org.skyworth.ess.device.service.IDeviceLog23Service;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeviceLog23ServiceImpl extends BaseServiceImpl<DeviceLog23Mapper, DeviceLog23> implements IDeviceLog23Service {


    @Override
    public List<DeviceLog23> selectDataByLatestTime() {
        return baseMapper.selectDataByLatestTime();
    }
}
