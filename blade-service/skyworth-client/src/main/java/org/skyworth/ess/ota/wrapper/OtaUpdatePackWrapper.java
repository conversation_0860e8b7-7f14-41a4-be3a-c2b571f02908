/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.wrapper;

import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * OTA升级包 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public class OtaUpdatePackWrapper extends BaseEntityWrapper<OtaUpdatePackEntity, OtaUpdatePackVO>  {

	public static OtaUpdatePackWrapper build() {
		return new OtaUpdatePackWrapper();
 	}

	@Override
	public OtaUpdatePackVO entityVO(OtaUpdatePackEntity otaUpdatePack) {
		if (otaUpdatePack == null) {
			return null;
		}
		OtaUpdatePackVO otaUpdatePackVO = Objects.requireNonNull(BeanUtil.copy(otaUpdatePack, OtaUpdatePackVO.class));

		//User createUser = UserCache.getUser(otaUpdatePack.getCreateUser());
		//User updateUser = UserCache.getUser(otaUpdatePack.getUpdateUser());
		//otaUpdatePackVO.setCreateUserName(createUser.getName());
		//otaUpdatePackVO.setUpdateUserName(updateUser.getName());

		return otaUpdatePackVO;
	}


}
