package org.skyworth.ess.quote.service;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.vo.ItemMergeVO;
import org.skyworth.ess.design.vo.ItemSeparatePriceVO;
import org.skyworth.ess.design.vo.ProductVO;
import org.skyworth.ess.ota.feign.IFileModelBusinessClient;
import org.skyworth.ess.ota.vo.FileResultVO;
import org.skyworth.ess.quote.dto.QuoteFileDTO;
import org.skyworth.ess.quote.dto.QuoteInfoDTO;
import org.skyworth.ess.quote.entity.DeviceQuoteInfo;
import org.skyworth.ess.quote.quotedata.*;
import org.skyworth.ess.quote.vo.QuoteFileResultVO;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.service.ISkuBaseInfoService;
import org.skyworth.ess.utils.FileIOUtils;
import org.skyworth.ess.utils.WordToPdfUtil;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class QuoteService {

    private final IFileModelBusinessClient fileModelBusinessClient;

    private final IOrderService orderService;

    private final IDeviceItemService deviceItemService;

    private final ISkuBaseInfoService skuBaseInfoService;

    private final BladeRedis bladeRedis;

    private final IDictBizClient dictBizClient;

    private final IDeviceQuoteInfoService deviceQuoteInfoService;

    /**
     * 生成报价单pdf文件
     */
    public R<QuoteFileResultVO> generateQuotePdf(@Valid @RequestBody QuoteFileDTO quoteFileDTO) {
        // 查询订单是否存在
        OrderEntity orderEntity = orderService.getById(quoteFileDTO.getOrderId());
        if (ObjectUtil.isEmpty(orderEntity)) {
            return R.fail("Order does not exist");
        }

        QuoteFileResultVO quoteFileResultVO = new QuoteFileResultVO();

        File file = null;
        Path tempWordFilePath = null;
        Path tempPdfFilePath = null;
        try {
            // 把模板文件读取到本地目录中
            Resource resource = new ClassPathResource("quotefile/Advanced Standard Quote Template.docx");
            InputStream in = resource.getInputStream();
            file = new File("/tmp/Advanced Standard Quote Template.docx");
            FileUtils.copyToFile(in, file);
            if (ObjectUtil.isEmpty(file) || StringUtils.isEmpty(file.getPath())) {
                return R.fail("Failed to generate quotation");
            }

            // 模板文件生成word文档
            String templateFilePath = file.getPath();
            tempWordFilePath = Files.createTempFile("Advanced Standard Quote", ".docx");
            String wordFilePath = tempWordFilePath.toFile().getAbsolutePath();
            QuoteData quoteData = this.templateGenQuote(quoteFileDTO.getOrderId(), templateFilePath, wordFilePath);

            // word转pdf
            tempPdfFilePath = Files.createTempFile("Advanced Standard Quote", ".pdf");
            String pdfFilePath = tempPdfFilePath.toFile().getAbsolutePath();
            WordToPdfUtil.doc2pdf(wordFilePath, pdfFilePath);
            log.info("文件路径 wordFilePath: {}, pdfFilePath: {}", wordFilePath, pdfFilePath);

            // 创建一个输入流对象并关联文件
            MultipartFile multipartFile = FileIOUtils.getMultipartFile(new File(pdfFilePath));
            // multipartFile重命名
            String newFileName = "Quote " + quoteData.getQuotationNo() + ".pdf";
            File newFile = new File("/tmp/" + newFileName);
            multipartFile.transferTo(newFile);
            multipartFile = FileIOUtils.getMultipartFile(newFile);

            // 文件上传到云端
            R<FileResultVO> fileResultVOR = fileModelBusinessClient.uploadFile("agent", 1,
                    quoteFileDTO.getBusinessId(), new MultipartFile[]{multipartFile});
            if (fileResultVOR.isSuccess()) {
                FileResultVO fileResultVO = fileResultVOR.getData();
                if (fileResultVO != null) {
                    quoteFileResultVO.setBusinessId(fileResultVO.getBusinessId());
                    quoteFileResultVO.setOrderId(quoteFileDTO.getOrderId());
                    quoteFileResultVO.setQuoteFileModelVOList(fileResultVO.getFileResultVOList());
                    // 上传成功后，如果此订单未生成过报价单并且未提交过，则将报价单编号存入redis
                    DeviceQuoteInfo deviceQuoteInfo = deviceQuoteInfoService.getByOrderId(quoteFileDTO.getOrderId());
                    if ((ObjectUtil.isEmpty(deviceQuoteInfo) || ObjectUtil.isEmpty(deviceQuoteInfo.getQuoteDocBizKey())) && !bladeRedis.exists(CacheNames.ORDER_QUOTE_KEY + quoteFileDTO.getOrderId())) {
                        bladeRedis.set(CacheNames.QUOTE_NO_KEY + quoteData.getYearMonth(), String.valueOf(quoteData.getSno()));
                        QuoteInfoDTO quoteInfoDTO = new QuoteInfoDTO();
                        quoteInfoDTO.setQuotationNo(quoteData.getQuotationNo());
                        quoteInfoDTO.setSno(quoteData.getSno());
                        quoteInfoDTO.setYearMonth(quoteData.getYearMonth());
                        bladeRedis.set(CacheNames.ORDER_QUOTE_KEY + quoteFileDTO.getOrderId(), quoteInfoDTO);
                    }
                }
            } else {
                log.error("Quotation file upload failed, msg: {}", fileResultVOR.getMsg());
                return R.fail("Quotation file upload failed");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成报价单异常: {}", e.getMessage());
            return R.fail("Failed to generate quotation");
        } finally {
            // 删除临时文件
            if (tempWordFilePath != null) {
                tempWordFilePath.toFile().delete();
            }
            if (tempPdfFilePath != null) {
                tempPdfFilePath.toFile().delete();
            }
            if (file != null) {
                file.delete();
            }
        }

        return R.data(quoteFileResultVO);
    }

    private QuoteData templateGenQuote(Long orderId, String templatePath, String outPath) throws IOException {
        Configure config = Configure.builder().bind("quote_detail_table", new QuoteDetailTablePolicy()).build();
        QuoteData quoteData = quoteDataHandle(orderId);
        XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(quoteData);
        template.writeToFile(outPath);
        return quoteData;
    }

    private QuoteData quoteDataHandle(Long orderId) {
        QuoteData quoteData = new QuoteData();
        // 电话号码默认为空
        quoteData.setTel("");
        // 根据order_id查询business_order表，顾客姓名和建站地址
        OrderEntity orderEntity = orderService.getById(orderId);
        if (ObjectUtil.isNotEmpty(orderEntity)) {
            quoteData.setInvoiceCustomer(orderEntity.getCustomerName());
            quoteData.setInvoiceAddress(orderEntity.getSiteAddress());
        }

        // 报价单编码
        LocalDate localDate = LocalDate.now();
        // 查询订单是否生成过报价单，如果生成过，报价编号不变，没有则最大编号加1
        if (bladeRedis.exists(CacheNames.ORDER_QUOTE_KEY + orderId)) {
            QuoteInfoDTO quoteInfoDTO = bladeRedis.get(CacheNames.ORDER_QUOTE_KEY + orderId);
            if (ObjectUtil.isNotEmpty(quoteInfoDTO)) {
                quoteData.setQuotationNo(quoteInfoDTO.getQuotationNo());
                quoteData.setYearMonth(quoteInfoDTO.getYearMonth());
                quoteData.setSno(quoteInfoDTO.getSno());
            }
        } else {
            int sno = 1;
            // 获取年后两位
            String yearMonth = localDate.format(DateTimeFormatter.ofPattern("yyMM"));
            if (bladeRedis.exists(CacheNames.QUOTE_NO_KEY + yearMonth)) {
                // 当月不是第一次生成报价单，报价编码加1
                sno = Integer.parseInt(Objects.requireNonNull(bladeRedis.get(CacheNames.QUOTE_NO_KEY + yearMonth))) + 1;
            }
            // 查询redis中报价编码
            quoteData.setQuotationNo("SKYQU" + yearMonth + String.format("%03d", sno));
            quoteData.setYearMonth(yearMonth);
            quoteData.setSno(sno);
        }

        // 报价日期
        quoteData.setQuotationDate(localDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));

        ItemMergeVO itemMergeVO = deviceItemService.queryProductList(orderId);
        if (ObjectUtil.isNotEmpty(itemMergeVO)) {
            List<ItemData> items = new ArrayList<>();
            // 物料基础包统计
            basePackageDataHandle(itemMergeVO, items);
            // 自定义物料统计
            separateDataHandle(itemMergeVO, items);
            // 物料清单列表输出
            quoteItemsHandle(items, quoteData);
            // 物料数量列表输出
            productQuantityHandle(itemMergeVO, quoteData);
        }
        return quoteData;
    }

    // 物料数量列表输出
    private void productQuantityHandle(ItemMergeVO itemMergeVO, QuoteData quoteData) {
        // 物料数量汇总
        Map<String, Long> productQuantityMap = getProductQuantityMap(itemMergeVO);
        // 查询物料基础包的信息
        List<DictBiz> dictBizList = dictBizClient.getListAllLang(DictBizCodeEnum.AGENT_ITEM_BASE_PACKAGE.getDictCode()).getData();
        // 转map，并去重
        Map<String, DictBiz> dictBizMap = dictBizList.stream().filter(distinctByKey(DictBiz::getDictValue)).collect(Collectors.toMap(DictBiz::getDictValue, dictBiz -> dictBiz));
        DictBiz dictBiz = dictBizMap.get(itemMergeVO.getItemBasePackageName());
        String attribute3;
        if (ObjectUtil.isEmpty(dictBiz) || StringUtils.isEmpty(dictBiz.getAttribute3())) {
            if ("Sky Essential Backup".equals(itemMergeVO.getItemBasePackageName()) || "基本备份包".equals(itemMergeVO.getItemBasePackageName())) {
                attribute3 =  DictBizCodeEnum.SKU_QUOTE_DEVICE_ITEMS_B.getDictCode();
            } else {
                attribute3 = DictBizCodeEnum.SKU_QUOTE_DEVICE_ITEMS_A.getDictCode();
            }
        } else {
            attribute3 = dictBiz.getAttribute3();
        }
        List<DictBiz> skuQuoteDeviceItemsDictBizList = dictBizClient.getListAllLang(attribute3).getData();
        if (CollectionUtil.isNotEmpty(skuQuoteDeviceItemsDictBizList)) {
            for (int i = 0; i < skuQuoteDeviceItemsDictBizList.size(); i++) {
                DictBiz skuQuoteDeviceItemsDictBiz = skuQuoteDeviceItemsDictBizList.get(i);
                String dictKey = skuQuoteDeviceItemsDictBiz.getDictKey();
                boolean flag = dictKey.contains("(Kit)") || dictKey.contains("（Kit）") || dictKey.contains("(kit)") || dictKey.contains("（kit）");
                if (i == 0) {
                    quoteData.setKey1(dictKey);
                    if (flag) {
                        quoteData.setVal1("1");
                    } else {
                        quoteData.setVal1(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue()) == null ? "0" : String.valueOf(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue())));
                    }
                } else if (i == 1) {
                    quoteData.setKey2(dictKey);
                    if (flag) {
                        quoteData.setVal2("1");
                    } else {
                        quoteData.setVal2(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue()) == null ? "0" : String.valueOf(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue())));
                    }
                } else if (i == 2) {
                    quoteData.setKey3(dictKey);
                    if (flag) {
                        quoteData.setVal3("1");
                    } else {
                        quoteData.setVal3(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue()) == null ? "0" : String.valueOf(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue())));
                    }
                } else if (i == 3) {
                    quoteData.setKey4(dictKey);
                    if (flag) {
                        quoteData.setVal4("1");
                    } else {
                        quoteData.setVal4(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue()) == null ? "0" : String.valueOf(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue())));
                    }
                } else if (i == 4) {
                    quoteData.setKey5(dictKey);
                    if (flag) {
                        quoteData.setVal5("1");
                    } else {
                        quoteData.setVal5(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue()) == null ? "0" : String.valueOf(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue())));
                    }
                } else if (i == 5) {
                    quoteData.setKey6(dictKey);
                    if (flag) {
                        quoteData.setVal6("1");
                    } else {
                        quoteData.setVal6(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue()) == null ? "0" : String.valueOf(productQuantityMap.get(skuQuoteDeviceItemsDictBiz.getDictValue())));
                    }
                }
            }
        }
    }

    // 物料清单列表输出
    private void quoteItemsHandle(List<ItemData> items, QuoteData quoteData) {
        BigDecimal subtotal = null;
        QuoteDetailData quoteDetailData = new QuoteDetailData();
        List<RowRenderData> quoteDetailDataRows = new ArrayList<>();
        Map<String, Long> itemMap = CollectionUtil.newHashMap(8);
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setGroupingUsed(true);
        numberFormat.setMinimumFractionDigits(2);
        // 按sort字段从大到小排列items
        items.sort(Comparator.comparingInt(ItemData::getSort).reversed());
        for (ItemData itemData : items) {
            itemMap.put(itemData.getItem(), itemData.getQuantity());

            // 定义长度为5的数组
            String[] itemArray = new String[5];
            itemArray[0] = itemData.getItem();
            itemArray[1] = itemData.getDescription();
            itemArray[2] = String.valueOf(itemData.getQuantity());
            String unitPriceStr = numberFormat.format(itemData.getUnitPrice().setScale(2, RoundingMode.HALF_UP));
            itemData.setAmount(itemData.getUnitPrice().multiply(new BigDecimal(itemData.getQuantity())));
            String amountStr = numberFormat.format(itemData.getAmount().setScale(2, RoundingMode.HALF_UP));
            itemArray[3] = "R " + unitPriceStr;
            itemArray[4] = "R " + amountStr;
            quoteDetailDataRows.add(Rows.of(itemArray).create());

            BigDecimal amount = itemData.getAmount().setScale(2, RoundingMode.HALF_UP);
            subtotal = ObjectUtils.defaultIfNull(subtotal, BigDecimal.ZERO).add(amount);
        }
        quoteDetailData.setItems(quoteDetailDataRows);
        quoteData.setQuoteDetailData(quoteDetailData);
        log.info("物料清单汇总: {}", quoteDetailData);

        // 计算总价
        BigDecimal vat = null;
        BigDecimal total = null;
        if (subtotal != null && subtotal.compareTo(BigDecimal.ZERO) > 0) {
            subtotal = subtotal.setScale(2, RoundingMode.HALF_UP);
            vat = subtotal.multiply(new BigDecimal("0.15")).setScale(2, RoundingMode.HALF_UP);
            total = subtotal.add(vat).setScale(2, RoundingMode.HALF_UP);
            quoteData.setVat("R " + numberFormat.format(vat));
            quoteData.setSubtotal("R " + numberFormat.format(subtotal));
            quoteData.setTotal("R " + numberFormat.format(total));
        }
        log.info("报价单vat: {}, total: {}", vat, total);
    }

    // 物料数量汇总
    private Map<String, Long> getProductQuantityMap(ItemMergeVO itemMergeVO) {
        Map<String, Long> productQuantityMap = new HashMap<>();
        List<ProductVO> productVOList = new ArrayList<>();
        List<ProductVO> itemCustomizeList = itemMergeVO.getItemCustomizeList();
        List<ProductVO> itemBasePackageList = itemMergeVO.getItemBasePackageList();
        // 合并物料基础包和自定义物料
        if (CollectionUtil.isNotEmpty(itemBasePackageList)) {
            productVOList.addAll(itemBasePackageList);
        }
        if (CollectionUtil.isNotEmpty(itemCustomizeList)) {
            productVOList.addAll(itemCustomizeList);
        }
        if (CollectionUtil.isNotEmpty(productVOList)) {
            List<SkuBaseInfoEntity> baseInfoEntityList = skuBaseInfoService.list();
            if (CollectionUtil.isNotEmpty(baseInfoEntityList)) {
                // 根据sku_code分类成map
                Map<String, SkuBaseInfoEntity> skuBaseInfoEntityMap = baseInfoEntityList.stream().collect(Collectors.toMap(SkuBaseInfoEntity::getSkuCode, skuBaseInfoEntity -> skuBaseInfoEntity));
                for (ProductVO productVO : productVOList) {
                    String itemCode = productVO.getItemCode();
                    Long quantity = productVO.getQuantity() == null ? 0L : productVO.getQuantity();
                    // 物料清单统计
                    SkuBaseInfoEntity skuBaseInfoEntity = skuBaseInfoEntityMap.get(itemCode);
                    if (ObjectUtil.isNotEmpty(skuBaseInfoEntity) && StringUtils.isNotEmpty(skuBaseInfoEntity.getCategory())) {
                        productQuantityMap.merge(skuBaseInfoEntity.getCategory(), quantity, Long::sum);
                    }
                }
            }
        }
        return productQuantityMap;
    }

    // 自定义物料统计
    private void separateDataHandle(ItemMergeVO itemMergeVO, List<ItemData> items) {
        List<ItemSeparatePriceVO> itemSeparatePriceVOList = itemMergeVO.getItemSeparatePriceVOList();
        if (CollectionUtil.isNotEmpty(itemSeparatePriceVOList)) {
            for (int i = 0; i < itemSeparatePriceVOList.size(); i++) {
                ItemSeparatePriceVO itemSeparatePriceVO = itemSeparatePriceVOList.get(i);
                ItemData itemData = new ItemData();
                itemData.setSort(i + 2);
                itemData.setItem("00040");
                itemData.setDescription("555W Solar Panel");
                itemData.setQuantity(itemSeparatePriceVO.getItemOrderQuantity() == null ? 0L : itemSeparatePriceVO.getItemOrderQuantity());
                itemData.setUnitPrice(itemSeparatePriceVO.getItemSeparatePrice());
                itemData.setAmount(itemSeparatePriceVO.getItemSeparateTotalPrice());
                items.add(itemData);
            }
        }
        log.info("备料清单列表items: {}", items);
    }

    // 物料基础包统计
    private void basePackageDataHandle(ItemMergeVO itemMergeVO, List<ItemData> items) {
        ItemData itemDataBasePackage = new ItemData();
        itemDataBasePackage.setSort(1);
        itemDataBasePackage.setItem("00010");
        itemDataBasePackage.setQuantity(1L);
        // 中文就不加package
        String currentLanguage = CommonUtil.getCurrentLanguage();
        if (CommonConstant.CURRENT_LANGUAGE_EN.equals(currentLanguage)) {
            itemDataBasePackage.setDescription(itemMergeVO.getItemBasePackageName() + " Package");
        } else {
            itemDataBasePackage.setDescription(itemMergeVO.getItemBasePackageName());
        }
        BigDecimal basePackageUnitPrice = itemMergeVO.getItemFirstTotalPrice() == null ? BigDecimal.ZERO : itemMergeVO.getItemFirstTotalPrice();
        itemDataBasePackage.setUnitPrice(basePackageUnitPrice);
        itemDataBasePackage.setAmount(basePackageUnitPrice.multiply(BigDecimal.valueOf(itemDataBasePackage.getQuantity())));
        items.add(itemDataBasePackage);
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }


}