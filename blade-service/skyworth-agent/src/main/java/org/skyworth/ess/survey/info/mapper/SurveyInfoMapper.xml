<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.survey.info.mapper.SurveyInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="infoResultMap" type="org.skyworth.ess.survey.info.entity.SurveyInfoEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="technician_sign_img_biz_key" property="technicianSignImgBizKey"/>
        <result column="survey_final_finish_date" property="surveyFinalFinishDate"/>
        <result column="electrician_owner_img_biz_key" property="electricianOwnerImgBizKey"/>
        <result column="landlord_sign_img_biz_key" property="landlordSignImgBizKey"/>
        <result column="landlord_sign_date" property="landlordSignDate"/>
        <result column="survey_submit_date" property="surveySubmitDate"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectInfoPage" resultMap="infoResultMap">
        select * from survey_info where is_deleted = 0
    </select>


    <select id="exportInfo" resultType="org.skyworth.ess.survey.info.excel.SurveyInfoExcel">
        SELECT * FROM survey_info ${ew.customSqlSegment}
    </select>

</mapper>
