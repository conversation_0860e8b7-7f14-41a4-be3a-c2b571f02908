/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.wrapper;

import org.skyworth.ess.plant.entity.PhotovoltaicPlantDeviceEntity;
import org.skyworth.ess.plant.vo.PhotovoltaicPlantDeviceVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 光伏板站点设备/逆变器表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public class PhotovoltaicPlantDeviceWrapper extends BaseEntityWrapper<PhotovoltaicPlantDeviceEntity, PhotovoltaicPlantDeviceVO>  {

	public static PhotovoltaicPlantDeviceWrapper build() {
		return new PhotovoltaicPlantDeviceWrapper();
 	}

	@Override
	public PhotovoltaicPlantDeviceVO entityVO(PhotovoltaicPlantDeviceEntity PhotovoltaicPlantDevice) {
		if (PhotovoltaicPlantDevice == null) {
			return null;
		}
		PhotovoltaicPlantDeviceVO PhotovoltaicPlantDeviceVO = Objects.requireNonNull(BeanUtil.copy(PhotovoltaicPlantDevice, PhotovoltaicPlantDeviceVO.class));

		//User createUser = UserCache.getUser(PhotovoltaicPlantDevice.getCreateUser());
		//User updateUser = UserCache.getUser(PhotovoltaicPlantDevice.getUpdateUser());
		//PhotovoltaicPlantDeviceVO.setCreateUserName(createUser.getName());
		//PhotovoltaicPlantDeviceVO.setUpdateUserName(updateUser.getName());

		return PhotovoltaicPlantDeviceVO;
	}


}
