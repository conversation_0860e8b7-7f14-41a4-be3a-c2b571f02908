/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.sku.entity.SkuHistoryEntity;
import org.skyworth.ess.sku.vo.SkuHistoryVO;
import java.util.Objects;

/**
 * 物料记录表，每次新增写入一条 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public class SkuHistoryWrapper extends BaseEntityWrapper<SkuHistoryEntity, SkuHistoryVO>  {

	public static SkuHistoryWrapper build() {
		return new SkuHistoryWrapper();
 	}

	@Override
	public SkuHistoryVO entityVO(SkuHistoryEntity skuHistory) {
		SkuHistoryVO skuHistoryVO = Objects.requireNonNull(BeanUtil.copy(skuHistory, SkuHistoryVO.class));

		//User createUser = UserCache.getUser(skuHistory.getCreateUser());
		//User updateUser = UserCache.getUser(skuHistory.getUpdateUser());
		//skuHistoryVO.setCreateUserName(createUser.getName());
		//skuHistoryVO.setUpdateUserName(updateUser.getName());

		return skuHistoryVO;
	}


}
