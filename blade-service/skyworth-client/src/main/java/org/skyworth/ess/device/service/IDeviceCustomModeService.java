/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.vo.DeviceCustomModeVO;
import org.skyworth.ess.device.excel.DeviceCustomModeExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 设备/逆变器自定义模式表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface IDeviceCustomModeService extends BaseService<DeviceCustomModeEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceCustomMode
	 * @return
	 */
	IPage<DeviceCustomModeVO> selectDeviceCustomModePage(IPage<DeviceCustomModeVO> page, DeviceCustomModeVO deviceCustomMode);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceCustomModeExcel> exportDeviceCustomMode(Wrapper<DeviceCustomModeEntity> queryWrapper);

	int updateCustomMode(DeviceCustomModeEntity entity);

}
