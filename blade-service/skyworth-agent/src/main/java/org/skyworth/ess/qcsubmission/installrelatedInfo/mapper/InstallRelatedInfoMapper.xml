<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.qcsubmission.installrelatedInfo.mapper.InstallRelatedInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="relatedInfoResultMap" type="org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="feedback_doc_img_biz_key" property="feedbackDocImgBizKey"/>
        <result column="construction_date" property="constructionDate"/>
        <result column="install_technician_sign_img_biz_key" property="installTechnicianSignImgBizKey"/>
        <result column="warehouse_manager_sign_img_biz_key" property="warehouseManagerSignImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectInstallRelatedInfoPage" resultMap="relatedInfoResultMap">
        select * from install_related_info where is_deleted = 0
    </select>


    <select id="exportInstallRelatedInfo" resultType="org.skyworth.ess.qcsubmission.installrelatedInfo.excel.InstallRelatedInfoExcel">
        SELECT * FROM install_related_info ${ew.customSqlSegment}
    </select>

</mapper>
