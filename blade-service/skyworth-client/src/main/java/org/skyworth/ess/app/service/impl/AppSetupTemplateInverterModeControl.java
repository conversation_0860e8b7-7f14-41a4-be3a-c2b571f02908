package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.service.IDevice24Service;
import org.skyworth.ess.setItem.entity.SetItemEntity;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.tool.api.R;

import java.util.*;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 逆变器开关机设置子类
 * @create-time 2023/12/1 15:03:03
 */
@Slf4j
public class AppSetupTemplateInverterModeControl extends AppSetupTemplate {
	private IDevice24Service device24Service = SpringUtil.getBean(IDevice24Service.class);
	private IDevice23Service device23Service = SpringUtil.getBean(IDevice23Service.class);
	private IDevice21Service device21Service = SpringUtil.getBean(IDevice21Service.class);
	private ISetItemConfigService setItemConfigService = SpringUtil.getBean(ISetItemConfigService.class);

	@Override
	public Map<String, Object> getSetupDataByDb(Long plantId, String deviceSerialNumber) {
		Map<String, Object> inverterControl = device24Service.getInverterControl(plantId, deviceSerialNumber);
		if (ObjectUtil.isEmpty(inverterControl)) {
			inverterControl = device24Service.getInverterControlIsDelete(plantId, deviceSerialNumber);
		}
		return inverterControl;
	}

//	@Override
//	public SetItemEntity getSetupConfig(AppSetRequestVO appSetRequestVO) {
//		SetItemEntity setItemEntity = new SetItemEntity();
//		//查询设置项
//		setItemEntity.setSetItemConfigEntityList(setItemConfigService.getSetupConfig(appSetRequestVO));
//		return setItemEntity;
//	}

	@Override
	public void completeConfigItemBySubTemplate(Map<String, Object> setupDataByDb, SetItemEntity setupItemConfig, JSONObject dataObject, AppSetRequestVO appSetRequestVO) {
		if (ObjectUtil.isNotNull(setupDataByDb) && !setupDataByDb.isEmpty()) {
			for (Map.Entry<String, Object> entry : setupDataByDb.entrySet()) {

				super.buildSetupItemInfo(entry, setupItemConfig, dataObject);
			}
		}
	}

	@Override
	public R<String> issueSetupToToolKit(AppAdvancedSetup deviceAdvancedSetup) {
		R<String> result = device24Service.issueAdvancedSetup(deviceAdvancedSetup);
		log.info("set Advanced Setup : {}", result);
		return result;
	}

	@Override
    protected R<?> getReturnByMqttResult(AppAdvancedSetup deviceAdvancedSetup, R<String> result, Table<String, String, Object> table, List<AppAdvancedSetup.SetupItem> items) {
		if (result.getCode() == 200) {
			// 设置成功以后要对device21\23\24表进行修改，记录状态
			Map<String, Object> device24 = table.row("device_24");
			if (ObjectUtil.isNotEmpty(device24) && !device24.isEmpty()) {
				device24Service.updateSetup(device24, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}
			Map<String, Object> device23 = table.row("device_23");
			if (ObjectUtil.isNotEmpty(device23) && !device23.isEmpty()) {
				device23Service.updateSetup(device23, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}
			Map<String, Object> device21 = table.row("device_21");
			if (ObjectUtil.isNotEmpty(device21) && !device21.isEmpty()) {
				device21Service.updateSetup(device21, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}

			//根据下发类型处理不同的业务
			if (ObjectUtil.isNotNull(deviceAdvancedSetup.getIssueSetupType())) {
				// 开机设置成功以后，更新wifi表字段和device24表字段值
				String inverterControl = "1";
				String deviceStatus = "0";
				for (AppAdvancedSetup.SetupItem item : items) {
					inverterControl = item.getData().toString();
				}
				Device24Entity device24Entity = new Device24Entity();
				device24Entity.setPlantId(deviceAdvancedSetup.getPlantId());
				device24Entity.setDeviceSerialNumber(deviceAdvancedSetup.getDeviceSerialNumber());
				device24Entity.setInverterControl(inverterControl);
				device24Service.updateInverterControl(device24Entity);


				// 逆变器开则修改设备状态开
				if ("0".equals(inverterControl)){
					deviceStatus = "1";
				}
				LambdaUpdateWrapper<Device21Entity> deviceEq = Wrappers.<Device21Entity>lambdaUpdate()
					.set(Device21Entity::getDeviceStatus,deviceStatus)
					.eq(Device21Entity::getPlantId,deviceAdvancedSetup.getPlantId())
					.eq(Device21Entity::getDeviceSerialNumber,deviceAdvancedSetup.getDeviceSerialNumber());

				device21Service.update(deviceEq);
			}

			String currentLanguage = CommonUtil.getCurrentLanguage();
			return R.success(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100123.autoGetMessage(currentLanguage));
		} else if (result.getCode() == 100027) {
			// 把未设置成功设置项返回前端
			List<AppAdvancedSetup.SetupItem> errorItems = new ArrayList<>();
			String msg = result.getMsg();
			String[] arr = msg.split(",");
			HashSet<String> set = new HashSet<>(Arrays.asList(arr));
			String errorDateTime = "";
			for (AppAdvancedSetup.SetupItem item : items) {
				if (set.contains(item.getAddress().toString())) {
					errorItems.add(item);
				}
				if (item.getDefinition().equals(DATETIME)) {
					errorDateTime = (String) item.getData();
				}
			}
			if (set.contains(12288 + "")) {
				AppAdvancedSetup.SetupItem setupItem = new AppAdvancedSetup.SetupItem();
				setupItem.setDefinition(DATETIME);
				setupItem.setData(errorDateTime);
				errorItems.add(setupItem);
			}
			return R.data(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100027.getCode(), errorItems, I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100027.getMessage());
		}
		return result;
	}
}
