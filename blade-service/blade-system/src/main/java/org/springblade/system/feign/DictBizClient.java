/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;


import lombok.AllArgsConstructor;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.service.IDictBizService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;


/**
 * 字典服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class DictBizClient implements IDictBizClient {

	private final IDictBizService service;

	@Override
	@GetMapping(GET_BY_ID)
	public R<DictBiz> getById(Long id) {
		return R.data(service.getById(id));
	}

	@Override
	@GetMapping(GET_VALUE)
	public R<String> getValue(String code, String dictKey) {
		return R.data(service.getValue(code, dictKey));
	}

	@Override
	@GetMapping(GET_LIST)
	public R<List<DictBiz>> getList(String code) {
		return R.data(service.getList(code));
	}

	@Override
	@GetMapping(GET_LIST_LANG)
	public R<List<DictBiz>> getListByLang(String code,String language) {
		return R.data(service.getListByLang(code,language));
	}


	@Override
	@GetMapping(GET_LIST_BY_PCODE)
	public R<List<DictBiz>> getListByParentCode(String code, String parentCode,String language) {
		return R.data(service.getListByParentCode(code,parentCode,language));
	}
	@Override
	@GetMapping(GET_ALL_LIST)
	public R<List<DictBiz>> getListAllLang(String code) {
		return R.data(service.getListAllLang(code));
	}

	@Override
	@GetMapping(GET_VALUE_LANG)
	public R<String> getValueByLang(String code, String dictKey, String language) {
		return R.data(service.getValueByLang(code, dictKey,language));
	}

	@Override
	@GetMapping(BATCH_GET_LIST)
	public R<Map<String,List<DictBiz>>> batchGetList(@RequestParam("codeList") List<String> codeList) {
		return R.data(service.batchGetList(codeList));
	}
	@Override
	@GetMapping(QUERY_CHILD_BY_DICT_KEY)
	public R<List<DictBiz>> queryChildByDictKey(String code, String dictKey,String language) {
		return R.data(service.queryChildByDictKey(code, dictKey, language));
	}

	@Override
	public R<List<DictBiz>> getDataByCodeAndKey(String code, String dictKey) {
		return R.data(service.getDataByCodeAndKey(code,dictKey));
	}
}
