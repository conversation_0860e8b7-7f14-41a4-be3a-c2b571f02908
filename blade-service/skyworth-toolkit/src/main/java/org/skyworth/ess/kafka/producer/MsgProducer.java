package org.skyworth.ess.kafka.producer;

import com.alibaba.nacos.common.utils.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.tool.api.R;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * kafka消息生产者
 * <AUTHOR>
 * */
@Service
public class MsgProducer {

	@Value("${blade.kafka.broker}")
	private String bootstrapServer;
	@Autowired
	IDictBizClient client;
	private static final Logger logger = LoggerFactory.getLogger(MsgProducer.class);
	private static final String DECODE_STR = "org.apache.kafka.common.serialization.StringSerializer";


	/**
	 * 存储生产者
	 */
	private final Map<String, Producer<String, String>> producerMap = new ConcurrentHashMap<>();


	public void sendMsg(String topic, String msg) {
		sendMsg(topic, null, msg);
	}

	/**
	 * 发送数据
	 * @param topic
	 * @param key
	 * @param msg
	 */
	public void sendMsg(String topic, String key, String msg) {
		try {
			if (StringUtils.isNotEmpty(msg)) {
				boolean isProducer = producerMap.containsKey(topic);
				if (!isProducer) {
					init(topic);
				}

				if(key == null) {
					producerMap.get(topic).send(new ProducerRecord<>(topic, msg));
				} else {
					producerMap.get(topic).send(new ProducerRecord<>(topic, key, msg));
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	/**
	 * 初始化生产者
	 * @Param topic
     */
	private void init(String topic) {
		Properties props = new Properties();
		// 服务器ip:端口号，集群用逗号分隔
		props.put("bootstrap.servers", bootstrapServer);
		props.put("acks", "all");
		props.put("retries", 3);
		props.put("batch.size", 16384);
		props.put("linger.ms", 1);
		props.put("request.timeout.ms", 10000);
		props.put("buffer.memory", 33554432);
		props.put("key.serializer", DECODE_STR);
		props.put("value.serializer", DECODE_STR);
		Producer<String, String> producer = new KafkaProducer<>(props);
		producerMap.put(topic, producer);
	}

}
