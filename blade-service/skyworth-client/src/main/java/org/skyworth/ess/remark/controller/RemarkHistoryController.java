package org.skyworth.ess.remark.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.remark.entity.RemarkHistoryEntity;
import org.skyworth.ess.remark.vo.RemarkHistoryVO;
import org.skyworth.ess.remark.excel.RemarkHistoryExcel;
import org.skyworth.ess.remark.wrapper.RemarkHistoryWrapper;
import org.skyworth.ess.remark.service.IRemarkHistoryService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 站点评论记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-remarkHistory/remarkHistory")
@Api(value = "站点评论记录表", tags = "站点评论记录表接口")
public class RemarkHistoryController extends BladeController {

	private final IRemarkHistoryService remarkHistoryService;

	/**
	 * 站点评论记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入remarkHistory")
	public R<RemarkHistoryVO> detail(RemarkHistoryEntity remarkHistory) {
		RemarkHistoryEntity detail = remarkHistoryService.getOne(Condition.getQueryWrapper(remarkHistory));
		return R.data(RemarkHistoryWrapper.build().entityVO(detail));
	}
	/**
	 * 站点评论记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入remarkHistory")
	public R<IPage<RemarkHistoryVO>> list(@ApiIgnore @RequestParam Map<String, Object> remarkHistory, Query query) {
		IPage<RemarkHistoryEntity> pages = remarkHistoryService.page(Condition.getPage(query), Condition.getQueryWrapper(remarkHistory, RemarkHistoryEntity.class));
		return R.data(RemarkHistoryWrapper.build().pageVO(pages));
	}

	/**
	 * 站点评论记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入remarkHistory")
	public R<IPage<RemarkHistoryVO>> page(RemarkHistoryVO remarkHistory, Query query) {
		IPage<RemarkHistoryVO> pages = remarkHistoryService.selectRemarkHistoryPage(Condition.getPage(query), remarkHistory);
		return R.data(pages);
	}

	/**
	 * 站点评论记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入remarkHistory")
	public R save(@Valid @RequestBody RemarkHistoryEntity remarkHistory) {
		return R.status(remarkHistoryService.save(remarkHistory));
	}

	/**
	 * 站点评论记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入remarkHistory")
	public R update(@Valid @RequestBody RemarkHistoryEntity remarkHistory) {
		return R.status(remarkHistoryService.updateById(remarkHistory));
	}

	/**
	 * 站点评论记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入remarkHistory")
	public R submit(@Valid @RequestBody RemarkHistoryEntity remarkHistory) {
		return R.status(remarkHistoryService.saveOrUpdate(remarkHistory));
	}

	/**
	 * 站点评论记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(remarkHistoryService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-remarkHistory")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入remarkHistory")
	public void exportRemarkHistory(@ApiIgnore @RequestParam Map<String, Object> remarkHistory, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<RemarkHistoryEntity> queryWrapper = Condition.getQueryWrapper(remarkHistory, RemarkHistoryEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(RemarkHistory::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(RemarkHistoryEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<RemarkHistoryExcel> list = remarkHistoryService.exportRemarkHistory(queryWrapper);
		ExcelUtil.export(response, "站点评论记录表数据" + DateUtil.time(), "站点评论记录表数据表", list, RemarkHistoryExcel.class);
	}

}
