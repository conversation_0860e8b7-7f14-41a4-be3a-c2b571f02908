<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.company.mapper.AgentFileInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentFileInfoResultMap" type="org.skyworth.ess.company.entity.AgentFileInfoEntity">
        <result column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="file_doc_biz_key" property="fileDocBizKey"/>
        <result column="file_aliasing" property="fileAliasing"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>

    <delete id="deleteByAgentId">
        delete
        from agent_file_info a
        where a.agent_id = #{agentId}
    </delete>

</mapper>
