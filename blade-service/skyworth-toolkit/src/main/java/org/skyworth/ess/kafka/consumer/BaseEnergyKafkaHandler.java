package org.skyworth.ess.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.DeviceLogService;
import org.skyworth.ess.device.client.ITimeZoneDeviceServiceClient;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.skyworth.ess.log.service.OriginalDataRecordService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.mq.kafka.config.KafkaDataHandler;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.IotContextUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * 储能数据清洗公共类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class BaseEnergyKafkaHandler implements KafkaDataHandler {

	private OriginalDataRecordService recordService;

	private ITimeZoneDeviceServiceClient timeZoneDeviceServiceClient;

	private static final List<String> TYPE_LIST = Arrays.asList("1", "2", "3", "4");

	/**
	 * 处理数据的主方法
	 * 该方法负责过滤重复数据，获取时区信息，处理每个JSON对象，并分发数据到具体的处理类
	 *
	 * @param list 包含JSON对象的列表
	 * @return 处理结果代码，0表示完成
	 */
	@Override
	public int handlerData(List<JSONObject> list) {
		log.info("BaseEnergyKafkaHandler : {}", list);
		if (list == null || list.isEmpty()) {
			log.warn("Input list is empty or null.");
			return 0;
		}
		Map<String, List<JSONObject>> dataList = new ConcurrentHashMap<>();
		Set<Object> seen = new HashSet<>();
		// 过滤重复的数据
		List<JSONObject> filteredList = list.parallelStream()
			.filter(jsonObject -> isUnique(jsonObject, seen))
			.collect(Collectors.toList());
		// 获取站点所属时区
		List<Long> plantIds = filteredList.stream()
			.map(json -> json.getJSONObject(KafkaDataHandler.LINE))
			.filter(Objects::nonNull)
			.map(line -> line.getLong("plantID"))
			.filter(Objects::nonNull)
			.distinct() // 去重以减少不必要的调用
			.collect(Collectors.toList());
		// 根据站点id获取时区
		Map<String, String> timeZoneMap = timeZoneDeviceServiceClient.getMapFromCacheByPlantIdList(plantIds).getData();
		// 获取需要处理的数据
		filteredList.forEach(jsonObject -> processJsonObject(jsonObject, timeZoneMap, dataList));
		// 分发数据到具体的处理类
		dataList.forEach(this::dispatchData);
		return 0;
	}

	/**
	 * 处理JSON对象，验证数据完整性，转换时区并分类存储数据
	 *
	 * @param jsonObject 待处理的JSON对象
	 * @param timeZoneMap 包含时区信息的映射表
	 * @param dataList 用于存储分类后数据的映射表
	 */
	private void processJsonObject(JSONObject jsonObject, Map<String, String> timeZoneMap, Map<String,
			List<JSONObject>> dataList) {
		try {
			// 获取LINE字段，如果不存在则记录错误并返回
			JSONObject data = jsonObject.getJSONObject(LINE);
			if (data == null) {
				log.warn("Missing LINE field in jsonObject: {}", jsonObject.toJSONString());
				saveErrorRecord(jsonObject, "Missing LINE field");
				return;
			}
			// 验证type字段，如果无效则记录错误并返回
			Integer type = data.getInteger("type");
			if (!ValidationUtil.isNotEmpty(type)) {
				log.warn("Invalid type in jsonObject: {}", jsonObject.toJSONString());
				saveErrorRecord(jsonObject, "Invalid type");
				return;
			}
			// 获取并验证plantID和deviceSn字段，如果任一为空则记录错误并返回
			String plantId = data.getString("plantID");
			String deviceSn = data.getString("deviceSn");
			if (StringUtils.isEmpty(plantId) || StringUtils.isEmpty(deviceSn)) {
				log.warn("Missing plantID or deviceSn in jsonObject: {}", jsonObject.toJSONString());
				saveErrorRecord(jsonObject, "Missing plantID or deviceSn");
				return;
			}
			// 检查站点是否存在，如果不存在则记录错误并返回
			List<Map<String, Object>> existPlantResult = recordService.isExistPlant(Long.parseLong(plantId), deviceSn);
			if (existPlantResult == null || existPlantResult.isEmpty()) {
				log.warn("Plant not found for plantID: {}, deviceSn: {}", plantId, deviceSn);
				//saveErrorRecord(jsonObject, "Plant not found");
				return;
			}
			// 获取通信版本和时间戳
			String modbusProtocolVersion = data.getString("commuVersion");
			// 获取时区并转换时间戳
			String timeZone = timeZoneMap.getOrDefault(plantId, CommonConstant.COMMON_DEFAULT_TIME_ZONE);
			data.put("timeZone", timeZone);
			// 将数据分类存储
			String key = type + "_" + modbusProtocolVersion;
			dataList.computeIfAbsent(key, k -> new ArrayList<>()).add(data);
		} catch (Exception e) {
			// 处理异常情况，记录错误信息
			log.error("Error processing jsonObject: {}", jsonObject.toJSONString(), e);
			saveErrorRecord(jsonObject, e.getMessage());
		}
	}

	/**
	 * 根据数据类型分发数据到具体的处理类
	 *
	 * @param key   数据类型和协议版本的组合键
	 * @param value 包含相同类型数据的列表
	 */
	private void dispatchData(String key, List<JSONObject> value) {
		String type = key.split("_")[0];
		if (ValidationUtil.isNotEmpty(type) && TYPE_LIST.contains(type)) {
			DeviceLogService deviceLogService =
				(DeviceLogService) IotContextUtils.getBean(Constants.PREFIX_DEVICE_LOG + type);
			deviceLogService.handler(value);
		} else {
			log.warn("Invalid type in key: {}", key);
		}
	}

	/**
	 * 保存错误记录
	 *
	 * @param jsonObject   导致错误的JSON对象
	 * @param errorMessage 错误信息
	 */
	private void saveErrorRecord(JSONObject jsonObject, String errorMessage) {
		OriginalDataRecord record = new OriginalDataRecord();
		record.setData(jsonObject.toJSONString());
		record.setType("2");
		record.setExceptionInfo(errorMessage);
		recordService.save(record);
	}

	/**
	 * 检查JSON对象是否唯一，用于过滤重复的数据
	 *
	 * @param jsonObject 待检查的JSON对象
	 * @param seen       用于记录已见过的对象的集合
	 * @return 如果对象是唯一的，则返回true，否则返回false
	 */
	private static boolean isUnique(JSONObject jsonObject, Set<Object> seen) {
		if (!jsonObject.containsKey(BaseEnergyKafkaHandler.LINE)) {
			return true;
		}
		Object value = jsonObject.get(BaseEnergyKafkaHandler.LINE);
		if (value instanceof JSONObject) {
			JSONObject nestedObject = (JSONObject) value;
			return seen.add(nestedObject.toString());
		} else {
			return seen.add(value);
		}
	}
}
