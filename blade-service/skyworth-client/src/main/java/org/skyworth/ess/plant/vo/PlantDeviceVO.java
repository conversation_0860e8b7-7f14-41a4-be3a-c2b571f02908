package org.skyworth.ess.plant.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * <AUTHOR>
 */
@Data
public class PlantDeviceVO extends SkyWorthEntity {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long plantId;
    private String deviceSerialNumber;
    // 是否并机 0否 1是
    private String parallelModeFunction;
    // 是否在通过平台开机过。1否0是
    private Integer startupByBackstage;
    // 该逆变器是否配网成功。1否0是
    private Integer inverterConfigureNetwork;
    // 该逆变器是否在通过平台控制
    private String inverterControl;
    private String inverterKind;
	// 设备是否在线 1在线 0离线
	private String onLineStatus;
}
