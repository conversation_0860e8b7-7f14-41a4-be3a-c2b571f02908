package org.skyworth.ess.device.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.BinaryConvertUtils2;
import org.skyworth.ess.device.DeviceLogService;
import org.skyworth.ess.device.client.IDeviceLog21Client;
import org.skyworth.ess.device.client.ISoftVersionBizClient;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.DeviceLog21;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.skyworth.ess.log.service.OriginalDataRecordService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.DateUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 处理所上报2.1的数据
 */
@Component("DeviceLog2_" + Constants.ONE)
@Slf4j
@AllArgsConstructor
public class DeviceLog21Handler implements DeviceLogService {


	private BinaryConvertUtils2 binaryConvertUtils;

	private IDeviceLog21Client iDeviceLog21Client;

	private OriginalDataRecordService recordService;

	private ISoftVersionBizClient softVersionBizClient;

	@Override
	public void handler(List<JSONObject> dataList) {
		// 检查输入列表是否为空
		if (CollectionUtils.isNullOrEmpty(dataList)) {
			return;
		}
		List<DeviceLog21> deviceLog21List = new ArrayList<>();
		List<JSONObject> softVersions = new ArrayList<>();
		dataList.stream().parallel().forEach(data -> {
			try {
				long plantId = Long.parseLong(data.getString("plantID"));
				String modbusProtocolVersion = data.getString("commuVersion");
				String deviceSerialNumber = data.getString("deviceSn");
				String company = data.getString("company");
				String content = data.getString("content");
				String wifiVersion = data.getString("DongleVersion");
				Long timeStamp = data.getLong("timeStamp");
				String timeZone = data.getString("timeZone");
				Date deviceDateTime = DateUtil.convertLongTimeZoneForDeviceUpload(timeStamp,timeZone,CommonConstant.COMMON_DEFAULT_TIME_ZONE);
				JSONObject softWifiVersion = new JSONObject();
				softWifiVersion.put("deviceSn", deviceSerialNumber);
				softWifiVersion.put("company", "lx");
				softWifiVersion.put("type", "wifi");
				softWifiVersion.put("wifiVersion", wifiVersion);
				softVersions.add(softWifiVersion);
				JSONObject result = binaryConvertUtils.getData(content, Constants.ONE, modbusProtocolVersion);
				if (result != null) {
					//拿到ota相关版本号，同步推到ota表
					JSONObject softVersion = new JSONObject();
					softVersion.put("deviceSn", deviceSerialNumber);
					softVersion.put("company", company);
					softVersion.put("type", "inverter");
					softVersion.put("master", result.getString("masterSoftwareVersion").trim());
					softVersion.put("slave", result.getString("slaveFirmwareVersion").trim());
					softVersion.put("ems", result.getString("emsFirmwareVersion").trim());
					softVersion.put("dc", result.getString("dcdcFirmwareVersion").trim());
					softVersions.add(softVersion);
					if (result.containsKey("bmsFirmwareVersion")) {
						JSONObject bmsVersion = new JSONObject();
						bmsVersion.put("deviceSn", deviceSerialNumber);
						bmsVersion.put("company", company);
						bmsVersion.put("type", "battery");
						bmsVersion.put("bmsFirmwareVersion", result.getString("bmsFirmwareVersion").trim());
						softVersions.add(bmsVersion);
					}
					DeviceLog21 deviceLog21 = JSON.toJavaObject(result, DeviceLog21.class);
					deviceLog21.setPlantId(plantId);
					deviceLog21.setDeviceDateTime(deviceDateTime);
					deviceLog21.setModbusProtocolVersion(modbusProtocolVersion);
					deviceLog21.setDeviceSerialNumber(deviceSerialNumber);
					deviceLog21.setWifiVersion(wifiVersion);
					if (result.containsKey("bmsFirmwareVersion")) {
						deviceLog21.setBatteryVersion(result.getString("bmsFirmwareVersion").trim());
					}
					Date currentTime = new Date();
					deviceLog21.setCreateTime(currentTime);
					deviceLog21.setUpdateTime(currentTime);
					deviceLog21.setIsDeleted(0);
					deviceLog21.setSynchStatus("Y");
					deviceLog21.setTenantId(CommonConstant.CLIENT_TENANT_ID);
					deviceLog21List.add(deviceLog21);
				}
			} catch (Exception e) {
				//将数据插入记录表中  保留异常信息
				OriginalDataRecord record = new OriginalDataRecord();
				record.setData(data.toJSONString());
				record.setType("1");
				record.setExceptionInfo(e.getMessage());
				recordService.save(record);
			}
		});
		try {
			if (!deviceLog21List.isEmpty()) {
				//插入数据
				iDeviceLog21Client.insertBatchDeviceLog21Column(deviceLog21List);
				//更新版本信息
				softVersionBizClient.updateVersion(softVersions);
			}
		} catch (Exception e) {
			throw new RuntimeException("deviceLog21Handler is exception");
		}
	}
}
