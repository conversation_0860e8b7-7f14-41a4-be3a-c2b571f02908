package org.skyworth.ess.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年5月20日 09:46:36
 */
public enum DeviceEventContentMultiLanguageEnum {


	PLANT_EVENT_CREATE("Errichtung der Energieanlage","Establish the energy station","创建电站"),
	PLANT_EVENT_DELETE("Die Energiestation wird gelöscht","The energy station is deleted","删除电站"),
	INVERTER_EVENT_BIND_PLANT("Wechselrichter ist an die Energieanlage gebunden","Inverter is bound to energy station","逆变器绑定到电站"),
	INVERTER_EVENT_INFO_IMPORT("Informationen zum Inverter-Werk","Inverter factory information","逆变器出厂信息"),
	INVERTER_EVENT_BIND_BATTERY("Batterie hinzufügen","Add battery","添加电池"),
	INVERTER_EVENT_UNBIND_BATTERY("Batterie abbinden","Unbind battery","取消绑定电池"),
	BATTERY_EVENT_BIND_BATTERY("Batterie ist an Wechselrichter gebunden","Battery is bound to inverter","电池绑定逆变器"),
	BATTERY_EVENT_UNBIND("Batterie ist ungebunden","Battery is unbound","电池取消绑定");

	final String deEvent;
	final String enEvent;
	final String zhEvent;
	final static List<String> LANGUAGE_LIST = Arrays.asList("en","de","cn","jp");

	DeviceEventContentMultiLanguageEnum(String deEvent, String enEvent, String zhEvent) {
		this.deEvent = deEvent;
		this.enEvent = enEvent;
		this.zhEvent = zhEvent;
	}

	public String getDeviceEventByLang(String language) {
		if (LANGUAGE_LIST.contains(language)){
			switch (language){
				case "en":
				case "jp":
					return enEvent;
				case "de":
					return deEvent;
				case "cn":
					return zhEvent;
            }
		}
		return enEvent;
	}
}
