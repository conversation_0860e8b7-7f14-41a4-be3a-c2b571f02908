<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop.xsd
                           http://www.springframework.org/schema/tx
                           http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!--配置dataSource    com.alibaba.druid.pool.DruidDataSource
    org.apache.commons.dbcp.BasicDataSource  -->
<!--    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">-->
<!--        &lt;!&ndash;配置连接参数&ndash;&gt;-->
<!--        <property name="driverClassName" value="com.mysql.cj.jdbc.Driver"/>-->
<!--        <property name="url" value="**************************************"/>-->
<!--        <property name="username" value="root"/>-->
<!--        <property name="password" value="skyworth@123"/>-->

    <!--配置事务管理器-->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <!--注入数据源-->
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!--配置事务通知-->
    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <!--配置事务的管理策略-->
        <tx:attributes>
            <tx:method name="transfer"/>
            <tx:method name="*" propagation="REQUIRED"
                       read-only="false" timeout="300" rollback-for="java.lang.Exception"/>
            <tx:method name="get*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="query*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="list*" propagation="REQUIRED" read-only="true"/>
            <tx:method name="page*" propagation="REQUIRED" read-only="true"/>
        </tx:attributes>
    </tx:advice>


    <!--配置切入点和切入面-->
    <aop:config>
        <!--切入点-->
        <aop:pointcut id="skyworthPointcut" expression="execution(* org.skyworth.ess..service.impl.*.*(..))"/>
        <!--切面-->
        <aop:advisor advice-ref="txAdvice" pointcut-ref="skyworthPointcut"/>
    </aop:config>


</beans>
