/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.sku.entity.SkuInventoryManageEntity;
import org.skyworth.ess.sku.vo.SkuInventoryManageVO;
import org.skyworth.ess.sku.excel.SkuInventoryManageExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 物料库存管理，sku唯一，数量发货时扣除，新增时累加 服务类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public interface ISkuInventoryManageService extends BaseService<SkuInventoryManageEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param skuInventoryManage
	 * @return
	 */
	IPage<SkuInventoryManageVO> selectSkuInventoryManagePage(IPage<SkuInventoryManageVO> page, SkuInventoryManageVO skuInventoryManage);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SkuInventoryManageExcel> exportSkuInventoryManage(Wrapper<SkuInventoryManageEntity> queryWrapper);

}
