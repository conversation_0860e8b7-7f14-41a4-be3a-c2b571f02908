package org.skyworth.ess.device.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/9/21 10:20:52
 */
public interface IInverterDeviceService {

	IPage<InverterDevicePageVO> selectInverterDevicePageByCountry(IPage<InverterDevicePageVO> page, InverterDevicePageVO inverterDevicePageVO);

	JSONObject getInverterOverviewByCountry(InverterDevicePageVO inverterDevicePageVO);

	InverterDetailHeadVO getInverterDetailHead(Map<String, Object> detail);

	InverterDeviceInstallVO getInverterDetailInstallInfo(String deviceSerialNumber, String plantId);

	Map<String, List<Object>> selectStatusReport(InverterReportQueryVO queryCondition);

	InverterDayReport selectEveryDayStat(InverterReportQueryVO queryCondition);

	JSONObject selectEnergyTotalStat(InverterReportQueryVO queryCondition);

	Device23Entity getDetailParameter(Device23Entity device23Entity);

	DeviceCurrentStatusEntity getDetailCurrentStatus(DeviceCurrentStatusEntity deviceCurrentStatus);

	Device21Entity getDetailDeviceInfo(Device21Entity device21Entity);

	void selectStatusReportExport(InverterReportQueryVO queryCondition, HttpServletResponse response);

	void selectEveryDayStatExport(InverterReportQueryVO queryCondition, HttpServletResponse response);
}

