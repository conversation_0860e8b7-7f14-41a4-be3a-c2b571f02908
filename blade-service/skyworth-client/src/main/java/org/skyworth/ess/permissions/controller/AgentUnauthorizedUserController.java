/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.permissions.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.permissions.entity.AgentUnauthorizedUserEntity;
import org.skyworth.ess.permissions.service.IAgentUnauthorizedUserService;
import org.skyworth.ess.permissions.vo.AgentUnauthorizedUserVO;
import org.skyworth.ess.permissions.wrapper.AgentUnauthorizedUserWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 站点挂的代理商下，无权限操作的用户id 控制器
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/agentUnauthorizedUser")
@Api(value = "站点挂的代理商下，无权限操作的用户id", tags = "站点挂的代理商下，无权限操作的用户id接口")
public class AgentUnauthorizedUserController extends BladeController {

	private final IAgentUnauthorizedUserService agentUnauthorizedUserService;

	private final IUserSearchClient userSearchClient;

	/**
	 * 站点挂的代理商下，无权限操作的用户id 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入agentUnauthorizedUser")
	@TenantIgnore
	public R<IPage<AgentUnauthorizedUserVO>> list(@ApiIgnore @RequestParam Map<String, Object> agentUnauthorizedUser, Query query) {
		IPage<AgentUnauthorizedUserEntity> pages = agentUnauthorizedUserService.page(Condition.getPage(query), Condition.getQueryWrapper(agentUnauthorizedUser, AgentUnauthorizedUserEntity.class));
		List<AgentUnauthorizedUserEntity> agentUnauthorizedUserEntityList = pages.getRecords();
		if (CollectionUtil.isNotEmpty(agentUnauthorizedUserEntityList)) {
			List<User> userList = userSearchClient.listAllByUserIds(agentUnauthorizedUserEntityList.stream().map(AgentUnauthorizedUserEntity::getUnauthorizedUserId).collect(Collectors.toList())).getData();
			if (CollectionUtil.isNotEmpty(userList)) {
				Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
				agentUnauthorizedUserEntityList.forEach(a -> {
					User user = userMap.get(a.getUnauthorizedUserId());
					if (user != null) {
						a.setUserName(user.getRealName());
						a.setUserPhone(user.getPhone());
					}
				});
			}
		}
		return R.data(AgentUnauthorizedUserWrapper.build().pageVO(pages));
	}


	/**
	 * 站点挂的代理商下，无权限操作的用户id 新增
	 */
	@PostMapping("/saveBatch")
	@ApiOperationSupport(order = 4)
	@TenantIgnore
	@ApiOperation(value = "批量新增", notes = "传入agentUnauthorizedUser")
	public R saveBatch(@Valid @RequestBody List<AgentUnauthorizedUserEntity> agentUnauthorizedUserList) {
		return R.status(agentUnauthorizedUserService.saveBatch(agentUnauthorizedUserList));
	}


	/**
	 * 站点挂的代理商下，无权限操作的用户id 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@TenantIgnore
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(agentUnauthorizedUserService.deleteLogic(Func.toLongList(ids)));
	}
}
