package org.skyworth.ess.log.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.log.entity.OriginalDataRecord;

import java.util.List;
import java.util.Map;

public interface OriginalDataRecordMapper  extends BaseMapper<OriginalDataRecord> {
	List<Map<String,Object>> isExistPlant(@Param("plantId") long plantId,@Param("deviceSn") String deviceSn);

	Map<String, String> getTimeZone(String deviceSn);

	Map<String,Object> getStartupByBackstage(@Param("plantId") long plantId,@Param("deviceSn") String deviceSn);

	int updateWifiAndPlant(@Param("plantId") long plantId,@Param("deviceSn") String deviceSn);

	List<JSONObject> getTimeZones(List<String> deviceSnList);

	List<Map<String,Object>> isParallelModeList(@Param("plantIds")List<Long> plantIds);
}
