/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.UserMappingEntity;
import org.springblade.system.service.IUserMappingService;
import org.springblade.system.vo.UserInfoMappingVO;
import org.springblade.system.wrapper.UserMappingWrapper;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 控制器
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/userMapping")
@Api(value = "用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用", tags = "用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用接口")
public class UserMappingController extends BladeController {

	private final IUserMappingService userMappingService;

	/**
	 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入userMapping")
	public R<UserInfoMappingVO> detail(UserMappingEntity userMapping) {
		UserMappingEntity detail = userMappingService.getOne(Condition.getQueryWrapper(userMapping));
		return R.data(UserMappingWrapper.build().entityVO(detail));
	}

	/**
	 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入userMapping")
	public R<IPage<UserInfoMappingVO>> list(@ApiIgnore @RequestParam Map<String, Object> userMapping, Query query) {
		IPage<UserMappingEntity> pages = userMappingService.page(Condition.getPage(query), Condition.getQueryWrapper(userMapping, UserMappingEntity.class));
		return R.data(UserMappingWrapper.build().pageVO(pages));
	}

	/**
	 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入userMapping")
	public R<IPage<UserInfoMappingVO>> page(UserInfoMappingVO userMapping, Query query) {
		IPage<UserInfoMappingVO> pages = userMappingService.selectUserMappingPage(Condition.getPage(query), userMapping);
		return R.data(pages);
	}

	/**
	 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入userMapping")
	public R save(@Valid @RequestBody UserMappingEntity userMapping) {
		return R.status(userMappingService.save(userMapping));
	}

	/**
	 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入userMapping")
	public R update(@Valid @RequestBody UserMappingEntity userMapping) {
		return R.status(userMappingService.updateById(userMapping));
	}

	/**
	 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入userMapping")
	public R submit(@Valid @RequestBody UserMappingEntity userMapping) {
		return R.status(userMappingService.saveOrUpdate(userMapping));
	}

	/**
	 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userMappingService.deleteLogic(Func.toLongList(ids)));
	}


}
