package org.skyworth.ess.quote.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.quote.dto.QuoteFileDTO;
import org.skyworth.ess.quote.service.QuoteService;
import org.skyworth.ess.quote.vo.QuoteFileResultVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("quote")
@Api(value = "报价单接口", tags = "报价单接口")
public class QuoteController extends BladeController {

    private final QuoteService quoteService;

    /**
     * 生成报价单pdf文件
     */
    @ApiOperation(value = "生成报价单pdf文件", notes = "生成报价单pdf文件")
    @PostMapping("/generateQuotePdf")
    public R<QuoteFileResultVO> generateQuotePdf(@Valid @RequestBody QuoteFileDTO quoteFileDTO) {
        return quoteService.generateQuotePdf(quoteFileDTO);
    }

}
