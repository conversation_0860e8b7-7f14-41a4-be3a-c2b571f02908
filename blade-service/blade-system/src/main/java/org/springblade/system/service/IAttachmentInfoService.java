/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.springblade.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.vo.AttachmentInfoVO;

import java.util.List;

/**
 * 附件表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
public interface IAttachmentInfoService extends BaseService<AttachmentInfoEntity> {

	/**
	 * 根据业务id批量查询附件信息
	 *
	 * @param businessIds 入参
	 * @return List<AttachmentInfoVO>
	 * <AUTHOR>
	 * @since 2023/10/7 11:23
	 **/
	List<AttachmentInfoEntity> findByBusinessIds(List<Long> businessIds);

	/**
	 * 批量修改
	 *
	 * @param attachmentInfoEntityList 入参
	 * <AUTHOR>
	 * @since 2023/10/24 16:41
	 **/
	void updateBatch(List<AttachmentInfoEntity> attachmentInfoEntityList, Long updateUserId);

}
