/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.excel.SetItemConfigExcel;
import org.skyworth.ess.setItem.vo.SetItemConfigVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * APP设置项配置 服务类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public interface ISetItemConfigService extends BaseService<SetItemConfigEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param setItemConfig
	 * @return
	 */
	IPage<SetItemConfigVO> selectSetItemConfigPage(IPage<SetItemConfigVO> page, SetItemConfigVO setItemConfig);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SetItemConfigExcel> exportSetItemConfig(Wrapper<SetItemConfigEntity> queryWrapper);

	boolean removeSetItem(String ids);

	SetItemConfigEntity getSetItemDetail(SetItemConfigVO setItemConfig);

	List<SetItemConfigEntity> getSetupConfig(AppSetRequestVO appSetRequestVO);

	boolean saveSetItem(SetItemConfigEntity setItemConfig);

	boolean updateSetItemById(SetItemConfigEntity setItemConfig);

	void generateAnotherModelSetItem(SetItemConfigEntity setItemConfig);

	void importExcel(List<SetItemConfigExcel> data, Boolean isCovered);
}
