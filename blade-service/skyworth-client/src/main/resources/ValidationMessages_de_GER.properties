client.battery.query.connot.empty=Die Abfragekriterien dürfen nicht leer sein.
client.setItem.delete.fail=Löschen Sie zuerst den Subkonfigurationsknoten!
client.system.error=Systemfehler
client.email.address.error=Die E-Mail-Adresse ist falsch
client.parameter.error.empty=Parameter-Ausnahme: Parameter kann nicht leer sein
client.parameter.error.record.duplicate=Es gibt doppelte Datensätze, bitte prüfen.
client.parameter.error.record.empty=Bitte einen Datensatz auswählen.
client.invert.serial.number.exist=Die Seriennummer des Wechselrichters : [%s] ist vorhanden
client.plant.name.exist=Der Pflanzenname: [%s] ist vorhanden
client.ota.record.count.error=Die Gesamtzahl der ausgewählten Datensätze stimmt nicht mit der Gesamtzahl der Server überein
client.plant.change.user.phone.empty=Der mobile Verifizierungscode ist leer
client.plant.change.user.phone.error=Der eingegebene mobile Verifizierungscode ist falsch
client.plant.change.user.email.empty=Der eingegebene E-Mail-Verifizierungscode ist leer
client.plant.change.user.email.error=Der eingegebene E-Mail-Verifizierungscode ist falsch
client.plant.change.user.phone.email.empty=Mobiltelefon- und E-Mail-Adresse dürfen nicht vollständig leer sein
client.plant.change.user.check=Das aktuelle Benutzerkennwort ist falsch
client.plant.change.user.phone.same=Dies kann nicht fortgesetzt werden, wenn Sie die Handynummer Ihres Kontos nicht ändern möchten.
client.plant.change.user.email.same=Dies kann nicht fortgesetzt werden, wenn Sie die E-Mail-Adresse Ihres Kontos nicht ändern möchten.
client.ota.record.upgrade.one=Mit derselben Seriennummer kann jeweils nur ein Firmware-Typ aktualisiert werden. Die Seriennummer mit Ausnahmeprüfung lautet wie folgt: %s
client.email.code.exist=Der E-Mail-Bestätigungscode wurde gesendet. Bitte überprüfen Sie Ihren Posteingang. Wenn Sie ihn nicht erhalten, werfen Sie einen Blick in den Papierkorb oder prüfen Sie, ob er blockiert ist.
client.please.register.account.first=Bitte registrieren Sie Ihr Konto.
client.user.email.is.exits=Die E-Mail-Adresse wurde registriert
client.inverter.sn.exist=Die Wechselrichter-SN ist in Verwendung und kann nicht gelöscht werden
client.battery.serial.number.exist=Die Seriennummer des : [%s] ist vorhanden
client.battery.exit.add.sn.exist=In der geliefert nachricht ist die seriennummer der batterie hinzugefügt worden
client.ota.record.upgrading=Die Firmware der folgenden Geräte wird aktualisiert, überprüfen Sie bitte:%s
client.ota.record.already.latest=Die zum Upgrade ausgewählte Firmware ist bereits die neueste Version. Bitte überprüfen oder aktualisieren Sie die Seite:%s
client.battery.sn.is.used=Akku SN wird verwendet und kann nicht gelöscht werden
client.parameter.error.invalidDateFormat=Das Zeitformat ist falsch
client.inverter.sn.not.exists=Die folgende Seriennummer des Wechselrichters existiert nicht: %s, bitte überprüfen.
client.inverter.sn.has.used=Der folgende Inverter wird bereits verwendet: %s, kann nicht gelöscht werden.
client.battery.sn.not.exists=Die folgende Batterieseriennummer existiert nicht: %s Bitte überprüfen.
client.battery.sn.has.used=Die folgende Batterie ist bereits in Gebrauch: %s, kann nicht gelöscht werden.
client.inverter.sn.empty=Die Seriennummer des Wechselrichters darf nicht leer sein.
client.battery.sn.empty=Die Seriennummer der Batterie darf nicht leer sein.
client.import.record.not.empty=Der Importdatensatz darf nicht leer sein.
client.inverter.max.parallel.limit=Das überschreitet die leistungsgrenze des prüfgeräts.
client.inverter.bind.same.parallel=Bitte fügen Sie einen Wechselrichter mit den gleichen Spezifikationen wie der vorherige Wechselrichter hinzu.
client.inverter.latest.unbind.limit=Der server kann die entspertung nicht lösen, wenn nur ein retro-veränderer zur verfügung steht.
client.inverter.unable.unbind.limit=Der Wechselrichter kann nicht entbunden werden.
