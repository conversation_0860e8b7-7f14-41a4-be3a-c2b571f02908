/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.mapper;

import org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.skyworth.ess.ota.vo.SoftwareUpgradeRecordVO;
import org.skyworth.ess.ota.excel.SoftwareUpgradeRecordExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备软件版本升级记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
public interface SoftwareUpgradeRecordMapper extends BaseMapper<SoftwareUpgradeRecordEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param softwareUpgradeRecord
	 * @return
	 */
	List<SoftwareUpgradeRecordVO> selectSoftwareUpgradeRecordPage(IPage page, SoftwareUpgradeRecordVO softwareUpgradeRecord);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SoftwareUpgradeRecordExcel> exportSoftwareUpgradeRecord(@Param("ew") Wrapper<SoftwareUpgradeRecordEntity> queryWrapper);

	/**
	 * 插入升级记录表
	 *
	 * @param otaUpdatePackVOList 入参
	 * <AUTHOR>
	 * @since 2023/9/25 9:42
	 **/
	void batchInsert(@Param("list") List<OtaUpdatePackVO> otaUpdatePackVOList);

}
