/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.wrapper;

import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity;
import org.skyworth.ess.ota.vo.SoftwareUpgradeRecordVO;
import java.util.Objects;

/**
 * 设备软件版本升级记录表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
public class SoftwareUpgradeRecordWrapper extends BaseEntityWrapper<SoftwareUpgradeRecordEntity, SoftwareUpgradeRecordVO>  {

	public static SoftwareUpgradeRecordWrapper build() {
		return new SoftwareUpgradeRecordWrapper();
 	}

	@Override
	public SoftwareUpgradeRecordVO entityVO(SoftwareUpgradeRecordEntity softwareUpgradeRecord) {
		if (softwareUpgradeRecord == null) {
			return null;
		}
		SoftwareUpgradeRecordVO softwareUpgradeRecordVO = Objects.requireNonNull(BeanUtil.copy(softwareUpgradeRecord, SoftwareUpgradeRecordVO.class));

		//User createUser = UserCache.getUser(softwareUpgradeRecord.getCreateUser());
		//User updateUser = UserCache.getUser(softwareUpgradeRecord.getUpdateUser());
		//softwareUpgradeRecordVO.setCreateUserName(createUser.getName());
		//softwareUpgradeRecordVO.setUpdateUserName(updateUser.getName());

		return softwareUpgradeRecordVO;
	}


}
