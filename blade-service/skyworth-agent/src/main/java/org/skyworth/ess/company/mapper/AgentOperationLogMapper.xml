<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.company.mapper.AgentOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agentOperationLogResultMap" type="org.skyworth.ess.company.entity.AgentOperationLogEntity">
        <result column="id" property="id"/>
        <result column="business_id" property="businessId"/>
        <result column="operate_type" property="operateType"/>
        <result column="model_type" property="modelType"/>
        <result column="request_body" property="requestBody"/>
        <result column="response_body" property="responseBody"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>
</mapper>
