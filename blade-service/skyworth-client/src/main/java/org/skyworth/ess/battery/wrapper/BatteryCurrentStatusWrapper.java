/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.wrapper;

import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.vo.BatteryCurrentStatusVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 电池当前状态 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public class BatteryCurrentStatusWrapper extends BaseEntityWrapper<BatteryCurrentStatusEntity, BatteryCurrentStatusVO>  {

	public static BatteryCurrentStatusWrapper build() {
		return new BatteryCurrentStatusWrapper();
 	}

	@Override
	public BatteryCurrentStatusVO entityVO(BatteryCurrentStatusEntity batteryCurrentStatus) {
		if (batteryCurrentStatus == null) {
			return null;
		}
		BatteryCurrentStatusVO batteryCurrentStatusVO = Objects.requireNonNull(BeanUtil.copy(batteryCurrentStatus, BatteryCurrentStatusVO.class));
		//User createUser = UserCache.getUser(batteryCurrentStatus.getCreateUser());
		//User updateUser = UserCache.getUser(batteryCurrentStatus.getUpdateUser());
		//batteryCurrentStatusVO.setCreateUserName(createUser.getName());
		//batteryCurrentStatusVO.setUpdateUserName(updateUser.getName());

		return batteryCurrentStatusVO;
	}


}
