/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.aspect;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.config.TransactionUtil;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.utils.ThreadLocalUtil;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 操作日志拦截器
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-07 17:48
 **/
@Data
@Aspect
@Component
@Slf4j
public class FileSaveAspect {
	/**
	 * 附件信息
	 */
	private final IAttachmentInfoClient attachmentInfoClient;
	/**
	 * 图片说明
	 */
	private final IAdditionalInfoService additionalInfoService;
	/**
	 * 事务
	 */
	private final TransactionUtil transactionUtil;
	/**
	 * thread_local变量key
	 */
	private final String TRANSACTION_STATUS_KEY = "transactionStatus";

	/**
	 * 处理完请求后执行此处代码
	 */
	@Pointcut("@annotation(org.skyworth.ess.aspect.FileSave)")
	public void logPointCut() {
	}

	@Around("logPointCut()")
	public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
		//执行方法
		Object[] args = joinPoint.getArgs();
		Object result;
		try {
			ThreadLocalUtil.put(TRANSACTION_STATUS_KEY, transactionUtil.begin());
			result = joinPoint.proceed(args);
			saveFileAndFileRemark(args);
			transactionUtil.commit(ThreadLocalUtil.get(TRANSACTION_STATUS_KEY));
		}catch (BusinessException be) {
			log.error(String.format("Method exception,method name = %s, the exception information is as follows: %s", joinPoint.getSignature().getName(), be.getMessage()));
			transactionUtil.rollBack(ThreadLocalUtil.get(TRANSACTION_STATUS_KEY));
			throw be;
		} catch (Exception e) {
			log.error(String.format("Method exception,method name = %s, the exception information is as follows: %s", joinPoint.getSignature().getName(), e.getMessage()));
			transactionUtil.rollBack(ThreadLocalUtil.get(TRANSACTION_STATUS_KEY));
			throw e;
		} finally {
			ThreadLocalUtil.remove(TRANSACTION_STATUS_KEY);
		}
		return result;
	}

	/**
	 * 保存附件和附件描述
	 *
	 * @param args 方法入参
	 * <AUTHOR>
	 * @since 2023/12/6 10:19
	 **/
	private void saveFileAndFileRemark(Object[] args) {
		BatchVO<AttachmentInfoEntity> batchVO = null;
		List<AdditionalInfoEntity> list = null;
		// 方法执行完成后执行保存附件操作
		for (Object o : args) {
			if (Objects.isNull(o)) {
				continue;
			}
			Object jsonObj = JSON.toJSON(o);
			if (!JSONUtil.isTypeJSON(jsonObj.toString())) {
				continue;
			}
			JSONObject jsonObject = (JSONObject) jsonObj;
			if (jsonObject.containsKey("batchVO")) {
				JSONObject batchVoObject = jsonObject.getJSONObject("batchVO");
				batchVO = JSON.parseObject(JSON.toJSONString(batchVoObject), BatchVO.class);
			}
			if (jsonObject.containsKey("imgDescOperationList")) {
				JSONArray batchVoObject = jsonObject.getJSONArray("imgDescOperationList");
				list = JSON.parseArray(JSON.toJSONString(batchVoObject), AdditionalInfoEntity.class);
			}
		}
		// 保存附件信息
		if (batchVO != null) {
			attachmentInfoClient.saveAndUpdate(batchVO);
		}
		// 保存图片备注信息
		if (list != null) {
			additionalInfoService.saveAdditionalInfoEntityList(list);
		}
	}
}
