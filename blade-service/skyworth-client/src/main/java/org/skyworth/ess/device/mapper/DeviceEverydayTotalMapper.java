/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.mapper;

import org.skyworth.ess.device.entity.DeviceEverydayTotalEntity;
import org.skyworth.ess.device.vo.DeviceEverydayTotalVO;
import org.skyworth.ess.device.excel.DeviceEverydayTotalExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.device.vo.InverterReportQueryVO;

import java.util.List;

/**
 * 设备/逆变器每日统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface DeviceEverydayTotalMapper extends BaseMapper<DeviceEverydayTotalEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceEverydayTotal
	 * @return
	 */
	List<DeviceEverydayTotalVO> selectDeviceEverydayTotalPage(IPage page, DeviceEverydayTotalVO deviceEverydayTotal);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceEverydayTotalExcel> exportDeviceEverydayTotal(@Param("ew") Wrapper<DeviceEverydayTotalEntity> queryWrapper);

	DeviceEverydayTotalVO appFromPvAndGrid(@Param("queryCondition") DeviceEverydayTotalVO queryCondition);

	/**
	 * 逆变器日数据查询
	 *
	 * @param queryCondition 查询条件
	 * @return List<DeviceEverydayTotalEntity>
	 */
	List<DeviceEverydayTotalEntity> queryDayReport(@Param("queryCondition") InverterReportQueryVO queryCondition);

	/**
	 * 逆变器月数据查询
	 *
	 * @param queryCondition 逆变器月数据查询条件
	 * @return List<DeviceEverydayTotalEntity>
	 */
	List<DeviceEverydayTotalEntity> queryMonthReport(@Param("queryCondition") InverterReportQueryVO queryCondition);

}
