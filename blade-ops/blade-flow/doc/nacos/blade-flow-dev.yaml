spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #driver-class-name: org.postgresql.Driver
    #driver-class-name: oracle.jdbc.OracleDriver
    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    #driver-class-name: dm.jdbc.driver.DmDriver
    druid:
      # MySql、PostgreSQL、SqlServer、DaMeng校验
      validation-query: select 1
      # Oracle校验
      #validation-query: select 1 from dual

#项目模块集中配置
blade:
  #工作流模块开发生产环境数据库地址
  datasource:
    flow:
      dev:
        # MySql
        url: ****************************************************************************************************************************************************************************************************************************************************************************************
        username: root
        password: root
        # PostgreSQL
        #url: ********************************************
        #username: postgres
        #password: 123456
        # Oracle
        #url: *************************************
        #username: BLADEX_FLOW
        #password: BLADEX_FLOW
        # SqlServer
        #url: ********************************************************
        #username: bladex_flow
        #password: bladex_flow
        # DaMeng
        #url: jdbc:dm://127.0.0.1:5236/BLADEX_FLOW?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
        #username: BLADEX_FLOW
        #password: BLADEX_FLOW
