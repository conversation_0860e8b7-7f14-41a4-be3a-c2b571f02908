system.agent.agentNumber.can.not.empty=Rollout Manager ID darf nicht leer sein
system.agent.agentNumber.is.not.exits=Verteiler ID existiert nicht
system.error=Systemfehler
system.user.account.is.exits=Das Benutzerkonto ist besetzt
system.user.phone.is.exits=Die Rufnummer ist besetzt
system.user.phone.is.exits.to.another.tenant=<PERSON><PERSON> ist an ein anderes Konto gebunden. Bitte wenden Sie sich an den Systemmanager.
system.user.the.verification.code.is.error=Der Verifizierungscode ist Fehler
system.user.the.is.not.exist=Der Benutzer existiert nicht.
system.user.email.is.exits=Die E-Mail-Adresse wurde registriert
system.exception.delete.child.nodes=Entfernen sie zuerst das knotenpunkt!
system.exception.delete.child.nodes.interface=Entfernen Sie zuerst die untergeordneten Schnittstellen!
system.dict.key.value.exist=Der aktuelle krümmwert für den wörterbuch existiert bereits!
system.exception.not.resubmit=Bitte nicht erneut einreichen
system.attach.info.empty=Anhangsinformationen sind leer.
system.exception.code.execution=Eine anomalie im business-code
system.exception.cannot.select.parent.itself=Knotenpunkte wählen sich nicht selbst!
system.exception.cannot.config.superadmin.role=Keine Berechtigung zum Konfigurieren der Super-Management-Rolle!
system.exception.cannot.config.admin.role=Keine Berechtigung zum Konfigurieren der Administratorrolle!
system.exception.cannot.create.superadmin.role=Keine Berechtigung zum Erstellen einer Super-Management-Rolle
system.exception.cannot.delete.super.tenant=Sie kann nicht gelöscht werden.
system.exception.menu.name.number.exist=Dafür gibt es bereits einen menünamen und eine nummer!
system.exception.menu.parent.only.menu.type=Knotenpunkte wählen nur den menütyp aus!
system.user.password.cannot.empty=Das Passwort kann nicht leer sein
system.user.tenant.quota.max=Der aktuelle Mandant hat das maximale Kontokontingent erreicht
system.user.enter.confirm.password=Bitte geben sie das korrekte bestätigungspasswort ein!
system.user.original.password.incorrect=Der ursprüngliche code ist falsch!
system.user.tenant.info.error=Fehler bei der Mieterinformation!
system.user.third.login.error=Third-party login information error!
system.user.account.connot.delete=Dieser zugang ist nicht löschen!
system.user.account.abnormal=Das Konto des aktuellen Benutzers [{%s}] ist abnormal!
system.user.account.not.exist=Der aktuelle Benutzer [{%s}] existiert nicht!
system.user.account.is.exist=Der aktuelle Benutzer [{%s}] existiert bereits!
