/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.controller;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.excel.DeviceExitFactoryDeleteImportExcel;
import org.skyworth.ess.device.excel.DeviceExitFactoryInfoExcel;
import org.skyworth.ess.device.excel.ExitFactoryExcelColumnEnum;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.device.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.device.wrapper.DeviceExitFactoryInfoWrapper;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.excel.ExcelImportServiceInterface;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.function.BiFunction;

/**
 * 设备/逆变器出厂信息表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/deviceExitFactoryInfo")
@Api(value = "设备/逆变器出厂信息表", tags = "设备/逆变器出厂信息表接口")
public class DeviceExitFactoryInfoController extends BladeController {

	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final ExcelImportServiceInterface<DeviceExitFactoryInfoExcel> deviceExitFactoryImportImpl;
	private final ExcelImportServiceInterface<DeviceExitFactoryDeleteImportExcel> deviceExitFactoryImportDeleteImpl;
	private final BladeRedis bladeRedis;

	/**
	 * 设备/逆变器出厂信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入DeviceExitFactoryInfo")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:detail')")
	public R<DeviceExitFactoryInfoVO> detail(DeviceExitFactoryInfoEntity deviceExitFactoryInfo) {
		DeviceExitFactoryInfoEntity detail = deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(deviceExitFactoryInfo));
		return R.data(DeviceExitFactoryInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 设备/逆变器出厂信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入DeviceExitFactoryInfo")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:list')")
	public R<IPage<DeviceExitFactoryInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> deviceExitFactoryInfo, Query query) {
		query.setDescs("id");
		BladeUser userAuth = AuthUtil.getUser();
		IPage<DeviceExitFactoryInfoEntity> pages = new Page<>();
		if (userAuth.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) userAuth.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有数据
			if (roleInnerFlag != null && roleInnerFlag) {
				pages = deviceExitFactoryInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(deviceExitFactoryInfo, DeviceExitFactoryInfoEntity.class));
				pages.setRecords(pages.getRecords());
			}
		}
		return R.data(DeviceExitFactoryInfoWrapper.build().pageVO(pages));
	}


	/**
	 * 设备/逆变器出厂信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入DeviceExitFactoryInfo")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:add')")
	public R<Long> save(@Valid @RequestBody DeviceExitFactoryInfoEntity deviceExitFactoryInfo) {
		delBatchCacheKey("deviceModelProtocol*");
		BladeUser user = AuthUtil.getUser();
		deviceExitFactoryInfo.setCreateUserAccount(user.getAccount());
		DeviceExitFactoryInfoEntity entity = new DeviceExitFactoryInfoEntity();
		entity.setDeviceSerialNumber(deviceExitFactoryInfo.getDeviceSerialNumber());
		long count = deviceExitFactoryInfoService.count(Condition.getQueryWrapper(entity));
		if (count > BizConstant.NUMBER_ZERO) {
			throw new BusinessException("client.invert.serial.number.exist",deviceExitFactoryInfo.getDeviceSerialNumber());
		}
		deviceExitFactoryInfoService.save(deviceExitFactoryInfo);
		return R.data(deviceExitFactoryInfo.getId());
	}

	/**
	 * 设备/逆变器出厂信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入DeviceExitFactoryInfo")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:update')")
	public R update(@Valid @RequestBody DeviceExitFactoryInfoEntity deviceExitFactoryInfo) {
		delBatchCacheKey("deviceModelProtocol*");
		BladeUser user = AuthUtil.getUser();
		deviceExitFactoryInfo.setCreateUserAccount(user.getAccount());
		Wrapper<DeviceExitFactoryInfoEntity> queryWrapper = Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery().
			eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, deviceExitFactoryInfo.getDeviceSerialNumber())
			.ne(DeviceExitFactoryInfoEntity::getId, deviceExitFactoryInfo.getId());
		long count = deviceExitFactoryInfoService.count(queryWrapper);
		if (count > BizConstant.NUMBER_ZERO) {
			throw new BusinessException("client.invert.serial.number.exist",deviceExitFactoryInfo.getDeviceSerialNumber());
		}
		deviceExitFactoryInfo.setUpdateUserAccount(user.getAccount());
		return R.status(deviceExitFactoryInfoService.updateById(deviceExitFactoryInfo));
	}

	/**
	 * 设备/逆变器出厂信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入DeviceExitFactoryInfo")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:update')")
	public R submit(@Valid @RequestBody DeviceExitFactoryInfoEntity deviceExitFactoryInfo) {
		delBatchCacheKey("deviceModelProtocol*");
		return R.status(deviceExitFactoryInfoService.saveOrUpdate(deviceExitFactoryInfo));
	}

	/**
	 * 设备/逆变器出厂信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestBody DeviceExitFactoryInfoVO deviceExitFactoryInfoVO) {
		delBatchCacheKey("deviceModelProtocol*");
		return deviceExitFactoryInfoService.deleteLogicDeviceExitFactory(Func.toLongList(deviceExitFactoryInfoVO.getIds()));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-DeviceExitFactoryInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入DeviceExitFactoryInfo")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:export')")
	public void exportDeviceExitFactoryInfo(@ApiIgnore @RequestParam Map<String, Object> deviceExitFactoryInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DeviceExitFactoryInfoEntity> queryWrapper = Condition.getQueryWrapper(deviceExitFactoryInfo, DeviceExitFactoryInfoEntity.class);
		queryWrapper.lambda().eq(DeviceExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DeviceExitFactoryInfoExcel> list = deviceExitFactoryInfoService.exportDeviceExitFactoryInfo(queryWrapper);
		ExcelUtil.export(response, "inverterExitFactoryInfo" + DateUtil.time(), "inverterExitFactoryInfo", list, DeviceExitFactoryInfoExcel.class);
	}

	/**
	 * 导出模板
	 */
	@GetMapping("/export-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导出模板")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:export')")
	public void exportImportantEventTemplate(HttpServletResponse response) {
		List<DeviceExitFactoryInfoExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "template", "inverterExitFactoryInfo", list, DeviceExitFactoryInfoExcel.class);
	}

	/**
	 * 导入
	 */
	@PostMapping("/import-add-dviceExitFactory")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "新增导入出厂信息", notes = "传入excel")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:import')")
	public R importAddDeviceExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<DeviceExitFactoryInfoExcel>, Boolean, String> fun = deviceExitFactoryInfoService::importAddExcel;
		String result = deviceExitFactoryImportImpl.importExcel(file, ExitFactoryExcelColumnEnum.getColumn("en"),
			DeviceExitFactoryInfoExcel.class, fun);
		if (StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}

	/**
	 * 导入
	 */
	@PostMapping("/import-modify-dviceExitFactory")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "修改导入出厂信息", notes = "传入excel")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:import')")
	public R importModifyDeviceExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<DeviceExitFactoryInfoExcel>, Boolean, String> fun = deviceExitFactoryInfoService::importModifyExcel;
		String result = deviceExitFactoryImportImpl.importExcel(file, ExitFactoryExcelColumnEnum.getColumn("en"),
			DeviceExitFactoryInfoExcel.class, fun);
		if (StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}

	private void delBatchCacheKey(String keyPrefix){
		Set<String> allAddressMapDefinitionByAddress = bladeRedis.keys(keyPrefix);
		bladeRedis.del(allAddressMapDefinitionByAddress);
	}

	/**
	 * 导出批量删除SN模板
	 */
	@GetMapping("/export-delete-template")
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "导出模板")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:export')")
	public void exportDeleteTemplate(HttpServletResponse response) {
		List<DeviceExitFactoryDeleteImportExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "Device_delete_factory_info_template",
			"InverterDeleteFactoryInfo", list,
			DeviceExitFactoryDeleteImportExcel.class);
	}

	@PostMapping("/import-delete-deviceExitFactory")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "新增导入删除出厂信息", notes = "传入excel")
	@PreAuth("hasPermission('client:deviceExitFactoryInfo:import')")
	public R importDeleteDeviceExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<DeviceExitFactoryDeleteImportExcel>, Boolean, String> fun =
			deviceExitFactoryInfoService::importDeleteExcel;
		Set<String> columnSet = new HashSet<>(1);
		columnSet.add("Inverter SN");
		String result = deviceExitFactoryImportDeleteImpl.importExcel(file, columnSet,
			DeviceExitFactoryDeleteImportExcel.class, fun);
		if (StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}
}
