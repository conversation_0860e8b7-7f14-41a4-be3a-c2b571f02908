<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.ehssubmission.toolsmachinery.mapper.ToolsMachineryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="toolsMachineryResultMap" type="org.skyworth.ess.ehssubmission.toolsmachinery.entity.ToolsMachineryEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="tools_machinery_type" property="toolsMachineryType"/>
        <result column="other_machinery_name" property="otherMachineryName"/>
        <result column="tools_machinery_img_biz_key" property="toolsMachineryImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectToolsMachineryPage" resultMap="toolsMachineryResultMap">
        select * from ehs_tools_machinery where is_deleted = 0
    </select>


    <select id="exportToolsMachinery" resultType="org.skyworth.ess.ehssubmission.toolsmachinery.excel.ToolsMachineryExcel">
        SELECT * FROM ehs_tools_machinery ${ew.customSqlSegment}
    </select>

</mapper>
