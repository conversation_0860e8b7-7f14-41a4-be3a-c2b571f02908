/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.vo.Device24VO;
import java.util.Objects;

/**
 * Device24 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public class Device24Wrapper extends BaseEntityWrapper<Device24Entity, Device24VO>  {

	public static Device24Wrapper build() {
		return new Device24Wrapper();
 	}

	@Override
	public Device24VO entityVO(Device24Entity Device24) {
		Device24VO Device24VO = Objects.requireNonNull(BeanUtil.copy(Device24, Device24VO.class));

		//User createUser = UserCache.getUser(Device24.getCreateUser());
		//User updateUser = UserCache.getUser(Device24.getUpdateUser());
		//Device24VO.setCreateUserName(createUser.getName());
		//Device24VO.setUpdateUserName(updateUser.getName());

		return Device24VO;
	}


}
