/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.device.entity.DeviceLog24Entity;

import java.util.HashMap;
import java.util.List;

/**
 * DeviceLog24 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface DeviceLog24Mapper extends BaseMapper<DeviceLog24Entity> {
	List<DeviceLog24Entity> selectDataByLatestTime();

    HashMap<String, Object> getAllQuickSetup(@Param("plantId") Long plantId,@Param("deviceSerialNumber") String deviceSerialNumber);
    HashMap<String, Object> getAllQuickSetupIsDelete(@Param("plantId") Long plantId,@Param("deviceSerialNumber") String deviceSerialNumber);

//	/**
//	 * 自定义分页
//	 *
//	 * @param page
//	 * @param DeviceLog24
//	 * @return
//	 */
//	List<DeviceLog24VO> selectDeviceLog24Page(IPage page, DeviceLog24VO DeviceLog24);
//
//
//	/**
//	 * 获取导出数据
//	 *
//	 * @param queryWrapper
//	 * @return
//	 */
//	List<DeviceLog24Excel> exportDeviceLog24(@Param("ew") Wrapper<DeviceLog24Entity> queryWrapper);

}
