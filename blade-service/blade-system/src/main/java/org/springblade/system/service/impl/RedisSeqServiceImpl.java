package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.system.entity.RedisSeqEnums;
import org.springblade.system.entity.RedisSequence;
import org.springblade.system.mapper.RedisSeqMapper;
import org.springblade.system.service.IRedisSeqService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * redis唯一id
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-13 16:59
 **/
@Service
@AllArgsConstructor
public class RedisSeqServiceImpl extends ServiceImpl<RedisSeqMapper, RedisSequence> implements IRedisSeqService {
	private BladeRedis bladeRedis;
	private static final int SECTION_WIDTH = 1000;

	@Override
	@RedisLock(value = "lock:system:sequence")
	public String getRedisUniqueId(RedisSeqEnums redisSeqEnums) {
		String bizTag = redisSeqEnums.getBizTag();
		String id = bladeRedis.lPop(CommonConstant.SKYWORTH_SEQUENCE_PREFIX + bizTag);
		if (id == null) {
			Long current = 0L;
			RedisSequence seq = null;
			//二次 判断
			id = bladeRedis.lPop(CommonConstant.SKYWORTH_SEQUENCE_PREFIX + bizTag);
			if (id != null) {
				return id;
			}
			//从数据库获取seq
			seq = baseMapper.selectById(bizTag);
			if (seq == null) {
				seq = new RedisSequence();
				seq.setCurrentId(1L);
				seq.setBizTag(bizTag);
				seq.setPrefixAbc(redisSeqEnums.getPrefixAbc());
				seq.setLength(redisSeqEnums.getLength());
				seq.setRefresh(redisSeqEnums.getRefresh());
				seq.setPrefixDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern(CommonConstant.SKYWORTH_SEQUENCE_DATE_FORMAT)));
				baseMapper.insert(seq);
			}
			current = seq.getCurrentId();
			seq.setCurrentId(seq.getCurrentId() + SECTION_WIDTH);
			baseMapper.updateById(seq);
			//存入redis缓存
			cache(bizTag, seq, current);
			id = bladeRedis.lPop(CommonConstant.SKYWORTH_SEQUENCE_PREFIX + bizTag);
		}
		return id;
	}

	/**
	 * 存入redis缓存
	 */
	private void cache(String bizTag, RedisSequence seq, Long current) {
		for (long i = current; i < current + SECTION_WIDTH; i++) {
			bladeRedis.rPush(CommonConstant.SKYWORTH_SEQUENCE_PREFIX + bizTag, seq.getPrefixAbc() + seq.getPrefixDate() + addZeroToSeq(i, seq.getLength()));
		}
	}

	/**
	 * 在前面加0
	 */
	private String addZeroToSeq(long currentId, int length) {
		String c = String.valueOf(currentId);
		length = length - c.length();
		if (length > 0) {
			for (int i = 0; i < length; i++) {
				c = "0" + c;
			}
		}
		return c;
	}


	@Override
	public void clearRedisSequence() {
		String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(CommonConstant.SKYWORTH_SEQUENCE_DATE_FORMAT));
		List<RedisSequence> list = baseMapper.selectList(new QueryWrapper<>());
		if (list != null && !list.isEmpty()) {
			list.forEach(v -> {
				v.setCurrentId(1L);
				v.setPrefixDate(dateStr);
				baseMapper.updateById(v);
			});
		}
		//清空redis缓存的流水号
		for (RedisSeqEnums value : RedisSeqEnums.values()) {
			bladeRedis.lTrim(CommonConstant.SKYWORTH_SEQUENCE_PREFIX + value.getBizTag(), 1, 0);
		}
	}


}
