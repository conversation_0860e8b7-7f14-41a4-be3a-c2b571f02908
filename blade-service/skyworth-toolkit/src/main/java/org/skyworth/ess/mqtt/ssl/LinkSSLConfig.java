package org.skyworth.ess.mqtt.ssl;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.security.Security;

public class LinkSSLConfig implements Serializable {

    private String certKey;
    private String caFile;
    private String certFile;
    private String keyFile;

    public LinkSSLConfig(String caFile, String certFile, String keyFile, String certKey) {
        this.caFile = caFile;
        this.certFile = certFile;
        this.keyFile = keyFile;
        this.certKey = certKey;
    }

    public SSLContext getSSLContext() throws Exception {
        try(InputStream caInputStream = Files.newInputStream(Paths.get(caFile));
            InputStream keyInputStream = Files.newInputStream(Paths.get(keyFile));
            InputStream certInputStream = Files.newInputStream(Paths.get(certFile))) {
            Security.addProvider(new BouncyCastleProvider());

            int caCount = caInputStream.available();
            byte[] caBytes = new byte[caCount];

            while (true) {
                int read = caInputStream.read(caBytes);;
                if (read == -1) {
                    break;
                }
            }

            int keyCount = keyInputStream.available();
            byte[] keyBytes = new byte[keyCount];
            while (true) {
                int read = keyInputStream.read(keyBytes);
                if (read == -1) {
                    break;
                }
            }

            int certCount = certInputStream.available();
            byte[] certBytes = new byte[certCount];
            while (true) {
                int read = certInputStream.read(certBytes);
                if (read == -1) {
                    break;
                }
            }

            String capem = new String(caBytes);
            String keypem = new String(keyBytes);
            String certpem = new String(certBytes);

            String kmfAlgorithm = AccessController.doPrivileged(this.getSystemProperty("ssl.keyManagerFactory.algorithm", KeyManagerFactory.getDefaultAlgorithm()));
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(kmfAlgorithm);
            keyManagerFactory.init(LinkSSLUtils.createKeyStore(keypem, certpem, this.certKey), this.certKey.toCharArray());
            String tmfAlgorithm = AccessController.doPrivileged(this.getSystemProperty("ssl.trustManagerFactory.algorithm", TrustManagerFactory.getDefaultAlgorithm()));
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(tmfAlgorithm);
            trustManagerFactory.init(LinkSSLUtils.createTrustStore(capem));
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(keyManagerFactory.getKeyManagers(), trustManagerFactory.getTrustManagers(), null);
            return sslContext;
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    private PrivilegedAction<String> getSystemProperty(final String name, final String def) {
        return () -> System.getProperty(name, def);
    }

    public SSLSocketFactory getSSLSocketFactory() throws Exception {
        return getSSLContext().getSocketFactory();
    }



}
