/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.ota.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-27 10:28
 **/
@Data
@ApiModel(value = "FileModelVO对象", description = "附件上传回写页面参数")
public class FileModelVO implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "附件地址,不需要sas签名")
	private String fileUrl;
	@ApiModelProperty(value = "附件名称")
	private String fileName;
	@ApiModelProperty(value = "缩略图地址，针对图片")
	private String thumbnailUrl;
	@ApiModelProperty(value = "附件大小")
	private Long attachSize;
	@ApiModelProperty(value = "附件在blob的路径(删除附件以及需要sas签名下载附件的时候使用)")
	private String azureKey;
	@ApiModelProperty(value = "缩略图在blob的路径(删除附件以及需要sas签名下载附件的时候使用)")
	private String thumbnailAzureKey;
	@ApiModelProperty(value = "容器container")
	private String container;
	@ApiModelProperty(value = "业务主键id")
	private Long businessId;
	@ApiModelProperty(value = "原图sas签名")
	private String sasToken;
	@ApiModelProperty(value = "缩略图sas签名")
	private String thumbnailSasToken;
	@ApiModelProperty(value = "校验和")
	private Long checkSum;
}
