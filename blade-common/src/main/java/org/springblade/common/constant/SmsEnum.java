package org.springblade.common.constant;

import lombok.Getter;
import org.springblade.common.utils.tool.ValidationUtil;

/**
 * <AUTHOR>
 */
@Getter
public enum SmsEnum {
	// 2024-4-12 注释原因：欧洲版的短信签名公司使用Strong集团，非Skyworth，所以短信模板ID不同
//	// 登录
//	ONE("1", "5808024", "5877480"),
//	// 注册
//	TWO("2", "5808022", "5877478"),
//	// 重置密码
//	THREE("3", "5808320", "5877474"),
//	// 更新手机
//	FOUR("4", "5813870", "5877484"),
//	// 注销账号
//	FIVE("5", "5814628", "5877482","5910592");



	// 登录
	ONE("1", "5910674", "5808024","5910584"),
	// 注册
	TWO("2", "5910672", "5808022","5910582"),
	// 重置密码
	THREE("3", "5910588", "5808320","5910878"),
	// 更新手机
	FOUR("4", "5910676", "5813870","5910586"),
	// 注销账号
	FIVE("5", "5910680", "5814628","5910592"),
	// 更改站点所属用户
	SIX("6", "6254740", "6254742","6254742"),
	// 后台自动注册用户发送
	SEVEN("7", "6273360", "6273738","6273738");

	final String smsType;
	final String smsTemplateEn;
	final String smsTemplateCn;
	final String smsTemplateDe;

	SmsEnum(String smsType, String smsTemplateEn, String smsTemplateCn,String smsTemplateDe) {
		this.smsType = smsType;
		this.smsTemplateEn = smsTemplateEn;
		this.smsTemplateCn = smsTemplateCn;
		this.smsTemplateDe = smsTemplateDe;
	}

	public static String getSmsTemplate(String smsType, String language) {
		SmsEnum[] values = SmsEnum.values();
		for (SmsEnum smsEnum : values) {
			if (smsEnum.smsType.equals(smsType)) {
				if (CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
					return smsEnum.smsTemplateCn;
				} else if (CommonConstant.CURRENT_LANGUAGE_DE.equalsIgnoreCase(language)) {
					return smsEnum.smsTemplateDe;
				} else {
					return smsEnum.smsTemplateEn;
				}
			}
		}
		return "";
	}
}
