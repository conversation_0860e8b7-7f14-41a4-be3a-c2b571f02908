/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.excel.DeviceSoftwareVersionInfoExcel;
import org.skyworth.ess.ota.service.IDeviceSoftwareVersionInfoService;
import org.skyworth.ess.ota.vo.DeviceSoftwareVersionInfoVO;
import org.skyworth.ess.ota.wrapper.DeviceSoftwareVersionInfoWrapper;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备软件版本信息表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@AllArgsConstructor
@RequestMapping("/deviceSoftwareVersionInfo")
@Api(value = "设备软件版本信息表", tags = "设备软件版本信息表接口")
public class DeviceSoftwareVersionInfoController extends BladeController {
	private final IDeviceSoftwareVersionInfoService deviceSoftwareVersionInfoService;

	/**
	 * 设备软件版本信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:detail')")
	public R<DeviceSoftwareVersionInfoVO> detail(DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo) {
		DeviceSoftwareVersionInfoEntity detail = deviceSoftwareVersionInfoService.getOne(Condition.getQueryWrapper(deviceSoftwareVersionInfo));
		return R.data(DeviceSoftwareVersionInfoWrapper.build().entityVO(detail));
	}


	/**
	 * 设备软件版本信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:list')")
	public R<IPage<DeviceSoftwareVersionInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> deviceSoftwareVersionInfo, Query query) {
		return deviceSoftwareVersionInfoService.queryList(deviceSoftwareVersionInfo,query);
	}


	/**
	 * 设备软件版本信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:add')")
	public R save(@Valid @RequestBody DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo) {
		return R.status(deviceSoftwareVersionInfoService.save(deviceSoftwareVersionInfo));
	}

	/**
	 * 设备软件版本信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:update')")
	public R update(@Valid @RequestBody DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo) {
		return R.status(deviceSoftwareVersionInfoService.updateById(deviceSoftwareVersionInfo));
	}

	/**
	 * 设备软件版本信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:update')")
	public R submit(@Valid @RequestBody DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo) {
		return R.status(deviceSoftwareVersionInfoService.saveOrUpdate(deviceSoftwareVersionInfo));
	}

	/**
	 * 设备软件版本信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceSoftwareVersionInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-deviceSoftwareVersionInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:export')")
	public void exportDeviceSoftwareVersionInfo(@ApiIgnore @RequestParam Map<String, Object> deviceSoftwareVersionInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DeviceSoftwareVersionInfoEntity> queryWrapper = Condition.getQueryWrapper(deviceSoftwareVersionInfo, DeviceSoftwareVersionInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(DeviceSoftwareVersionInfo::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(DeviceSoftwareVersionInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DeviceSoftwareVersionInfoExcel> list = deviceSoftwareVersionInfoService.exportDeviceSoftwareVersionInfo(queryWrapper);
		ExcelUtil.export(response, "设备软件版本信息表数据" + DateUtil.time(), "设备软件版本信息表数据表", list, DeviceSoftwareVersionInfoExcel.class);
	}

	/**
	 * 设备软件版本信息表 详情
	 */
	@PostMapping("/retry")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "手动升级", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:ota')")
	public R retry(@RequestBody List<Long> deviceSoftwareVersionIds) {
		if (CollectionUtils.isNullOrEmpty(deviceSoftwareVersionIds)) {
			throw new BusinessException("client.parameter.error.record.empty");
		}
		deviceSoftwareVersionInfoService.retry(deviceSoftwareVersionIds);
		return R.status(true);
	}

	/**
	 * 推送ota升级包
	 */
	@GetMapping("/pushPage")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:ota')")
	public R circularProcessingUpgradeRecords() {
		deviceSoftwareVersionInfoService.circularProcessingUpgradeRecords();
		return R.status(true);
	}

	/**
	 * 处理超时升级包,手动推送
	 *
	 * @return R
	 * <AUTHOR>
	 * @since 2023/9/26 15:16
	 **/
	@GetMapping("/processingTimeoutStatus")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceSoftwareVersionInfo")
	@PreAuth("hasPermission('client:deviceSoftwareVersionInfo:ota')")
	public R processingTimeoutStatus() {
		deviceSoftwareVersionInfoService.processingTimeoutStatus();
		return R.status(true);
	}
}
