/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.common.base.Joiner;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.i18n.util.I18nUtil;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.resource.feign.ISmsClient;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.Role;
import org.springblade.system.entity.User;
import org.springblade.system.excel.UserExcel;
import org.springblade.system.excel.UserImporter;
import org.springblade.system.service.IDeptService;
import org.springblade.system.service.IUserService;
import org.springblade.system.vo.UserInfoVO;
import org.springblade.system.vo.UserVO;
import org.springblade.system.wrapper.UserWrapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.core.cache.constant.CacheConstant.USER_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@RequestMapping("/user")
@Api(value = "用户", tags = "用户")
@AllArgsConstructor
@Slf4j
public class UserController {

	private final IUserService userService;
	private final BladeRedis bladeRedis;
	private final IDeptService deptService;

	/**
	 * 查询单条
	 */
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查看详情", notes = "传入id")
	@GetMapping("/detail")
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)  提供此接口给app使用，获取人员信息
	public R<UserVO> detail(User user) {
		User detail = userService.getOne(Condition.getQueryWrapper(user));
		if (CommonConstant.CLIENT_TENANT_ID.equals(detail.getTenantId())) {
			String deptIds = detail.getDeptId();
			Set<String> agentDept;
			if (StringUtil.isNotBlank(deptIds)) {
				// 将 client的部门移除，只保留 agent部门id
				LambdaQueryWrapper<Dept> queryDept = Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, CommonConstant.CLIENT_TENANT_ID);
				List<Dept> clientDeptlist = deptService.list(queryDept);
				Set<String> clientDeptIdSet = clientDeptlist.stream().map(p -> String.valueOf(p.getId())).collect(Collectors.toSet());
				String[] userDeptIdArray = deptIds.split(",");
				agentDept = Arrays.stream(userDeptIdArray)
					.collect(Collectors.toSet());
				agentDept.removeAll(clientDeptIdSet);
				detail.setAgentDeptIdInClient(Joiner.on(",").join(agentDept));
			}
		}
		return R.data(UserWrapper.build().entityVO(detail));
	}

	/**
	 * 查询单条
	 */
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查看详情", notes = "传入id")
	@GetMapping("/info")
	public R<UserVO> info(BladeUser user) {
		User detail = userService.getById(user.getUserId());
		if (detail == null) {
			throw new BusinessException("system.user.the.is.not.exist");
		} else {
			return R.data(UserWrapper.build().entityVO(detail));
		}
	}

	/**
	 * 用户列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "account", value = "账号名", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "realName", value = "姓名", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入account和realName")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R<IPage<UserVO>> list(@ApiIgnore @RequestParam Map<String, Object> user, Query query, BladeUser bladeUser) {
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
		IPage<User> pages = userService.page(Condition.getPage(query), (!bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(User::getTenantId, bladeUser.getTenantId()) : queryWrapper);
		return R.data(UserWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义用户列表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "account", value = "账号名", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "realName", value = "姓名", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入account和realName")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R<IPage<UserVO>> page(@ApiIgnore User user, Query query, Long deptId, BladeUser bladeUser) {
		IPage<User> pages = userService.selectUserPage(Condition.getPage(query), user, deptId, (bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY : bladeUser.getTenantId()));
		return R.data(UserWrapper.build().pageVO(pages));
	}


	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增或修改", notes = "传入User")
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@ApiLog("用户新增或修改")
	public R submit(@Valid @RequestBody User user) {
		CacheUtil.clear(USER_CACHE);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		try {
			userService.submit(user);
			return R.success(I18nUtil.getString("system.tools.success"));
		} catch (Exception e) {
			R r = new R();
			if (e instanceof ServiceException) {
				if ("the password can not empty".equalsIgnoreCase(e.getMessage())) {
					r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100004.autoGetMessage(currentLanguage));
				}
				if ("theEmailIsError".equalsIgnoreCase(e.getMessage())) {
					r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100005.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100005.autoGetMessage(currentLanguage));
				}
				if ("theVerifiactionCodeIsError".equalsIgnoreCase(e.getMessage())) {
					r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				}
				if ("TheUserAlreadyExists".equalsIgnoreCase(e.getMessage())) {
					r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100007.getCode());
					r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_SYSTEM_USER_100007.autoGetMessage(currentLanguage), user.getAccount()));
				}
				if ("thePhoneIsError".equalsIgnoreCase(e.getMessage())) {
					r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100100.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100100.autoGetMessage(currentLanguage));
				}
				if ("the agentNumber can not empty.".equalsIgnoreCase(e.getMessage())) {
					r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100133.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100133.autoGetMessage(currentLanguage));
				}
				if ("The agent code entered is incorrect. Please re-enter it!".equalsIgnoreCase(e.getMessage())) {
					r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100134.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100134.autoGetMessage(currentLanguage));
				}
			} else if (e instanceof BusinessException) {
				throw e;
			} else {
				r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.autoGetMessage(currentLanguage));
			}
			return r;
		}
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入User")
	public R update(@Valid @RequestBody User user) {
		CacheUtil.clear(USER_CACHE);
		return R.status(userService.updateUser(user));
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "删除", notes = "传入id集合")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R remove(@RequestParam String ids) {
		CacheUtil.clear(USER_CACHE);
		return R.status(userService.removeUser(ids));
	}

	/**
	 * 设置菜单权限
	 */
	@PostMapping("/grant")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "权限设置", notes = "传入roleId集合以及menuId集合")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R grant(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds,
				   @ApiParam(value = "roleId集合", required = true) @RequestParam String roleIds) {
		boolean temp = userService.grant(userIds, roleIds);
		return R.status(temp);
	}

	/**
	 * 密码重制
	 */
	@PostMapping("/reset-password")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "初始化密码", notes = "传入userId集合")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R resetPassword(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds) {
		boolean temp = userService.resetPassword(userIds);
		return R.status(temp);
	}

	/**
	 * 修改密码
	 */
	@PostMapping("/update-password")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "修改密码", notes = "传入密码")
	public R updatePassword(BladeUser user, @ApiParam(value = "旧密码", required = true) @RequestParam String oldPassword,
							@ApiParam(value = "新密码", required = true) @RequestParam String newPassword,
							@ApiParam(value = "新密码", required = true) @RequestParam String newPassword1) {
		boolean temp = userService.updatePassword(user.getUserId(), oldPassword, newPassword, newPassword1);
		return R.status(temp);
	}

	/**
	 * 修改密码
	 */
	@PostMapping("/app/update-password")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "修改密码", notes = "传入密码")
	public R<String> appUpdatePassword(@RequestBody UserInfoVO userInfoVO) {
		BladeUser user = AuthUtil.getUser();
		return userService.appUpdatePassword(user.getUserId(), userInfoVO.getOldPassword(),
			userInfoVO.getNewPassword(), userInfoVO.getConfirmPassword());
	}

	/**
	 * 忘记密码
	 */
	@PostMapping("/forgetPassword")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "忘记密码", notes = "传入新密码")
	@ApiLog("用户忘记密码")
	public R forgetPassword(@RequestBody UserInfoVO userInfoVO
	) {
		return userService.forgetPassword(userInfoVO);
	}

	/**
	 * app修改手机号
	 */
	@PostMapping("/app/update-phone")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "app修改用户手机", notes = "传入User")
	@ApiLog("app修改用户手机")
	public R appUpdatePhone(@Valid @RequestBody User user) {
		return userService.appUpdatePhone(user);
	}

	@PostMapping("/app/update-email")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "app修改用户邮箱", notes = "传入User")
	@ApiLog("app修改用户邮箱")
	public R appUpdateEmail(@Valid @RequestBody User user) {
		return userService.appUpdateEmail(user);
	}

	/**
	 * app修改用户基本信息
	 */
	@PostMapping("/app/update-user")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "app修改用户基本信息", notes = "传入User")
	@ApiLog("app修改用户基本信息")
	public R appUpdateUser(@Valid @RequestBody User user) {
		return userService.appUpdateUser(user);
	}

	/**
	 * 修改基本信息
	 */
	@PostMapping("/update-info")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "修改基本信息", notes = "传入User")
	@ApiLog("用户修改基本信息")
	public R updateInfo(@Valid @RequestBody User user) {
		CacheUtil.clear(USER_CACHE);
		return R.status(userService.updateUserInfo(user));
	}

	/**
	 * 用户列表
	 */
	@GetMapping("/user-list")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "用户列表", notes = "传入user")
	public R<List<User>> userList(User user, BladeUser bladeUser) {
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user);
		List<User> list = userService.list((!AuthUtil.isAdministrator()) ? queryWrapper.lambda().eq(User::getTenantId, bladeUser.getTenantId()) : queryWrapper);
		return R.data(list);
	}

	/**
	 * 导入用户
	 */
	@PostMapping("import-user")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "导入用户", notes = "传入excel")
	public R importUser(MultipartFile file, Integer isCovered) {
		UserImporter userImporter = new UserImporter(userService, isCovered == 1);
		ExcelUtil.save(file, userImporter, UserExcel.class);
		return R.success("操作成功");
	}

	/**
	 * 导出用户
	 */
	@GetMapping("export-user")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "导出用户", notes = "传入user")
	public void exportUser(@ApiIgnore @RequestParam Map<String, Object> user, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
		if (!AuthUtil.isAdministrator()) {
			queryWrapper.lambda().eq(User::getTenantId, bladeUser.getTenantId());
		}
		queryWrapper.lambda().eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<UserExcel> list = userService.exportUser(queryWrapper);
		ExcelUtil.export(response, "用户数据" + DateUtil.time(), "用户数据表", list, UserExcel.class);
	}

	/**
	 * 导出模板
	 */
	@GetMapping("export-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导出模板")
	public void exportUser(HttpServletResponse response) {
		List<UserExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "用户数据模板", "用户数据表", list, UserExcel.class);
	}


	/**
	 * 第三方注册用户
	 */
	@PostMapping("/register-guest")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "第三方注册用户", notes = "传入user")
	public R registerGuest(User user, Long oauthId) {
		return R.status(userService.registerGuest(user, oauthId));
	}


	/**
	 * 配置用户平台信息
	 */
	@PostMapping("/update-platform")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "配置用户平台信息", notes = "传入user")
	public R updatePlatform(Long userId, Integer userType, String userExt) {
		return R.status(userService.updatePlatform(userId, userType, userExt));
	}

	/**
	 * 查看平台详情
	 */
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "查看平台详情", notes = "传入id")
	@GetMapping("/platform-detail")
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R<UserVO> platformDetail(User user) {
		return R.data(userService.platformDetail(user));
	}


	/**
	 * 用户解锁
	 */
	@PostMapping("/unlock")
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "账号解锁", notes = "传入id")
	public R unlock(String userIds) {
		if (StringUtil.isBlank(userIds)) {
			return R.fail("请至少选择一个用户");
		}
		List<User> userList = userService.list(Wrappers.<User>lambdaQuery().in(User::getId, Func.toLongList(userIds)));
		userList.forEach(user -> bladeRedis.del(CacheNames.tenantKey(user.getTenantId(), CacheNames.USER_FAIL_KEY, user.getAccount())));
		return R.success("操作成功");
	}


	/**
	 * 根据角色名称和用户名称查询包含该角色的用户信息
	 */
	@PostMapping("/findUserInfoByRoleName")
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "查询用户信息", notes = "传入角色名称")
	public R findUserInfoByRoleName(@RequestBody User user) {
		List<User> userList = userService.findUserInfoByRoleName(user);
		return R.data(userList);
	}


	/**
	 * 根据用户id查询该用户所具有的角色信息
	 */
	@GetMapping("/findUserRoleInfoByUserId")
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "查询用户角色信息", notes = "传入用户id")
	public R<List<Role>> findUserRoleInfoByUserId(@RequestParam("userId") String userId) {
		return userService.findUserRoleInfoByUserId(userId);
	}

	@PostMapping("/append-role")
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "用户追加角色", notes = "传入角色id")
	public R<String> appendRole(@RequestBody List<UserInfoVO> roleList) {
		return userService.appendRole(roleList);
	}

	@PostMapping("/phone-delete-user")
	@ApiOperationSupport(order = 21)
	@ApiOperation(value = "注销，删除用户", notes = "传入角色id")
	public R<String> phoneDeleteUser(@RequestBody UserInfoVO userInfoVO) {
		return userService.phoneDeleteUser(userInfoVO);
	}

	/**
	 * 角色对应的用户
	 */
	@GetMapping("/role-user")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "角色对应的用户", notes = "角色id")
	public R<IPage<User>> roleUser(@RequestParam("size") int size, @RequestParam("current") int current,
								   @RequestParam("roleId") String roleId) {
		Query query = new Query();
		query.setCurrent(current);
		query.setSize(size);
		IPage<User> page = Condition.getPage(query);
		page.setRecords(new ArrayList<>());
		if (Func.isEmpty(roleId)) {
			return R.data(page);
		}
		return R.data(userService.roleUser(query, roleId));
	}
}
