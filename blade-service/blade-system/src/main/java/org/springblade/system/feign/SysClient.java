/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.*;
import org.springblade.system.service.*;
import org.springblade.system.vo.ChangeUserVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static org.springblade.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * 系统服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class SysClient implements ISysClient {

	private final IDeptService deptService;

	private final IPostService postService;

	private final IRoleService roleService;

	private final IUserService iUserService;

	private final IMenuService menuService;

	private final ITenantService tenantService;

	private final ITenantPackageService tenantPackageService;

	private final IParamService paramService;

	private final IRegionService regionService;

	private final IRedisSeqService redisSeqService;

	private final IDictBizService dictBizService;

	@Override
	@GetMapping(MENU)
	public R<Menu> getMenu(Long id) {
		return R.data(menuService.getById(id));
	}

	@Override
	@GetMapping(DEPT)
	public R<Dept> getDept(Long id) {
		return R.data(deptService.getById(id));
	}

	@Override
	public R<String> getDeptIds(String tenantId, String deptNames) {
		return R.data(deptService.getDeptIds(tenantId, deptNames));
	}

	@Override
	public R<String> getDeptIdsByFuzzy(String tenantId, String deptNames) {
		return R.data(deptService.getDeptIdsByFuzzy(tenantId, deptNames));
	}

	@Override
	@GetMapping(DEPT_NAME)
	public R<String> getDeptName(Long id) {
		return R.data(deptService.getById(id).getDeptName());
	}

	@Override
	@GetMapping(DEPT_NAMES)
	public R<List<String>> getDeptNames(String deptIds) {
		return R.data(deptService.getDeptNames(deptIds));
	}

	@Override
	@GetMapping(DEPT_CHILD)
	public R<List<Dept>> getDeptChild(Long deptId) {
		return R.data(deptService.getDeptChild(deptId));
	}

	@Override
	@PostMapping(DEPT_ADD)
	public R<Boolean> submit(Dept dept) {
		boolean status = deptService.submit(dept);
		if (status) {
			CacheUtil.clear(SYS_CACHE);
			CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
		}
		return R.data(status);
	}

	@Override
	@GetMapping(DEPT_REMOVE)
	public R<Boolean> removeDept(String ids) {
		boolean status = deptService.removeDept(ids);
		if (status) {
			CacheUtil.clear(SYS_CACHE);
			CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
		}
		return R.status(status);
	}

	@Override
	public R<Post> getPost(Long id) {
		return R.data(postService.getById(id));
	}

	@Override
	public R<String> getPostIds(String tenantId, String postNames) {
		return R.data(postService.getPostIds(tenantId, postNames));
	}

	@Override
	public R<String> getPostIdsByFuzzy(String tenantId, String postNames) {
		return R.data(postService.getPostIdsByFuzzy(tenantId, postNames));
	}

	@Override
	public R<String> getPostName(Long id) {
		return R.data(postService.getById(id).getPostName());
	}

	@Override
	public R<List<String>> getPostNames(String postIds) {
		return R.data(postService.getPostNames(postIds));
	}

	@Override
	@GetMapping(ROLE)
	public R<Role> getRole(Long id) {
		return R.data(roleService.getById(id));
	}

	@Override
	public R<String> getRoleIds(String tenantId, String roleNames) {
		return R.data(roleService.getRoleIds(tenantId, roleNames));
	}

	@Override
	@GetMapping(ROLE_NAME)
	public R<String> getRoleName(Long id) {
		return R.data(roleService.getById(id).getRoleName());
	}

	@Override
	@GetMapping(ROLE_ALIAS)
	public R<String> getRoleAlias(Long id) {
		return R.data(roleService.getById(id).getRoleAlias());
	}

	@Override
	@GetMapping(ROLE_NAMES)
	public R<List<String>> getRoleNames(String roleIds) {
		return R.data(roleService.getRoleNames(roleIds));
	}

	@Override
	@GetMapping(ROLE_ALIASES)
	public R<List<String>> getRoleAliases(String roleIds) {
		return R.data(roleService.getRoleAliases(roleIds));
	}

	@Override
	@GetMapping(ROLE_ID_BY_CODE)
	public R<Role> getRoleInfoByCode(String roleCode) {
		return R.data(roleService.getRoleInfoByCode(roleCode));
	}


	@Override
	@GetMapping(TENANT)
	public R<Tenant> getTenant(Long id) {
		return R.data(tenantService.getById(id));
	}

	@Override
	@GetMapping(TENANT_ID)
	public R<Tenant> getTenant(String tenantId) {
		return R.data(tenantService.getByTenantId(tenantId));
	}

	@Override
	@GetMapping(TENANT_PACKAGE)
	public R<TenantPackage> getTenantPackage(String tenantId) {
		Tenant tenant = tenantService.getByTenantId(tenantId);
		return R.data(tenantPackageService.getById(tenant.getPackageId()));
	}

	@Override
	@GetMapping(PARAM)
	public R<Param> getParam(Long id) {
		return R.data(paramService.getById(id));
	}

	@Override
	@GetMapping(PARAM_VALUE)
	public R<String> getParamValue(String paramKey) {
		return R.data(paramService.getValue(paramKey));
	}

	@Override
	@GetMapping(REGION)
	public R<Region> getRegion(String code) {
		return R.data(regionService.getById(code));
	}

	@Override
	@PostMapping(REGION_LIST)
	public R<List<Region>> getRegionList(List<String> codeList) {
		return R.data(regionService.getRegionList(codeList));
	}

	@Override
	@GetMapping(REDIS_SEQUENCE_UNIQUE_ID)
	public R<String> getRedisUniqueId(RedisSeqEnums redisSeqEnums) {
		return R.data(redisSeqService.getRedisUniqueId(redisSeqEnums));
	}

	@Override
	@GetMapping(REDIS_SEQUENCE_CLEAR)
	public R clearRedisSequence() {
		redisSeqService.clearRedisSequence();
		return R.status(true);
	}

	@Override
	@GetMapping(ROLE_LIST)
	public R<List<Role>> getRoleList(String roleIds) {
		return R.data(roleService.getRoleList(roleIds));
	}

	@Override
	@GetMapping(ROLE_INFO_BY_USER)
	public R<List<Role>> findUserRoleInfoByUserId(String userId) {
		return iUserService.findUserRoleInfoByUserId(userId);
	}

	@Override
	@GetMapping(GET_ROLE_IGNORE_TENANT)
	public R<List<Role>> getRoleIgnoreTenant(List<String> roleCodeList, String tenantId) {
		List<Role> roleIgnoreTenant = roleService.getRoleIgnoreTenant(roleCodeList, tenantId);
		return R.data(roleIgnoreTenant);
	}

	@Override
	@GetMapping(ALARM_CONFIG_LIST)
	public R<JSONObject> alarmConfigList(String language, String roleNames, String deptIds){
		return R.data(dictBizService.alarmConfigList(language,roleNames, deptIds));
	}

	@Override
	@GetMapping(USER_FIND)
	public R<ChangeUserVO> userFind(@RequestParam("areaCode") String areaCode, @RequestParam(value = "phoneNumber", required = false) String phoneNumber,
									@RequestParam(value = "email", required = false) String email, @RequestParam("password") String password){
		ChangeUserVO changeUserVO = new ChangeUserVO();
		User user = iUserService.userFind(areaCode, phoneNumber, email).getData();
		//没找到用户，根据传进来的数据新建一个用户实体类插入数据库
		if(user == null){
			User newUser = new User();
			newUser.setPhoneDiallingCode(areaCode);
			newUser.setPhone(phoneNumber);
			newUser.setEmail(email);
			newUser.setPassword(password);
			String accountName = (phoneNumber != null) ? phoneNumber : email;
			newUser.setAccount(accountName);
			newUser.setUserType(2);
//			if(StringUtil.isNotBlank(email)) {
//				newUser.setRegisterType("2");
//			}
//			if(StringUtil.isNotBlank(phoneNumber)) {
//				newUser.setRegisterType("1");
//			}
			changeUserVO.setIsNew(Boolean.TRUE);
			boolean isSuccess = iUserService.submit(newUser);
			user = newUser;
		}
//		else{ //传进来的数据和根据手机号或者邮箱查找出来的用户数据不一致，并且用户没有允许更新信息，抛出对应异常
//			if(!confirmSign){
//				if(!user.getAccount().equals(accountName)){
//					throw new BusinessException("system.user.account.account.inconsistent");
//				}
//				if(!user.getPhone().equals(phoneNumber)){
//					throw new BusinessException("system.user.account.phone.inconsistent");
//				}
//				if(!user.getEmail().equals(email)){
//					throw new BusinessException("system.user.account.email.inconsistent");
//				}
//			}
//			//信息存在不一致，并且非空则修改
//			if(confirmSign && (user.getAccount().equals(accountName) || user.getPhone().equals(phoneNumber) || user.getEmail().equals(email))){
//				if(!accountName.isBlank()){
//					user.setName(accountName);
//					user.setAccount(accountName);
//				}
//				if(!phoneNumber.isBlank()) user.setPhone(phoneNumber);
//				if(!email.isBlank()){
//					if(iUserService.isEmailExist(email)){
//						throw new BusinessException("system.user.account.email.exist");
//					}
//					user.setEmail(email);
//				}
//				iUserService.updatePlantUser(user);
//			}
//		}

		changeUserVO.setId(user.getId());
		changeUserVO.setUserPhone(user.getPhone());
		changeUserVO.setUserEmail(user.getEmail());
		return R.data(changeUserVO);
	}
}
