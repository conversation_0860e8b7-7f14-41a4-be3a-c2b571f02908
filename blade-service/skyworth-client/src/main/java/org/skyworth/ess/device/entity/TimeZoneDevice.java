package org.skyworth.ess.device.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serializable;


/**
 * 逆变器与时区绑定关系;
 * <AUTHOR> wxc
 * @date : 2023-12-11
 */
@ApiModel(value = "逆变器与时区绑定关系",description = "")
@TableName("time_zone_device")
@Data
@EqualsAndHashCode(callSuper = true)
public class TimeZoneDevice extends TenantEntity implements Serializable,Cloneable{

	public TimeZoneDevice() {
	}

	public TimeZoneDevice(String deviceSerialNumber) {
		this.deviceSerialNumber = deviceSerialNumber;
	}

	@ApiModelProperty(name = "逆变器/设备SN",notes = "逆变器/设备SN")
	private String deviceSerialNumber ;

	@ApiModelProperty(name = "时区",notes = "时区")
	private String timeZone ;

	private Long plantId ;
}
