package org.skyworth.ess.constant;

import lombok.Getter;

/**
 * 日期时间类型枚举类
 * 该类枚举了不同的日期时间单位，如年、月、周、日
 *
 * <AUTHOR>
 * @version V1.0
 * @Date 2024/9/11 上午11:09
 */
@Getter
public enum DateTimeTypeEnum {
	// 年时间单位
	YEAR("y"),
	// 月时间单位
	MONTH("m"),
	// 周时间单位
	WEEK("w"),
	// 日时间单位
	DAY("d");

	/**
	 * 枚举值
	 */
	private final String value;

	/**
	 * 枚举构造方法
	 *
	 * @param value 枚举的字符串表示
	 */
	DateTimeTypeEnum(String value) {
		this.value = value;
	}

	/**
	 * 根据字符串值获取对应的枚举实例
	 *
	 * @param value 字符串表示的日期时间类型
	 * @return 对应的枚举实例，如果找不到则返回null
	 */
	public static DateTimeTypeEnum getDateTimeTypeEnum(String value) {
		// 遍历所有枚举实例
		for (DateTimeTypeEnum dateTimeTypeEnum : DateTimeTypeEnum.values()) {
			// 比较枚举实例的值与传入的字符串是否相等
			if (dateTimeTypeEnum.getValue().equals(value)) {
				// 如果相等，返回该枚举实例
				return dateTimeTypeEnum;
			}
		}
		// 如果找不到匹配的枚举实例，返回null
		return null;
	}
}

