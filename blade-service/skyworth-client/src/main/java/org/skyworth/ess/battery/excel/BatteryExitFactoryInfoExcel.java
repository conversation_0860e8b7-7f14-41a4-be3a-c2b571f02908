/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.excel;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import org.springblade.common.excel.ExcelBusinessUniqueValidate;
import org.springblade.common.excel.ExcelNotNullValidate;

import java.io.Serializable;


/**
 * 电池出厂信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class BatteryExitFactoryInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 电池SN
	 */
	@ColumnWidth(50)
	@ExcelProperty("Batterie SN")
	@ExcelBusinessUniqueValidate(uniqueFlag = true)
	@ExcelNotNullValidate(message = "Batterie SN")
	private String batterySerialNumber;
	/**
	 * 电池型号
	 */
	@ColumnWidth(30)
	@ExcelProperty("Gerätemodell")
	@ExcelNotNullValidate(message = "Gerätemodell")
	private String batteryType;
	/**
	 * 厂家
	 */
	@ColumnWidth(20)
	@ExcelProperty("Hersteller")
	@ExcelNotNullValidate(message = "Hersteller")
	private String company;
	/**
	 * 质保年限
	 */
	@ColumnWidth(30)
	@ExcelProperty("Garantiezeit")
	@ExcelNotNullValidate(message = "Garantiezeit")
	private String qualityGuaranteeYear;
	/**
	 * 出厂日期
	 */
	@ColumnWidth(30)
	@ExcelProperty("Fabrikdatum")
	@ExcelNotNullValidate(message = "Fabrikdatum")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private LocalDate exitFactoryDate;
	/**
	 * 额定电池能量
	 */
	@ColumnWidth(35)
	@ExcelProperty("Nennenergie(kWh)")
	@ExcelNotNullValidate(message = "Nennenergie(kWh)")
	private String ratedBatteryEnergy;
	/**
	 * 额定电池容量
	 */
	@ColumnWidth(30)
	@ExcelProperty("Nennleistung(Ah)")
	@ExcelNotNullValidate(message = "Nennleistung(Ah)")
	private String ratedBatteryCapacity;
	/**
	 * 额定电池电压
	 */
	@ColumnWidth(30)
	@ExcelProperty("Nennspannung(V)")
	@ExcelNotNullValidate(message = "Nennspannung(V)")
	private String ratedBatteryVoltage;
	/**
	 * 内含单体串并联数
	 */
	@ColumnWidth(100)
	@ExcelProperty("Enthält die Anzahl der Einzelzellreihen und Parallelverbindungen")
	@ExcelNotNullValidate(message = "Enthält die Anzahl der Einzelzellreihen und Parallelverbindungen")
	private String singleSeriesParallelingNumber;

	/**
	 * 单体容量
	 */
	@ColumnWidth(30)
	@ExcelProperty("Enthält Einzelzellenkapazität(Ah)")
	@ExcelNotNullValidate(message = "Enthält Einzelzellenkapazität(Ah)")
	private String singleCapacity;


//	/**
//	 * 内含单体串联数
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("Single Series Number")
//	private String singleSeriesNumber;
//	/**
//	 * 创建人账号
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("创建人账号")
//	private String createUserAccount;
//	/**
//	 * 更新人账号
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("更新人账号")
//	private String updateUserAccount;
//	/**
//	 * 租户ID
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("租户ID")
//	private String tenantId;
//	/**
//	 * 逻辑删除
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("逻辑删除")
//	private Integer isDeleted;

}
