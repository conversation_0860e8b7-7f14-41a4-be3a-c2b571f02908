/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.event.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.event.excel.ImportantEventExcel;
import org.skyworth.ess.event.excel.ImportantEventImporter;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.event.vo.ImportantEventVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 重要事件表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/importantEvent")
@Api(value = "重要事件表", tags = "重要事件表接口")
public class ImportantEventController extends BladeController {

	private final IImportantEventService importantEventService;

	/**
	 * 重要事件表 自定义分页
	 */
	@PostMapping("/page/{size}/{current}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入ImportantEvent")
	public R<IPage<ImportantEventVO>> page(@RequestBody ImportantEventVO importantEvent, Query query) {
		IPage<ImportantEventVO> pages = importantEventService.selectImportantEventPage(Condition.getPage(query), importantEvent);
		return R.data(pages);
	}

	/**
	 * 重要事件表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入ImportantEvent")
	public R save(@Valid @RequestBody ImportantEventEntity importantEvent) {
		BladeUser user = AuthUtil.getUser();
		importantEvent.setCreateUserAccount(user.getAccount());
		return R.status(importantEventService.save(importantEvent));
	}

	/**
	 * 重要事件表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入ImportantEvent")
	public R update(@Valid @RequestBody ImportantEventEntity importantEvent) {
		return R.status(importantEventService.updateById(importantEvent));
	}

	/**
	 * 重要事件表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入ImportantEvent")
	public R submit(@Valid @RequestBody ImportantEventEntity importantEvent) {
		return R.status(importantEventService.saveOrUpdate(importantEvent));
	}

	/**
	 * 重要事件表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(importantEventService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-importantEvent")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入ImportantEvent")
	public void exportImportantEvent(@ApiIgnore @RequestParam Map<String, Object> importantEvent, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ImportantEventEntity> queryWrapper = Condition.getQueryWrapper(importantEvent, ImportantEventEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ImportantEvent::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(ImportantEventEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ImportantEventExcel> list = importantEventService.exportImportantEvent(queryWrapper);
		ExcelUtil.export(response, "重要事件表数据" + DateUtil.time(), "重要事件表数据表", list, ImportantEventExcel.class);
	}

	/**
	 * 导出模板
	 */
	@GetMapping("/export-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导入模板")
	public void exportImportantEventTemplate(HttpServletResponse response) {
		List<ImportantEventExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "重要事件模板", "重要事件", list, ImportantEventExcel.class);
	}

	/**
	 * 导入用户
	 */
	@PostMapping("import-importantEvent")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "导入事件", notes = "传入excel")
	public R importUser(MultipartFile file, Integer isCovered) {
		ImportantEventImporter importer = new ImportantEventImporter(importantEventService, isCovered == 1);
		ExcelUtil.save(file, importer, ImportantEventExcel.class);
		return R.success("操作成功");
	}
}
