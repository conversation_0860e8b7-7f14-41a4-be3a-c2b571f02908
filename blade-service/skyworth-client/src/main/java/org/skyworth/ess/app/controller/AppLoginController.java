package org.skyworth.ess.app.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.impl.AppLoginServiceImpl;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年6月20日 10:39:08
 */
@RestController
@RequestMapping("/app/login")
@Api(value = "app接口", tags = "app接口")
@Slf4j
public class AppLoginController extends BladeController {

	@Resource
	private AppLoginServiceImpl appLoginService;

	@GetMapping("/getVerificationCode")
	@ApiOperation(value = "验证码信息", notes = "验证码信息")
	@ApiLog("app验证码信息")
	public R<String> getVerificationCode(@ApiParam(value = "邮箱", required = true) @RequestParam String email,
										 @ApiParam(value = "类型", required = true) @RequestParam String type) {
		return appLoginService.getVerificationCode(email, type, "");
	}
}
