package org.skyworth.ess.utils;

import com.microsoft.azure.storage.CloudStorageAccount;
import com.microsoft.azure.storage.StorageException;
import com.microsoft.azure.storage.blob.*;
import org.apache.commons.text.StringEscapeUtils;
import org.skyworth.ess.config.BlobConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.DateUtil;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.*;

/**
 * 获取blobContainer对象
 *
 * <AUTHOR>
 * @since 2023/9/27 17:31
 **/
public class BlobHelper {
	private static final Logger logger = LoggerFactory.getLogger(BlobHelper.class);

	public static CloudBlobContainer getBlobContainer(String containerName, BlobConfig storageConfig) {
		try {
			CloudStorageAccount account = CloudStorageAccount.parse(
				MessageFormat.format("DefaultEndpointsProtocol={0};AccountName={1};AccountKey={2};EndpointSuffix={3};BlobEndpoint={4};QueueEndpoint={5};TableEndpoint={6}", storageConfig.getDefaultEndpointsProtocol(), storageConfig.getAccountName(), storageConfig.getAccountKey(), storageConfig.getEndpointSuffix(),
					storageConfig.getBlobEndpoint(), storageConfig.getQueueEndpoint(), storageConfig.getTableEndpoint()
				));
			CloudBlobClient serviceClient = account.createCloudBlobClient();
			return serviceClient.getContainerReference(containerName);
		} catch (Exception e) {
			logger.error("Exception in obtaining BlobContainer,error:", e);
			throw new BusinessException("toolkit.blob.get.exception", e.getMessage());
		}
	}

	/**
	 * 获取文件名
	 *
	 * @param fileName 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2023/9/27 18:47
	 **/
	public static String getFileExtension(String fileName) {
		int position = fileName.lastIndexOf('.');
		if (position > 0) {
			return fileName.substring(position);
		}
		return "";
	}

	public static String getSasToken(CloudBlockBlob blob, int hours, String key, BladeRedis bladeRedis) throws InvalidKeyException, StorageException {
		// 生成鉴权
		SharedAccessBlobPolicy sharedAccessBlobPolicy = new SharedAccessBlobPolicy();
		// 时间 1小时
		logger.info("getSasToken==> current Time :" + new Date());
		GregorianCalendar calendar = new GregorianCalendar(TimeZone.getTimeZone("UTC"));
		calendar.setTime(new Date());
		calendar.add(Calendar.MINUTE, -5);
		sharedAccessBlobPolicy.setSharedAccessStartTime(calendar.getTime());
		logger.info("getSasToken==> SharedAccessStartTime :" + calendar.getTime());
		calendar.add(Calendar.HOUR, hours);
		// 生效时间
		sharedAccessBlobPolicy.setSharedAccessExpiryTime(calendar.getTime());
		logger.info("getSasToken==> SharedAccessExpiryTime :" + calendar.getTime());
		// 权限
		sharedAccessBlobPolicy.setPermissions(EnumSet.of(SharedAccessBlobPermissions.READ));
		String sasToken = StringEscapeUtils.unescapeHtml4(blob.generateSharedAccessSignature(sharedAccessBlobPolicy, null));
		// 设置缓存30分钟
		bladeRedis.setEx(key, sasToken, Duration.ofMinutes(30));
		return sasToken;
	}

	/**
	 * 设置文件路径
	 *
	 * @param fileType  文件类型
	 * @param thumbnail 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2023/9/27 18:47
	 **/
	public static String getBlobPreName(int fileType, Boolean thumbnail) {
		String afterName = "";
		if (thumbnail) {
			afterName = "thumbnail/";
		}
		switch (fileType) {
			case 1:
				return DateUtil.format(new Date(), DateUtil.PATTERN_DATE) + "/" + afterName;
			default:
				return "";
		}
	}

	public static String getSasToken(String resourceUri, String keyName, String key) {
		long epoch = System.currentTimeMillis() / 1000L;
		int week = 60 * 60 * 24 * 7;
		String expiry = Long.toString(epoch + week);
		String sasToken = null;
		try {
			String stringToSign = URLEncoder.encode(resourceUri, "UTF-8") + "\n" + expiry;
			String signature = getHmac256(key, stringToSign);
			sasToken = "SharedAccessSignature sr=" + URLEncoder.encode(resourceUri, "UTF-8") + "&sig=" +
				URLEncoder.encode(signature, "UTF-8") + "&se=" + expiry + "&skn=" + keyName;
		} catch (Exception e) {
			logger.error("getSasToken,error:", e);
			throw new BusinessException("toolkit.sastoken.get.error",e.getMessage());
		}
		return sasToken;
	}


	private static String getHmac256(String key, String input) {
		Mac mac = null;
		String hash = null;
		try {
			mac = Mac.getInstance("HmacSHA256");
			SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "HmacSHA256");
			mac.init(secretKey);
			Base64.Encoder encoder = Base64.getEncoder();
			hash = new String(encoder.encode(mac.doFinal(input.getBytes("UTF-8"))));
		} catch (InvalidKeyException e) {
			logger.error("getHmac256 InvalidKeyException,error:", e);
		} catch (NoSuchAlgorithmException e) {
			logger.error("getHmac256 NoSuchAlgorithmException,error:", e);
		} catch (IllegalStateException e) {
			logger.error("getHmac256 IllegalStateException,error:", e);
		} catch (UnsupportedEncodingException e) {
			logger.error("getHmac256 UnsupportedEncodingException,error:", e);
		}
		return hash;
	}
}
