package org.skyworth.ess.app.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppReportDataVO;
import org.skyworth.ess.app.vo.AppReportDetailVO;
import org.skyworth.ess.app.vo.AppReportHeaderVO;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.FunctionSetName;
import org.springblade.common.utils.DataUnitConversionUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
public class ReportUtil {

    public static Map<String, BigDecimal> initHoursMap() {
        // 初始化起始点为 00:00
        LocalDateTime localDateTime = LocalDateTime.of(2023, 1, 1, 0, 0);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
        // 计算一天有 288 个点
        for (int i = 0; i < 288; i++) {
            String format = dateTimeFormatter.format(localDateTime);
            map.put(format, new BigDecimal("0.0"));
            // 往后推5分钟
            localDateTime = localDateTime.plusSeconds(300);
        }
        map.put("24:00", new BigDecimal("0.0"));
        return map;
    }

    public static QueryDeviceLog22Condition getQueryDeviceLog22Condition(AppVO appVO) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date begin = simpleDateFormat.parse(appVO.getDataScope() + " 00:00:00");
        Date end = simpleDateFormat.parse(appVO.getDataScope() + " 23:59:59");
        QueryDeviceLog22Condition query = new QueryDeviceLog22Condition();
        query.setStartDateTime(begin);
        query.setEndDateTime(end);
        query.setPlantId(appVO.getPlantId());
        query.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
        return query;
    }

    public static QueryCondition getBatteryQueryCondition(AppVO appVO, LocalDate beginLocalDate, LocalDate endLocalDate) {
        QueryCondition queryCondition = new QueryCondition();
        queryCondition.setStartDateTime(Date.from(beginLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        queryCondition.setEndDateTime(Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        queryCondition.setPlantId(appVO.getPlantId());
        queryCondition.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
        return queryCondition;
    }

    // 设置天的 饼图
    public static void setDailyPidData(AppReportHeaderVO appReportHeaderVO,BigDecimal dailyEnergyOrParallel,BigDecimal todayExportEnergy,
                                       BigDecimal dailyEnergyOfLoadOrParallel,BigDecimal dailySupportEnergyToBackupOrParallel,
                                       BigDecimal batteryDailyChargeEnergyOrParallel,BigDecimal todayImportEnergy) {
        // 设置第一排饼图数据
		/** 并机
		 * 天的维度：
		 * 发电量PVI daily generating energy sum 0x13D8 - 馈电量0x1334
		 * 如果得数为负数，则不用减。
		 * 周月年的维度：把每天的最终得数直接相加
		 */
        if (dailyEnergyOrParallel.compareTo(todayExportEnergy) >= 0) {
            BigDecimal selfConsumed = dailyEnergyOrParallel.subtract(todayExportEnergy);
            appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(selfConsumed, 1));
            appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(todayExportEnergy, 1));
            // 两边的总数
            BigDecimal addResult = selfConsumed.add(todayExportEnergy);
            if (BigDecimal.ZERO.compareTo(addResult) == 0) {
                appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
                appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
                appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
            } else {
                BigDecimal selfConsumedRatio = selfConsumed.divide(addResult, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
                BigDecimal fedToGridRatio = new BigDecimal(100).subtract(selfConsumedRatio);
                appReportHeaderVO.setSelfConsumedRatio(selfConsumedRatio);
                appReportHeaderVO.setFedToGridRatio(fedToGridRatio);
                appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfConsumed, todayExportEnergy));
            }
        } else {
            appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(dailyEnergyOrParallel, 1));
            appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
            if (dailyEnergyOrParallel.compareTo(BigDecimal.ZERO) == 0) {
                appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
            } else {
                appReportHeaderVO.setSelfConsumedRatio(new BigDecimal(100));
            }
            appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
            appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, dailyEnergyOrParallel, BigDecimal.ZERO));
        }

        // 设置第二排饼图数据
		/** 并机
		 * Daily energy of load sum 0x13AC
		 * + Daily support energy sum to Backup 0x13C0
		 * + Battery daily charge energy 0x13E0
		 * - Today import Energy 0x1332
		 * 如果是负数，那就取PVI daily generating energy sum 0x13D8
		 * 周月年的维度：把每天的最终得数直接相加
		 */
        BigDecimal addResult = dailyEnergyOfLoadOrParallel.add(dailySupportEnergyToBackupOrParallel).add(batteryDailyChargeEnergyOrParallel);
        BigDecimal selfSufficiency = addResult.subtract(todayImportEnergy);
        appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(selfSufficiency, 1));
        appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(todayImportEnergy, 1));
        appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfSufficiency, todayImportEnergy));
        if (addResult.compareTo(BigDecimal.ZERO) > 0 && todayImportEnergy.compareTo(BigDecimal.ZERO) > 0) {
            // 如果相减为负数，非并机 则取 sumTodayEnergy 1027 地址值，并机则取 PVI daily generating energy sum 0x13D8
            if (selfSufficiency.compareTo(BigDecimal.ZERO) < 0) {
                log.info("addResult and sumTodayImportEnergy both more than zero, but selfSufficiency is negative");
                ReportUtil.rightIsNegative(appReportHeaderVO, dailyEnergyOrParallel, todayImportEnergy);
            } else {
                // 两边的总数
                BigDecimal add = selfSufficiency.add(todayImportEnergy);
                BigDecimal selfSufficiencyRatio = selfSufficiency.divide(add, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
                appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
                appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
            }
        } else if (addResult.compareTo(BigDecimal.ZERO) == 0 && todayImportEnergy.compareTo(BigDecimal.ZERO) == 0) {
            appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
            appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
            log.info("addResult and sumTodayImportEnergy both is zero");
        } else { // 2边上报数据有一边为负数场景 或者 为0的场景
            if (selfSufficiency.compareTo(BigDecimal.ZERO) < 0) {
                log.info("addResult and sumTodayImportEnergy subtract less zero");
                ReportUtil.rightIsNegative(appReportHeaderVO, dailyEnergyOrParallel, todayImportEnergy);
            } else {
                BigDecimal add = selfSufficiency.add(todayImportEnergy);
                if (add.compareTo(BigDecimal.ZERO) != 0) {
                    log.info("addResult and sumTodayImportEnergy subtract is not zero");
                    BigDecimal selfSufficiencyRatio = selfSufficiency.divide(add, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
                    appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
                    appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
                } else {
                    log.info("addResult and sumTodayImportEnergy subtract add is zero");
                    appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
                    appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
                }
            }
        }
    }
    // 设置周月年饼图
    public static void setWeekMonthAnnualPieData(AppReportHeaderVO appReportHeaderVO, BigDecimal selfConsumed,BigDecimal fedToGrid,BigDecimal selfSufficiency,BigDecimal fromGrid) {
        appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(selfConsumed, 1));
        appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(fedToGrid, 1));
        BigDecimal firstPieAddResult = selfConsumed.add(fedToGrid);
        if (BigDecimal.ZERO.compareTo(firstPieAddResult) == 0) {
            appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
            appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
            appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
        } else {
            BigDecimal selfConsumedRatio = selfConsumed.divide(firstPieAddResult, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
            BigDecimal fedToGridRatio = new BigDecimal(100).subtract(selfConsumedRatio);
            appReportHeaderVO.setSelfConsumedRatio(selfConsumedRatio);
            appReportHeaderVO.setFedToGridRatio(fedToGridRatio);
            appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfConsumed, fedToGrid));
        }

        appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(selfSufficiency, 1));
        appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(fromGrid, 1));
        BigDecimal secondPieAddResult = selfSufficiency.add(fromGrid);
        if (BigDecimal.ZERO.compareTo(secondPieAddResult) == 0) {
            appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
            appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
            appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
        } else {
            BigDecimal selfSufficiencyRatio = selfSufficiency.divide(secondPieAddResult, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
            BigDecimal fromGridRatio = new BigDecimal(100).subtract(selfSufficiencyRatio);
            appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
            appReportHeaderVO.setFromGridRatio(fromGridRatio);
            appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1, selfSufficiency, fromGrid));
        }
    }

    public static void setMapValue(Map<String, BigDecimal> map, String key, BigDecimal value) {
        if (value == null) {
            return;
        }
        map.put(key, value.setScale(1, RoundingMode.HALF_UP));
    }

    public static AppReportDetailVO getAppHoursReportDetailVO(Map<String, BigDecimal> pvTotalInputPowerMap, Map<String, BigDecimal> batteryOutputMap,
                                                        Map<String, BigDecimal> loadAddEpsMap) {
        AppReportDetailVO hourReport = new AppReportDetailVO();
        List<AppReportDataVO> pvList = new ArrayList<>();
        List<AppReportDataVO> batteryOutputList = new ArrayList<>();
        List<AppReportDataVO> loadAddEpsList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : pvTotalInputPowerMap.entrySet()) {
            AppReportDataVO pv = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
            pv.setKey(entry.getKey());
            pv.setValue(entry.getValue());
            pvList.add(pv);
        }
        for (Map.Entry<String, BigDecimal> entry : batteryOutputMap.entrySet()) {
            AppReportDataVO battery = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
            battery.setKey(entry.getKey());
            battery.setValue(entry.getValue());
            batteryOutputList.add(battery);
        }
        for (Map.Entry<String, BigDecimal> entry : loadAddEpsMap.entrySet()) {
            // 总充电量
            AppReportDataVO loadAddEps = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
            loadAddEps.setKey(entry.getKey());
            loadAddEps.setValue(entry.getValue());
            loadAddEpsList.add(loadAddEps);
        }

        hourReport.setPvGenerationList(pvList);
        hourReport.setBatteryOutputList(batteryOutputList);
        hourReport.setPowerConsumptionList(loadAddEpsList);
        log.info("queryPlantRunningStateV2 getHoursReportV2  end");
        return hourReport;
    }

    public static AppReportDetailVO calHoursReportData(List<DeviceLog22VO> deviceLog22VoList, Function<DeviceLog22VO,BigDecimal> pvFun,
                      Function<DeviceLog22VO,BigDecimal> batteryPowerFun, Function<DeviceLog22VO,BigDecimal> loadAddFun, Function<DeviceLog22VO,BigDecimal> phaseGrid1Fun,
                      Function<DeviceLog22VO,BigDecimal> phaseGrid2Fun, Function<DeviceLog22VO,BigDecimal> phaseGrid3Fun, Function<DeviceLog22VO,BigDecimal> batterySocFun) {
        // 初始化每5分钟数据
        Map<String, BigDecimal> pvTotalInputPowerMap = initHoursMap();
        Map<String, BigDecimal> batteryOutputMap = initHoursMap();
        Map<String, BigDecimal> powerConsumption4LoadAddEpsMap = initHoursMap();
        Map<String, BigDecimal> gridConsumptionMap = initHoursMap();
        Map<String, BigDecimal> feedInGridMap = initHoursMap();
        Map<String, BigDecimal> batteryInputMap = initHoursMap();
        Map<String, BigDecimal> batterySocMap = initHoursMap();
        for (DeviceLog22VO deviceLog22VO : deviceLog22VoList) {
            String appTotalDate = deviceLog22VO.getAppTotalDate();
            BigDecimal appPvTotalInputPower = pvFun.apply(deviceLog22VO);// getAppTotallyInputDcWattSum
            BigDecimal appBatteryPower = batteryPowerFun.apply(deviceLog22VO) == null ? BigDecimal.ZERO : batteryPowerFun.apply(deviceLog22VO);//getAppBatteryPowerSum
            BigDecimal loadAddEps = loadAddFun.apply(deviceLog22VO); //getAppLoadAddBackup();
            BigDecimal appPhaserWattOfGrid = phaseGrid1Fun.apply(deviceLog22VO) == null ? BigDecimal.ZERO : phaseGrid1Fun.apply(deviceLog22VO);//getAppPhaseL1WattOfGridSum
            BigDecimal appPhasesWattOfGrid = phaseGrid2Fun.apply(deviceLog22VO) == null ? BigDecimal.ZERO : phaseGrid2Fun.apply(deviceLog22VO);//getAppPhaseL2WattOfGridSum
            BigDecimal appPhasetWattOfGrid = phaseGrid3Fun.apply(deviceLog22VO) == null ? BigDecimal.ZERO : phaseGrid3Fun.apply(deviceLog22VO);//getAppPhaseL3WattOfGridSum
            BigDecimal appBatterySoc = batterySocFun.apply(deviceLog22VO);//getAppBatterySoc

            BigDecimal appBatteryOutput = BigDecimal.ZERO;
            BigDecimal appBatteryInput = BigDecimal.ZERO;
            if (appBatteryPower.compareTo(BigDecimal.ZERO) > 0) {
                // 少于0的时候记录为0,大于0为放电
                appBatteryOutput = appBatteryPower;
            } else if (appBatteryPower.compareTo(BigDecimal.ZERO) < 0) {
                // 正数和0记录为0，负数则取绝对值，小于0则为充电
                appBatteryInput = appBatteryPower.abs();
            }
            // 取正数，少于0的时候记录为0
            BigDecimal positivePhaserWattOfGrid = BigDecimal.ZERO;
            // 取负数，正数和0记录为0，负数则取绝对值相加
            BigDecimal negativePhaserWattOfGrid = BigDecimal.ZERO;
            if (appPhaserWattOfGrid.compareTo(BigDecimal.ZERO) > 0) {
                positivePhaserWattOfGrid = appPhaserWattOfGrid;
            } else if (appPhaserWattOfGrid.compareTo(BigDecimal.ZERO) < 0) {
                negativePhaserWattOfGrid = appPhaserWattOfGrid.abs();
            }
            BigDecimal positivePhasesWattOfGrid = BigDecimal.ZERO;
            BigDecimal negativePhasesWattOfGrid = BigDecimal.ZERO;
            if (appPhasesWattOfGrid.compareTo(BigDecimal.ZERO) > 0) {
                positivePhasesWattOfGrid = appPhasesWattOfGrid;
            } else if (appPhasesWattOfGrid.compareTo(BigDecimal.ZERO) < 0) {
                negativePhasesWattOfGrid = appPhasesWattOfGrid.abs();
            }
            BigDecimal positivePhasetWattOfGrid = BigDecimal.ZERO;
            BigDecimal negativePhasetWattOfGrid = BigDecimal.ZERO;
            if (appPhasetWattOfGrid.compareTo(BigDecimal.ZERO) > 0) {
                positivePhasetWattOfGrid = appPhasetWattOfGrid;
            } else if (appPhasetWattOfGrid.compareTo(BigDecimal.ZERO) < 0) {
                negativePhasetWattOfGrid = appPhasetWattOfGrid.abs();
            }
            BigDecimal gridConsumption = positivePhaserWattOfGrid.add(positivePhasesWattOfGrid).add(positivePhasetWattOfGrid);
            BigDecimal feedInGrid = negativePhaserWattOfGrid.add(negativePhasesWattOfGrid).add(negativePhasetWattOfGrid);
            int count = 0;
            String startDateKey = null;
            // 只需针对 pvTotalInputPowerMap 进行遍历， 因为其他 map 的数据和它的数据点一样，都是289个
            // 先取 第一个，然后将 第一个 和 第二个作为一个区间， 如果数据不在此区间，将 第二个作为第一个，开始下一个区间
            // 如果相等，则赋值 到 value上，跳出循环
            // 00:00  00:05 -> 00:05  00:10
            for (Map.Entry<String, BigDecimal> entry : pvTotalInputPowerMap.entrySet()) {
                // 取第一个作为 首个比较对象
                if (count == 0) {
                    startDateKey = entry.getKey();
                    count++;
                    continue;
                }
                String endDateKey = entry.getKey();
                if (appTotalDate.compareTo(startDateKey) >= 0 && appTotalDate.compareTo(endDateKey) < 0) {
                    setMapValue(pvTotalInputPowerMap, endDateKey, appPvTotalInputPower);
                    setMapValue(batteryOutputMap, endDateKey, appBatteryOutput);
                    setMapValue(powerConsumption4LoadAddEpsMap, endDateKey, loadAddEps);
                    setMapValue(gridConsumptionMap, endDateKey, gridConsumption);
                    setMapValue(feedInGridMap, endDateKey, feedInGrid);
                    setMapValue(batteryInputMap, endDateKey, appBatteryInput);
                    setMapValue(batterySocMap, endDateKey, appBatterySoc);
                    break;
                } else {
                    startDateKey = endDateKey;
                }
            }

        }
        AppReportDetailVO appHoursReportDetailVO = getAppHoursReportDetailVO(pvTotalInputPowerMap, batteryOutputMap, powerConsumption4LoadAddEpsMap);
        setOtherList(appHoursReportDetailVO, gridConsumptionMap, feedInGridMap, batteryInputMap, batterySocMap);
        return appHoursReportDetailVO;
    }

    private static void setOtherList(AppReportDetailVO appReportDetailVO, Map<String, BigDecimal> gridConsumptionMap, Map<String, BigDecimal> feedInGridMap,
                              Map<String, BigDecimal> batteryInputMap, Map<String, BigDecimal> batterySocMap) {
        appReportDetailVO.setGridConsumptionList(addData(gridConsumptionMap));
        appReportDetailVO.setFeedInGridList(addData(feedInGridMap));
        appReportDetailVO.setBatteryInputList(addData(batteryInputMap));
        appReportDetailVO.setBatterySocList(addData(batterySocMap));
        log.info("queryPlantRunningStateV2 getHoursReportV2 setOtherList end");
    }

    public static void rightIsNegative(AppReportHeaderVO appReportHeaderVO, BigDecimal sumTodayEnergy, BigDecimal sumTodayImportEnergy) {
        appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(sumTodayEnergy, 1));
        appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(sumTodayImportEnergy, 1));
        BigDecimal otherAdd = sumTodayEnergy.add(sumTodayImportEnergy);
        appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyResult(otherAdd, 1));
        if (otherAdd.compareTo(BigDecimal.ZERO) == 0) {
            appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
            appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
        } else {
            BigDecimal selfSufficiencyRatio = sumTodayEnergy.divide(otherAdd, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
            appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
            appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
        }
    }

    // 根据不同时间查询数据库
    public static List<BatteryEverydayTotalVO> getBatteryEverydayTotalDbData(AppVO appVO, LocalDate beginLocalDate, LocalDate endLocalDate, Function<QueryCondition, List<BatteryEverydayTotalVO>> fun) {
        QueryCondition queryCondition = new QueryCondition();
        queryCondition.setStartDateTime(Date.from(beginLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        queryCondition.setEndDateTime(Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        queryCondition.setPlantId(appVO.getPlantId());
        queryCondition.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
        return fun.apply(queryCondition);
    }
    // 并机 和 非并机取值字段不一样
    public static AppReportDetailVO setWeekMonthAnnualToKeyValue(AppVO appVO, List<BatteryEverydayTotalVO> batteryEverydayTotalList
            ,Function<BatteryEverydayTotalVO,BigDecimal> pvDailyEnergyOrParallelFun, Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyDischargeEnergyOrParallelFun,
                                                                 Function<BatteryEverydayTotalVO,BigDecimal> loadAddOrParallelFun, Function<BatteryEverydayTotalVO,BigDecimal> todayImportEnergyOrParallelFun,
                                                                 Function<BatteryEverydayTotalVO,BigDecimal> todayExportEnergyOrParallelFun, Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyChargeEnergyOrParallelFun

    ) {
                log.info("db report type : {} ", appVO.getType());
        AppReportDetailVO dailyReport = new AppReportDetailVO();
        List<AppReportDataVO> pvGenerationList = new ArrayList<>();
        List<AppReportDataVO> batteryOutputList = new ArrayList<>();
        List<AppReportDataVO> powerConsumptionList = new ArrayList<>();
        List<AppReportDataVO> gridConsumptionList = new ArrayList<>();
        List<AppReportDataVO> feedInGridList = new ArrayList<>();
        List<AppReportDataVO> batteryInputList = new ArrayList<>();
        for (BatteryEverydayTotalVO entity : batteryEverydayTotalList) {
            AppReportDataVO pv = new AppReportDataVO();
            AppReportDataVO batteryOutput = new AppReportDataVO();
            AppReportDataVO power = new AppReportDataVO();
            AppReportDataVO gridConsumption = new AppReportDataVO();
            AppReportDataVO feedInGrid = new AppReportDataVO();
            AppReportDataVO batteryInput = new AppReportDataVO();
            // 周报表
            if (1 == appVO.getType()) {
                String subTotalDate = entity.getAppTotalDate().substring(5);
                setKeyValue(subTotalDate, pv::setKey);
                setKeyValue(subTotalDate, batteryOutput::setKey);
                setKeyValue(subTotalDate, power::setKey);
                setKeyValue(subTotalDate, gridConsumption::setKey);
                setKeyValue(subTotalDate, feedInGrid::setKey);
                setKeyValue(subTotalDate, batteryInput::setKey);
            } else if (2 == appVO.getType() || 3 == appVO.getType()) {
                // 月、年报表
                String appTotalDate = entity.getAppTotalDate();
                setKeyValue(appTotalDate, pv::setKey);
                setKeyValue(appTotalDate, batteryOutput::setKey);
                setKeyValue(appTotalDate, power::setKey);
                setKeyValue(appTotalDate, gridConsumption::setKey);
                setKeyValue(appTotalDate, feedInGrid::setKey);
                setKeyValue(appTotalDate, batteryInput::setKey);
            }
            pv.setValue(divideThousand(pvDailyEnergyOrParallelFun.apply(entity)));
            pvGenerationList.add(pv);
            batteryOutput.setValue(divideThousand(batteryDailyDischargeEnergyOrParallelFun.apply(entity)));
            batteryOutputList.add(batteryOutput);
            power.setValue(divideThousand((loadAddOrParallelFun.apply(entity))));
            powerConsumptionList.add(power);
            gridConsumption.setValue(divideThousand(todayImportEnergyOrParallelFun.apply(entity)));
            gridConsumptionList.add(gridConsumption);
            feedInGrid.setValue(divideThousand(todayExportEnergyOrParallelFun.apply(entity)));
            feedInGridList.add(feedInGrid);
            batteryInput.setValue(divideThousand(batteryDailyChargeEnergyOrParallelFun.apply(entity)));
            batteryInputList.add(batteryInput);

        }
        dailyReport.setPvGenerationList(pvGenerationList);
        dailyReport.setBatteryOutputList(batteryOutputList);
        dailyReport.setPowerConsumptionList(powerConsumptionList);
        dailyReport.setGridConsumptionList(gridConsumptionList);
        dailyReport.setFeedInGridList(feedInGridList);
        dailyReport.setBatteryInputList(batteryInputList);
        return dailyReport;
    }
    // 补齐数据库中不存在的天数
    public static void completionDay(AppReportDetailVO report, LocalDate beginLocalDate, LocalDate endLocalDate, String format) throws ParseException {
        List<AppReportDataVO> pvList = report.getPvGenerationList();
        int interval;
        if ("dd".equals(format)) {
            interval = beginLocalDate.lengthOfMonth();
        } else {
            long epochDay = endLocalDate.toEpochDay() - beginLocalDate.toEpochDay();
            interval = Long.valueOf(epochDay).intValue();
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        // 只需构造一次， 在 setDbToInitMap 中 会和db中 的 日期做比较， 由于 5个报表的日期都会是一样的， 因此如果db中的 日期和
        // 初始化 initDaysMap 中的的日期 相等，那么 initDaysMap 后面的 value会覆盖前面的 value，在根据 initDaysMap 中的所有制构造的是一个新的 list
        Map<String, BigDecimal> initDaysMap = initDaysMap(dateTimeFormatter, interval, beginLocalDate);
        report.setPvGenerationList(setDbToInitMap(initDaysMap, pvList));

        List<AppReportDataVO> batteryList = report.getBatteryOutputList();
        report.setBatteryOutputList(setDbToInitMap(initDaysMap, batteryList));

        List<AppReportDataVO> powerList = report.getPowerConsumptionList();
        report.setPowerConsumptionList(setDbToInitMap(initDaysMap, powerList));
        List<AppReportDataVO> gridConsumptionList = report.getGridConsumptionList();
        report.setGridConsumptionList(setDbToInitMap(initDaysMap, gridConsumptionList));

        List<AppReportDataVO> feedInGridList = report.getFeedInGridList();
        report.setFeedInGridList(setDbToInitMap(initDaysMap, feedInGridList));

        List<AppReportDataVO> batteryInputList = report.getBatteryInputList();
        report.setBatteryInputList(setDbToInitMap(initDaysMap, batteryInputList));

    }

    public static void completionMonth(AppReportDetailVO report) {
        List<AppReportDataVO> pvList = report.getPvGenerationList();
        // 只用初始化一次， 只是在 getNotExistKeyList 做比较，不作为最终数据，最终数据是 将db中的原始数据 + 不在 initDaysMap 中的数据 addAll
        Map<String, BigDecimal> initDaysMap = initMonthsMap();
        List<AppReportDataVO> resultList = getNotExistKeyList(initDaysMap, pvList);
        pvList.addAll(resultList);

        List<AppReportDataVO> batteryList = report.getBatteryOutputList();
        batteryList.addAll(getNotExistKeyList(initDaysMap, batteryList));

        List<AppReportDataVO> powerList = report.getPowerConsumptionList();
        powerList.addAll(getNotExistKeyList(initDaysMap, powerList));

        List<AppReportDataVO> gridConsumptionList = report.getGridConsumptionList();
        gridConsumptionList.addAll(getNotExistKeyList(initDaysMap, gridConsumptionList));

        List<AppReportDataVO> feedInGridList = report.getFeedInGridList();
        feedInGridList.addAll(getNotExistKeyList(initDaysMap, feedInGridList));

        List<AppReportDataVO> batteryInputList = report.getBatteryInputList();
        batteryInputList.addAll(getNotExistKeyList(initDaysMap, batteryInputList));
        sortList(pvList);
        sortList(batteryList);
        sortList(powerList);

        sortList(gridConsumptionList);
        sortList(feedInGridList);
        sortList(batteryInputList);

    }

    private static Map<String, BigDecimal> initMonthsMap() {
        Map<String, BigDecimal> initMonthsMap = new HashMap<>();
        BigDecimal zero = new BigDecimal("0.00");
        initMonthsMap.put("01", zero);
        initMonthsMap.put("02", zero);
        initMonthsMap.put("03", zero);
        initMonthsMap.put("04", zero);
        initMonthsMap.put("05", zero);
        initMonthsMap.put("06", zero);
        initMonthsMap.put("07", zero);
        initMonthsMap.put("08", zero);
        initMonthsMap.put("09", zero);
        initMonthsMap.put("10", zero);
        initMonthsMap.put("11", zero);
        initMonthsMap.put("12", zero);
        return initMonthsMap;
    }

    private static void sortList(List<AppReportDataVO> list) {
        if (list != null) {
            list.sort(Comparator.comparing(AppReportDataVO::getKey));
        }
    }

    // 将初始化数据和数据库中比较， 如果数据库中不存在此天数据，则构造一个 0
    private static List<AppReportDataVO> getNotExistKeyList(Map<String, BigDecimal> initDaysMap, List<AppReportDataVO> dbList) {
        List<AppReportDataVO> resultList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();
            boolean existKey = false;
            for (AppReportDataVO vo : dbList) {
                if (key.equals(vo.getKey())) {
                    existKey = true;
                    break;
                }
            }
            if (!existKey) {
                AppReportDataVO addVo = new AppReportDataVO();
                addVo.setKey(key);
                addVo.setValue(value);
                resultList.add(addVo);
            }
        }
        return resultList;
    }
    // 初始化周、月报表天数 12.1:0  12.2:0 12.3:0 格式
    private static Map<String, BigDecimal> initDaysMap(DateTimeFormatter dateTimeFormatter, int intervalDay, LocalDate beginLocalDate) {
        Map<String, BigDecimal> initDaysMap = new LinkedHashMap<>();
        BigDecimal zero = new BigDecimal("0.00");
        for (int i = 0; i < intervalDay; i++) {
            LocalDate localDate = beginLocalDate.plusDays(Integer.toUnsignedLong(i));
            String formatDay = dateTimeFormatter.format(localDate);
            initDaysMap.put(formatDay, zero);
        }
//		log.info("initDaysMap : {}", initDaysMap);
        return initDaysMap;
    }

    private static List<AppReportDataVO> setDbToInitMap(Map<String, BigDecimal> initDaysMap, List<AppReportDataVO> dischargeCapacityList) {
        List<AppReportDataVO> resultList = new ArrayList<>();
        log.info("dbData ");
        for (AppReportDataVO vo : dischargeCapacityList) {
            for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
                String key = entry.getKey();
                if (key.equals(vo.getKey())) {
                    entry.setValue(vo.getValue());
                    break;
                }
            }
        }
        for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();
            AppReportDataVO addVo = new AppReportDataVO();
            addVo.setKey(key);
            addVo.setValue(value);
            resultList.add(addVo);
        }

        return resultList;
    }
    private static void setKeyValue(String value, FunctionSetName<String> function) {
        function.setName(value);
    }

    private static BigDecimal divideThousand(BigDecimal value) {
        if (value == null) {
            return new BigDecimal(0);
        }
        return value.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP);
    }
    private static List<AppReportDataVO> addData(Map<String, BigDecimal> map) {
        List<AppReportDataVO> addList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
            AppReportDataVO pv = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
            pv.setKey(entry.getKey());
            pv.setValue(entry.getValue());
            addList.add(pv);
        }
        return addList;
    }
}
