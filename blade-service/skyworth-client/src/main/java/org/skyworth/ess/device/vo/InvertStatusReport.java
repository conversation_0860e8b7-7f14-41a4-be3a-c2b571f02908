package org.skyworth.ess.device.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> - x<PERSON><PERSON>jiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/3/11 17:07:51
 */
@Data
public class InvertStatusReport {
	//	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal mppt1Power;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal mppt2Power;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal mppt3Power;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal mppt4Power;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseRWattOfLoad;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseSWattOfLoad;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseTWattOfLoad;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseRWattOfGrid;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseSWattOfGrid;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseTWattOfGrid;
	//@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseRWattOfEps;
//	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseSWattOfEps;
//	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseTWattOfEps;
	private BigDecimal l1PhasePowerOfAcCouple;
	private BigDecimal l2PhasePowerOfAcCouple;
	private BigDecimal l3PhasePowerOfAcCouple;
	private String deviceDateTime;
	private String deviceDateTimeForCal;
	private String deviceDateTimeForDay;
	private BigDecimal pv1Voltage;
	private BigDecimal pv2Voltage;
	private BigDecimal pv3Voltage;
	private BigDecimal pv4Voltage;
	private BigDecimal pv1Current;
	private BigDecimal pv2Current;
	private BigDecimal pv3Current;
	private BigDecimal pv4Current;

	public static InvertStatusReport init() {
		InvertStatusReport invertStatusReport = new InvertStatusReport();
		// 初始化BigDecimal类型的属性为BigDecimal.ZERO
		invertStatusReport.mppt1Power = BigDecimal.ZERO;
		invertStatusReport.mppt2Power = BigDecimal.ZERO;
		invertStatusReport.mppt3Power = BigDecimal.ZERO;
		invertStatusReport.mppt4Power = BigDecimal.ZERO;
		invertStatusReport.phaseRWattOfLoad = BigDecimal.ZERO;
		invertStatusReport.phaseSWattOfLoad = BigDecimal.ZERO;
		invertStatusReport.phaseTWattOfLoad = BigDecimal.ZERO;
		invertStatusReport.phaseRWattOfGrid = BigDecimal.ZERO;
		invertStatusReport.phaseSWattOfGrid = BigDecimal.ZERO;
		invertStatusReport.phaseTWattOfGrid = BigDecimal.ZERO;
		invertStatusReport.phaseRWattOfEps = BigDecimal.ZERO;
		invertStatusReport.phaseSWattOfEps = BigDecimal.ZERO;
		invertStatusReport.phaseTWattOfEps = BigDecimal.ZERO;
		invertStatusReport.pv1Voltage = BigDecimal.ZERO;
		invertStatusReport.pv2Voltage = BigDecimal.ZERO;
		invertStatusReport.pv3Voltage = BigDecimal.ZERO;
		invertStatusReport.pv4Voltage = BigDecimal.ZERO;
		invertStatusReport.pv1Current = BigDecimal.ZERO;
		invertStatusReport.pv2Current = BigDecimal.ZERO;
		invertStatusReport.pv3Current = BigDecimal.ZERO;
		invertStatusReport.pv4Current = BigDecimal.ZERO;
        return invertStatusReport;
    }
}
