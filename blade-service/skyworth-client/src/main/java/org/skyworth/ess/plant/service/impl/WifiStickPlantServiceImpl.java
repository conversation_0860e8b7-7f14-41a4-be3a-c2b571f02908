/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.excel.WifiStickPlantExcel;
import org.skyworth.ess.plant.mapper.WifiStickPlantMapper;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.plant.vo.WifiStickPlantVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * wifi棒对应站点表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service
public class WifiStickPlantServiceImpl extends BaseServiceImpl<WifiStickPlantMapper, WifiStickPlantEntity> implements IWifiStickPlantService {
	@Resource
	WifiStickPlantMapper wifiStickPlantMapper;
	@Override
	public boolean save(WifiStickPlantEntity wifiStickPlantEntity) {
		LambdaQueryWrapper<WifiStickPlantEntity> eq = Wrappers.<WifiStickPlantEntity>query().lambda()
			.eq(WifiStickPlantEntity::getDeviceSerialNumber, wifiStickPlantEntity.getDeviceSerialNumber());
//			.eq(WifiStickPlantEntity::getPlantId, wifiStickPlantEntity.getPlantId());
		Long userCount = baseMapper.selectCount(eq);
		if (userCount > 0L && Func.isEmpty(wifiStickPlantEntity.getId())) {
			throw new BusinessException("client.invert.serial.number.exist", wifiStickPlantEntity.getDeviceSerialNumber());
		}

		return super.save(wifiStickPlantEntity);
	}

	@Override
	public IPage<WifiStickPlantVO> selectWifiStickPlantPage(IPage<WifiStickPlantVO> page, WifiStickPlantVO WifiStickPlant) {
		return page.setRecords(baseMapper.selectWifiStickPlantPage(page, WifiStickPlant));
	}


	@Override
	public List<WifiStickPlantExcel> exportWifiStickPlant(Wrapper<WifiStickPlantEntity> queryWrapper) {
		List<WifiStickPlantExcel> WifiStickPlantList = baseMapper.exportWifiStickPlant(queryWrapper);
		//WifiStickPlantList.forEach(WifiStickPlant -> {
		//	WifiStickPlant.setTypeName(DictCache.getValue(DictEnum.YES_NO, WifiStickPlant.getType()));
		//});
		return WifiStickPlantList;
	}

	@Override
	public List<WifiStickPlantVO> queryDeviceCompany(AppVO appVO) {
		return baseMapper.queryDeviceCompany(appVO);
	}

	@Override
	public List<WifiStickPlantEntity> queryDeviceSerialNumberList(List<Long> list) {
		return baseMapper.queryDeviceSerialNumberList(list);
	}
	@Override
	public int batchDeleteLogicByPlantId(List<Long> plantIdList,String updateUserAccount) {
		return baseMapper.batchDeleteLogicByPlantId(plantIdList,updateUserAccount);
	}

	@Override
	public List<WifiStickPlantEntity> queryByDeviceSerialNumberList(List<String> list) {
		return baseMapper.queryByDeviceSerialNumberList(list);
	}
	@Override
	public List<WifiStickPlantEntity> queryOwnerData(Long createUser) {
		return baseMapper.queryOwnerData(createUser);
	}

	@Override
	public int updateDataByCondition(WifiStickPlantEntity updateOwner) {
		return baseMapper.updateDataByCondition(updateOwner);
	}

	@Override
	public List<WifiStickPlantEntity> getWifiStickInfo(List<WifiStickPlantEntity> wifiStickPlantEntities) {
		return wifiStickPlantMapper.getWifiStickInfo(wifiStickPlantEntities);
	}

	@Override
	public void updateStartupByBackstage(WifiStickPlantEntity wifiStickPlant) {
		wifiStickPlantMapper.updateStartupByBackstage(wifiStickPlant);
	}

	@Override
	public void batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account) {
		baseMapper.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber,account);
	}

	@Override
	public List<Long> getWifiStickInfoAndBatteryInfo(List<Long> longPlantIdList, String deviceSerialNumber) {
		return wifiStickPlantMapper.getWifiStickInfoAndBatteryInfo(longPlantIdList,deviceSerialNumber);
	}
}
