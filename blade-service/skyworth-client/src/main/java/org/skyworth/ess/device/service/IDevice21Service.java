/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.app.vo.AppDeviceInfo;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.excel.Device21Excel;
import org.skyworth.ess.device.vo.Device21VO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 设备/逆变器表，记录2.1数据 服务类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface IDevice21Service extends BaseService<Device21Entity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param Device21
	 * @return
	 */
	IPage<Device21VO> selectDevice21Page(IPage<Device21VO> page, Device21VO Device21);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<Device21Excel> exportDevice21(Wrapper<Device21Entity> queryWrapper);

	List<Device21Entity> getEntity(Wrapper<Device21Entity> queryWrapper);

	List<Device21Entity> getEntityIsDelete(Long plantId, String deviceSerialNumber);

	boolean save(Device21Entity device21);

	/**
	 * 查询逆变器信息
	 *
	 * @param plantId
	 * @return
	 */
	List<AppDeviceInfo> queryAppInverterInfo(Long plantId, String deviceSerialNumber);

	List<AppDeviceInfo> queryAppInverterInfoV2(Long plantId, String deviceSerialNumber);

	int updateByDeviceId(String deviceSn, String status);

	Long getRunDays(String deviceSerialNumber, Long plantId);

	int updateSetup(Map<String, Object> device21, Long plantId, String deviceSerialNumber);

	int deleteByPlantId(Long plantId);

	List<Device21Entity> queryDevice21InfoBySerialNumber(List<String> serialNumbers);

    List<Device21Entity> getListByDeviceSerialNumberCollect(List<String> deviceSerialNumberCollect);

	int deleteByPlantIdAndSn(Long plantId, String deviceSerialNumber);
}
