<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceLog21Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="Device21ResultMap" type="org.skyworth.ess.device.entity.DeviceLog21">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="modbus_protocol_version" property="modbusProtocolVersion"/>
        <result column="device_model" property="deviceModel"/>
        <result column="master_software_version" property="masterSoftwareVersion"/>
        <result column="master_software_build_date" property="masterSoftwareBuildDate"/>
        <result column="slave_firmware_version" property="slaveFirmwareVersion"/>
        <result column="slave_firmware_build_date" property="slaveFirmwareBuildDate"/>
        <result column="mppt_number" property="mpptNumber"/>
        <result column="rated_voltage" property="ratedVoltage"/>
        <result column="rated_frequency" property="ratedFrequency"/>
        <result column="rated_power" property="ratedPower"/>
        <result column="grid_phase_number" property="gridPhaseNumber"/>
        <result column="ems_firmware_version" property="emsFirmwareVersion"/>
        <result column="ems_firmware_build_date" property="emsFirmwareBuildDate"/>
        <result column="dcdc_firmware_version" property="dcdcFirmwareVersion"/>
        <result column="dcdc_firmware_build_date" property="dcdcFirmwareBuildDate"/>
        <result column="wifi_stick_serial_number" property="wifiStickSerialNumber"/>
        <result column="device_status" property="deviceStatus"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="selectDataByLatestTime" resultMap="Device21ResultMap">
        SELECT t1.*
        FROM device_log21 t1
        JOIN (
        SELECT plant_id, MAX(device_date_time) AS max_date,device_serial_number
        FROM device_log21
        GROUP BY plant_id,device_serial_number
        ) t2 ON t1.plant_id = t2.plant_id AND t1.device_date_time = t2.max_date and t1.device_serial_number=t2.device_serial_number  where (t1.synch_status is null or t1.synch_status='' or t1.synch_status='N');

    </select>
</mapper>

