package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppReportDetailVO;
import org.skyworth.ess.app.vo.AppReportHeaderVO;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.vo.DeviceEverydayTotalVO;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.constant.DictConstant;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppReportParallelServiceImpl {

    @Resource
    IBatteryCurrentStatusService batteryCurrentStatusService;

    @Resource
    private DeviceLog22ByDorisService deviceLog22ByDorisService;

    @Resource
    private IBatteryEverydayTotalService batteryEverydayTotalService;

	@Resource
	private IDevice23Service device23Service;
	@Resource
	private IDictBizClient dictBizClient;
    Function<BatteryEverydayTotalVO,BigDecimal> pvDailyEnergyOrParallelFun = vo -> vo.getAppPvlDailyGeneratingEnergySum().multiply(BizConstant.THOUSAND);
    Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyDischargeEnergyOrParallelFun = vo -> vo.getAppBatteryDailyDischargeEnergyParallel().multiply(BizConstant.THOUSAND);
    Function<BatteryEverydayTotalVO,BigDecimal> loadAddOrParallelFun = vo -> vo.getAppLoadAddBackup().multiply(BizConstant.THOUSAND);
    Function<BatteryEverydayTotalVO,BigDecimal> todayImportEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayImportEnergy;
    Function<BatteryEverydayTotalVO,BigDecimal> todayExportEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayExportEnergy;
    Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyChargeEnergyOrParallelFun = vo -> vo.getAppBatteryDailyChargeEnergyParallel().multiply(BizConstant.THOUSAND);

    public void getParallelHeaderCardData(AppVO appVO, AppReportHeaderVO resultAppReportHeaderVO) {

        BatteryCurrentStatusEntity queryBatteryCurrentStatusEntity = new BatteryCurrentStatusEntity();
        queryBatteryCurrentStatusEntity.setPlantId(appVO.getPlantId());
        queryBatteryCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
        log.info("queryPlantRunningStateV2 getBatteryData query current status begin");
        List<BatteryCurrentStatusEntity> dbBatteryCurrentStatuList = batteryCurrentStatusService.list(Condition.getQueryWrapper(queryBatteryCurrentStatusEntity));
        if(CollectionUtil.isEmpty(dbBatteryCurrentStatuList)) {
            return;
        }
        BatteryCurrentStatusEntity dbEntity = dbBatteryCurrentStatuList.get(0);
        // 数据库原始数据为 kwh
        resultAppReportHeaderVO.setBatteryDailyDischargeEnergyParallel(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getBatteryDailyDischargeEnergyParallel(),1));
        resultAppReportHeaderVO.setBatteryAccumulatedDischargeEnergyParallel(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getBatteryAccumulatedDischargeEnergyParallel(),1));
        resultAppReportHeaderVO.setPvlDailyGeneratingEnergySum(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getPvlDailyGeneratingEnergySum(),1));
        resultAppReportHeaderVO.setPvlAccumulatedEnergySum(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getPvlAccumulatedEnergySum(),1));
		// 和非并机字段保持一致，app端不用改
		resultAppReportHeaderVO.setBatteryTodayDischargeEnergy(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getBatteryDailyDischargeEnergyParallel(),1));
		resultAppReportHeaderVO.setBatteryAccumulatedDischargeEnergy(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getBatteryAccumulatedDischargeEnergyParallel(),1));
		resultAppReportHeaderVO.setTodayEnergy(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getPvlDailyGeneratingEnergySum(),1));
		resultAppReportHeaderVO.setTotalEnergy(DataUnitConversionUtil.getKwChangeEnergyResult(dbEntity.getPvlAccumulatedEnergySum(),1));

		// 原始数据为 wh
        resultAppReportHeaderVO.setTodayImportEnergy(DataUnitConversionUtil.getChangeEnergyResult(dbEntity.getTodayImportEnergy(),1));
        resultAppReportHeaderVO.setTotalImportEnergy(DataUnitConversionUtil.getChangeEnergyResult(dbEntity.getAccumulatedEnergyOfPositive(),1));
        SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.PATTERN_DATE);
        SimpleDateFormat formatter1 = new SimpleDateFormat(DateUtil.PATTERN_DATETIME);
        String formatStr = formatter1.format(dbEntity.getDeviceDateTime());
        log.info("app header battery db date before : {} ", formatStr);
        Date deviceDateTime = null;
        try {
            deviceDateTime = formatter.parse(formatStr);
            Date now = new Date();
            log.info("app header battery db date : {} ", formatter1.format(deviceDateTime));
            log.info("app header battery now date : {} ", formatter.format(now));
            // 判断是否为今天,不为今天，则天数据为0
            if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
                resultAppReportHeaderVO.setBatteryDailyDischargeEnergyParallel(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
                resultAppReportHeaderVO.setPvlDailyGeneratingEnergySum(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
                resultAppReportHeaderVO.setTodayImportEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
				resultAppReportHeaderVO.setBatteryTodayDischargeEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
				resultAppReportHeaderVO.setTodayEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

		//并机时 也需要查询逆变器模式
		LambdaQueryWrapper<Device23Entity> queryWrapper = Wrappers.<Device23Entity>query().lambda()
			.eq(Device23Entity::getDeviceSerialNumber,appVO.getDeviceSerialNumber())
			.eq(Device23Entity::getPlantId, appVO.getPlantId());

		Device23Entity device23 = device23Service.getOne(queryWrapper);
		resultAppReportHeaderVO.setHybridWorkMode("");
		if (ObjectUtil.isNotNull(device23) && ValidationUtil.isNotEmpty(device23.getHybridWorkMode())) {
			List<DictBiz> inverterMode = dictBizClient.getListByLang(DictConstant.INVERTER_MODE, CommonConstant.CURRENT_LANGUAGE_EN).getData();
			DictBiz snj = inverterMode.stream()
				.filter(dict -> Func.equals(dict.getDictKey(), device23.getHybridWorkMode()))
				.filter(dict -> Func.equals(dict.getLanguage(), CommonConstant.CURRENT_LANGUAGE_EN))
				.filter(dict -> Func.equals(dict.getAttribute2(), "snj"))
				.findFirst()
				.orElse(null);

			if (ObjectUtil.isNotNull(snj)) {
				resultAppReportHeaderVO.setHybridWorkMode(snj.getDictValue());
			}
		}
    }

    public AppReportHeaderVO getParallelReportData(AppVO appVO) {
        AppReportHeaderVO resultAppReportVO = new AppReportHeaderVO();
        try {
            // 获取pv、grid数据 (饼图)
            this.getFromPvAndGrid(appVO, resultAppReportVO);
            // 获取折线图数据
            this.getLineChart(appVO, resultAppReportVO);
        }catch (Exception e) {
            log.error("getParallelReportData : {}" ,e.getMessage());
        }

        return resultAppReportVO;
    }

    private void getLineChart(AppVO appVO, AppReportHeaderVO resultAppReportVO) throws ParseException {
        if (0 == appVO.getType()) {
            AppReportDetailVO hourReport = this.getHoursReportV2(appVO);
            resultAppReportVO.setDailyReport(hourReport);
        } else if (1 == appVO.getType()) {
            // 周报表，过去7天
            AppReportDetailVO dailyReport = this.getWeekReportV2(appVO);
            resultAppReportVO.setWeekReport(dailyReport);
        }
        else if (2 == appVO.getType()) {
            // 月报表
            AppReportDetailVO monthReport = this.getMonthReportV2(appVO);
            resultAppReportVO.setMonthlyReport(monthReport);
        } else if (3 == appVO.getType()) {
            // 年报表
            AppReportDetailVO annualReport = this.getAnnualReportV2(appVO);
            resultAppReportVO.setAnnualReport(annualReport);
        }
    }

    private AppReportDetailVO getWeekReportV2(AppVO appVO) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = simpleDateFormat.parse(appVO.getDataScope());
        Instant instant = parse.toInstant();
        LocalDate endLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate beginLocalDate = endLocalDate.plusDays(-7L);
        Function<QueryCondition, List<BatteryEverydayTotalVO>> dayFun = batteryEverydayTotalService::parallelWeekEstimateV2;
        // 查询数据库
        List<BatteryEverydayTotalVO> dailyReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO,beginLocalDate,endLocalDate,dayFun);
        // 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
//        BiFunction< BigDecimal, BigDecimal, BigDecimal> biFunction = BigDecimal::multiply;

//        test(biFunction,dailyReportList,pvDailyEnergyOrParallelFun,loadAddOrParallelFun,new BigDecimal("1000"));
        AppReportDetailVO dailyReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO,dailyReportList,pvDailyEnergyOrParallelFun,batteryDailyDischargeEnergyOrParallelFun,loadAddOrParallelFun,todayImportEnergyOrParallelFun,
                todayExportEnergyOrParallelFun,batteryDailyChargeEnergyOrParallelFun);
        // 补全全部天数，集合为无序的
        ReportUtil.completionDay(dailyReport, beginLocalDate, endLocalDate, "MM-dd");
        return dailyReport;
    }

//    private void test(BiFunction<BigDecimal, BigDecimal, BigDecimal> biFunction,List<BatteryEverydayTotalVO> dailyReportList,
//                      Function<BatteryEverydayTotalVO, BigDecimal> function,Function<BatteryEverydayTotalVO, BigDecimal> function1,BigDecimal mul) {
//        Function<BatteryEverydayTotalVO,BigDecimal> pvDailyEnergyOrParallelFun = vo -> vo.getAppPvlDailyGeneratingEnergySum();
//        Function<BatteryEverydayTotalVO,BigDecimal> loadAddOrParallelFun = vo -> vo.getAppLoadAddBackup();
//        BatteryEverydayTotalVO vi = new BatteryEverydayTotalVO();
//        BiFunction<Function<BatteryEverydayTotalVO, BigDecimal>, BigDecimal, BigDecimal> multiplyBy =
//                (function, multiplier) -> {
//                    pvDailyEnergyOrParallelFun
//                    return null;
//                } ;
//
//        for(BatteryEverydayTotalVO vo : dailyReportList) {
//            biFunction.apply(function.apply(vo),mul);
//            biFunction.apply(function1.apply(vo),mul);
//        }
//    }
    private AppReportDetailVO getMonthReportV2(AppVO appVO) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
        Instant instant = dataScope.toInstant();
        LocalDate month = instant.atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate beginLocalDate = month.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endLocalDate = month.with(TemporalAdjusters.lastDayOfMonth());
        Function<QueryCondition, List<BatteryEverydayTotalVO>> monthFun = batteryEverydayTotalService::parallelMonthEstimateV2;
        // 查询数据库
        List<BatteryEverydayTotalVO> monthReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO,beginLocalDate,endLocalDate,monthFun);
        // 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
        AppReportDetailVO monthReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO,monthReportList,pvDailyEnergyOrParallelFun,batteryDailyDischargeEnergyOrParallelFun,loadAddOrParallelFun,todayImportEnergyOrParallelFun,
                todayExportEnergyOrParallelFun,batteryDailyChargeEnergyOrParallelFun);
        // 补齐数据库中不存在的 天值
        ReportUtil.completionDay(monthReport, beginLocalDate, endLocalDate, "dd");
        return monthReport;
    }

    private AppReportDetailVO getAnnualReportV2(AppVO appVO) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
        Instant instant = dataScope.toInstant();
        LocalDate year = instant.atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = year.with(TemporalAdjusters.lastDayOfYear());
        LocalDate beginLocalDate = year.with(TemporalAdjusters.firstDayOfYear());
        Function<QueryCondition, List<BatteryEverydayTotalVO>> annualFun = batteryEverydayTotalService::parallelAnnualEstimateV2;
        // 查询数据库
        List<BatteryEverydayTotalVO> annualReportList = ReportUtil.getBatteryEverydayTotalDbData(appVO,beginLocalDate,endLocalDate,annualFun);
        // 将数据库中的值转成 map的key,value形式，非并机的周月年取值字段一样，但是和并机不同，两边定义不同的函数取值
        AppReportDetailVO annualReport = ReportUtil.setWeekMonthAnnualToKeyValue(appVO,annualReportList,pvDailyEnergyOrParallelFun,batteryDailyDischargeEnergyOrParallelFun,loadAddOrParallelFun,todayImportEnergyOrParallelFun,
                todayExportEnergyOrParallelFun,batteryDailyChargeEnergyOrParallelFun);
        // 补齐数据库中不存在的 月份值
        ReportUtil.completionMonth(annualReport);
        return annualReport;
    }
    private AppReportDetailVO getHoursReportV2(AppVO appVO) throws ParseException {
        // 天报表，每5分钟累计统计
        QueryDeviceLog22Condition query = ReportUtil.getQueryDeviceLog22Condition(appVO);
        // 查询结果按升序排列，同一时间段，后面的覆盖前面的
        log.info("queryPlantRunningStateV2 getHoursReportV2  query log22 begin");
        List<DeviceLog22VO> deviceLog22VoList = deviceLog22ByDorisService.appParallelReportEstimateV2(query);
        log.info("queryPlantRunningStateV2 getHoursReportV2  query log22 end");
        // mysql 测试数据
//		List<DeviceLog22VO> deviceLog22VoList = batteryEverydayTotalService.appReportEstimate(query);

        Function<DeviceLog22VO,BigDecimal> pvFun = DeviceLog22VO::getAppTotallyInputDcWattSum;
        Function<DeviceLog22VO,BigDecimal> batteryPowerSumFun = DeviceLog22VO::getAppBatteryPowerSum;
        Function<DeviceLog22VO,BigDecimal> loadAddBackupFun = DeviceLog22VO::getAppLoadAddBackup;
        Function<DeviceLog22VO,BigDecimal> phaseL1WattOfGridSumFun = DeviceLog22VO::getAppPhaseL1WattOfGridSum;
        Function<DeviceLog22VO,BigDecimal> phaseL2WattOfGridSumFun = DeviceLog22VO::getAppPhaseL2WattOfGridSum;
        Function<DeviceLog22VO,BigDecimal> phaseL3WattOfGridSumFun = DeviceLog22VO::getAppPhaseL3WattOfGridSum;
        Function<DeviceLog22VO,BigDecimal> batterySocFun = DeviceLog22VO::getAppBatterySoc;
        return  ReportUtil.calHoursReportData(deviceLog22VoList,pvFun,batteryPowerSumFun,loadAddBackupFun,
                phaseL1WattOfGridSumFun,phaseL2WattOfGridSumFun,phaseL3WattOfGridSumFun,batterySocFun);

    }

    private void getFromPvAndGrid(AppVO appVO, AppReportHeaderVO appReportHeaderVO) throws ParseException {
        log.info("queryPlantRunningStateV2 getFromPvAndGrid  begin");
        // 查询日月年报表数据
        if (0 == appVO.getType()) {
            QueryDeviceLog22Condition query = ReportUtil.getQueryDeviceLog22Condition(appVO);
            // 当天报表 ,因为当天数据未写入 日统计表，只能在doris中查原始数据
            DeviceLog22 deviceLog22 = deviceLog22ByDorisService.appParallelDailyFromPvAndGrid(query);
            if (deviceLog22 == null) {
                deviceLog22 = new DeviceLog22();
            }
            DeviceEverydayTotalVO deviceEverydayTotalVO = this.setDeviceData(deviceLog22::getPvlDailyGeneratingEnergySum, deviceLog22::getTodayExportEnergy
                    , deviceLog22::getDailyEnergyOfLoadSum, deviceLog22::getDailySupportEnergySumToBackup
                    , deviceLog22::getBatteryDailyChargeEnergyParallel, deviceLog22::getTodayImportEnergy);
            this.setDailyPieData(appReportHeaderVO, deviceEverydayTotalVO);
        } else if (1 == appVO.getType()) {
            // 周报表，过去7天
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = simpleDateFormat.parse(appVO.getDataScope());
            Instant instant = parse.toInstant();
            LocalDate endLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate().plusDays(-1L);
            LocalDate beginLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate().plusDays(-7L);
            BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.parallelPieReport(
                    ReportUtil.getBatteryQueryCondition(appVO, beginLocalDate, endLocalDate));
            log.info("fromPvAndGrid week db: {} ", batteryEverydayTotalEntity);
            this.setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
        }else if (2 == appVO.getType()) {
            // 月报表
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
            Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
            Instant instant = dataScope.toInstant();
            LocalDate month = instant.atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate beginLocalDate = month.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate endLocalDate = month.with(TemporalAdjusters.lastDayOfMonth());
            BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.parallelPieReport(
                    ReportUtil.getBatteryQueryCondition(appVO, beginLocalDate, endLocalDate));
            log.info("fromPvAndGrid month db: {} ", batteryEverydayTotalEntity);
            this.setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
        } else if (3 == appVO.getType()) {
            // 年报表
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
            Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
            Instant instant = dataScope.toInstant();
            LocalDate year = instant.atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate endLocalDate = year.with(TemporalAdjusters.lastDayOfYear());
            LocalDate beginLocalDate = year.with(TemporalAdjusters.firstDayOfYear());
            BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.parallelPieReport(
                    ReportUtil.getBatteryQueryCondition(appVO, beginLocalDate, endLocalDate));
            log.info("fromPvAndGrid year db: {} ", batteryEverydayTotalEntity);
            this.setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
        }
    }

    private void setWeekMonthAnnualPieDataV2(AppReportHeaderVO appReportHeaderVO, BatteryEverydayTotalEntity batteryEverydayTotalEntity) {
        if (batteryEverydayTotalEntity == null) {
            batteryEverydayTotalEntity = new BatteryEverydayTotalEntity();
        }
        // 此处不用转换计算，在定时任务中处理此逻辑，定时任务中计算后的数据存储的值为 wh
        BigDecimal selfConsumed = batteryEverydayTotalEntity.getParallelSelfConsumed() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getParallelSelfConsumed();
        BigDecimal fedToGrid = batteryEverydayTotalEntity.getParallelFedToGrid() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getParallelFedToGrid();
        BigDecimal selfSufficiency = batteryEverydayTotalEntity.getParallelSelfSufficiency() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getParallelSelfSufficiency();
        BigDecimal fromGrid = batteryEverydayTotalEntity.getParallelFromGrid() == null ? BigDecimal.ZERO : batteryEverydayTotalEntity.getParallelFromGrid();
        ReportUtil.setWeekMonthAnnualPieData(appReportHeaderVO,selfConsumed,fedToGrid,selfSufficiency,fromGrid);
    }


    private DeviceEverydayTotalVO setDeviceData(Supplier<BigDecimal> pvlDailyGeneratingEnergySum, Supplier<BigDecimal> todayLoadEnergy,
                                                 Supplier<BigDecimal> dailyEnergyOfLoadSum, Supplier<BigDecimal> dailySupportEnergySumToBackup,
             Supplier<BigDecimal> batteryDailyChargeEnergyParallel
            , Supplier<BigDecimal> todayImportEnergy) {
        DeviceEverydayTotalVO deviceEverydayTotalVO = new DeviceEverydayTotalVO();
        deviceEverydayTotalVO.setSumPvlDailyGeneratingEnergySum(pvlDailyGeneratingEnergySum.get());
        deviceEverydayTotalVO.setSumTodayExportEnergy(todayLoadEnergy.get());
        deviceEverydayTotalVO.setSumDailyEnergyOfLoadSum(dailyEnergyOfLoadSum.get());
        deviceEverydayTotalVO.setSumDailySupportEnergySumToBackup(dailySupportEnergySumToBackup.get());
        deviceEverydayTotalVO.setSumBatteryDailyChargeEnergyParallel(batteryDailyChargeEnergyParallel.get());
        deviceEverydayTotalVO.setSumTodayImportEnergy(todayImportEnergy.get());
        return deviceEverydayTotalVO;
    }


    private void setDailyPieData(AppReportHeaderVO appReportHeaderVO, DeviceEverydayTotalVO deviceEverydayTotalVO) {
        // 单位为 kwh，转成wh进行计算  避免 3.374kwh  + 1wh  进行计算式， 1被舍弃
        BigDecimal sumPvlDailyGeneratingEnergySum = deviceEverydayTotalVO.getSumPvlDailyGeneratingEnergySum() == null ? BigDecimal.ZERO :
                deviceEverydayTotalVO.getSumPvlDailyGeneratingEnergySum().multiply(BizConstant.THOUSAND);
        BigDecimal sumTodayExportEnergy = deviceEverydayTotalVO.getSumTodayExportEnergy() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumTodayExportEnergy();
        BigDecimal sumDailyEnergyOfLoadSum = deviceEverydayTotalVO.getSumDailyEnergyOfLoadSum() == null ? BigDecimal.ZERO :
                deviceEverydayTotalVO.getSumDailyEnergyOfLoadSum().multiply(BizConstant.THOUSAND);
        BigDecimal sumDailySupportEnergySumToBackup = deviceEverydayTotalVO.getSumDailySupportEnergySumToBackup() == null ? BigDecimal.ZERO :
                deviceEverydayTotalVO.getSumDailySupportEnergySumToBackup().multiply(BizConstant.THOUSAND);
        BigDecimal sumBatteryDailyChargeEnergyParallel = deviceEverydayTotalVO.getSumBatteryDailyChargeEnergyParallel() == null ? BigDecimal.ZERO :
                deviceEverydayTotalVO.getSumBatteryDailyChargeEnergyParallel().multiply(BizConstant.THOUSAND);
        BigDecimal sumTodayImportEnergy = deviceEverydayTotalVO.getSumTodayImportEnergy() == null ? BigDecimal.ZERO : deviceEverydayTotalVO.getSumTodayImportEnergy();
        ReportUtil.setDailyPidData(appReportHeaderVO,sumPvlDailyGeneratingEnergySum,sumTodayExportEnergy,sumDailyEnergyOfLoadSum,
                sumDailySupportEnergySumToBackup,sumBatteryDailyChargeEnergyParallel,sumTodayImportEnergy);
    }

}
