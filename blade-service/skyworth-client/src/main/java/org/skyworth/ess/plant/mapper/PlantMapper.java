/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.excel.PlantExcel;
import org.skyworth.ess.plant.vo.PlantAgentViewVO;
import org.skyworth.ess.plant.vo.PlantVO;
import org.springblade.core.tenant.annotation.TenantIgnore;

import java.util.List;

/**
 * 站点信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface PlantMapper extends BaseMapper<PlantEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param plant
	 * @return
	 */
	List<PlantVO> selectPlantPage(@Param("page") IPage page, @Param("params") PlantVO plant,
								  @Param("userType") String userType, @Param("listSearchCondition") String listSearchCondition);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<PlantExcel> exportPlant(@Param("ew") Wrapper<PlantEntity> queryWrapper);

	int updateStatusById(@Param("id") long id, @Param("status") String status,
						 @Param("existUserTypeAlarm") Integer existUserTypeAlarm,
						 @Param("existAgentTypeAlarm") Integer existAgentTypeAlarm);

	int updatePlant(@Param("params") PlantEntity plantEntity);

	@TenantIgnore
	List<PlantEntity> queryPlant(@Param("params") PlantEntity eq);

	PlantEntity queryPlantIsDelete(@Param("id") Long id);

	List<JSONObject> queryPlantStatusCount();

	/**
	 * epc代理商管理获取站点信息
	 *
	 * @param deptId 代理商部门id
	 * @param page   入参
	 * @return List<PlantAgentViewVO>
	 * <AUTHOR>
	 * @since 2024/3/8 10:14
	 **/
	List<PlantAgentViewVO> getPlantAgentViewInfo(@Param("deptId") Long deptId, @Param("page") IPage page);

	int cleanPlantOperationUserId(@Param("userIdList") List<Long> userIdList);

	/**
	 * 删除代理商用户的同时，删除电站运维人员,删除无权限列表用户
	 *
	 * @param deptId  代理商id
	 * @param userIds 用户ids
	 * @return int
	 * <AUTHOR>
	 * @since 2024/3/21 14:06
	 **/
	int cleanPlantOperationUser(@Param("deptId") String deptId, @Param("userIds") List<Long> userIds);

	/**
	 * 删除代理商，需要删除电站的运维人员和运维团队,删除无权限列表用户
	 *
	 * @param deptIds 代理商id
	 * @return int
	 * <AUTHOR>
	 * @since 2024/3/21 14:06
	 **/
	int cleanPlantDeptIdAndOperationUser(@Param("deptIds") String deptIds);

	Boolean updatePlantUser(@Param("plantId") Long plantId,@Param("updateUser") Long updateUser, @Param("userId") Long userId);
}
