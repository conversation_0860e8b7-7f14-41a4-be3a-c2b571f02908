<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.dailyStatistics.mapper.DeviceLog22ByDorisMapper">

    <resultMap id="baseResult" type="org.skyworth.ess.device.entity.DeviceLog22">
        <id property="id" column="id"/>
        <result property="synchStatus" column="synch_status"/>
        <result property="phaseAVoltage" column="phase_a_voltage"/>
        <result property="phaseACurrent" column="phase_a_current"/>
        <result property="phaseAPower" column="phase_a_power"/>
        <result property="phaseAFrequency" column="phase_a_frequency"/>
        <result property="phaseBVoltage" column="phase_b_voltage"/>
        <result property="phaseBCurrent" column="phase_b_current"/>
        <result property="phaseBPower" column="phase_b_power"/>
        <result property="phaseBFrequency" column="phase_b_frequency"/>
        <result property="phaseCVoltage" column="phase_c_voltage"/>
        <result property="phaseCCurrent" column="phase_c_current"/>
        <result property="phaseCPower" column="phase_c_power"/>
        <result property="phaseCFrequency" column="phase_c_frequency"/>
        <result property="pv1Voltage" column="pv1_voltage"/>
        <result property="pv1Current" column="pv1_current"/>
        <result property="mppt1Power" column="mppt1_power"/>
        <result property="pv2Voltage" column="pv2_voltage"/>
        <result property="pv2Current" column="pv2_current"/>
        <result property="mppt2Power" column="mppt2_power"/>
        <result property="pv3Voltage" column="pv3_voltage"/>
        <result property="pv3Current" column="pv3_current"/>
        <result property="mppt3Power" column="mppt3_power"/>
        <result property="innerTemperature" column="inner_temperature"/>
        <result property="inverterMode" column="inverter_mode"/>
        <result property="errorCode" column="error_code"/>
        <result property="totalEnergy" column="total_energy"/>
        <result property="totalGenerationTime" column="total_generation_time"/>
        <result property="todayEnergy" column="today_energy"/>
        <result property="activePower" column="active_power"/>
        <result property="reactivePower" column="reactive_power"/>
        <result property="todayPeakPower" column="today_peak_power"/>
        <result property="powerFactor" column="power_factor"/>
        <result property="pv4Voltage" column="pv4_voltage"/>
        <result property="pv4Current" column="pv4_current"/>
        <result property="mppt4Power" column="mppt4_power"/>
        <result property="phaseRWattOfGrid" column="phase_r_watt_of_grid"/>
        <result property="phaseSWattOfGrid" column="phase_s_watt_of_grid"/>
        <result property="phaseTWattOfGrid" column="phase_t_watt_of_grid"/>
        <result property="accumulatedEnergyOfPositive" column="accumulated_energy_of_positive"/>
        <result property="accumulatedEnergyOfNegative" column="accumulated_energy_of_negative"/>
        <result property="phaseRWattOfLoad" column="phase_r_watt_of_load"/>
        <result property="phaseSWattOfLoad" column="phase_s_watt_of_load"/>
        <result property="phaseTWattOfLoad" column="phase_t_watt_of_load"/>
        <result property="accumulatedEnergyOfLoad" column="accumulated_energy_of_load"/>
        <result property="l1NPhaseVoltageOfGrid" column="l1_n_phase_voltage_of_grid"/>
        <result property="l2NPhaseVoltageOfGrid" column="l2_n_phase_voltage_of_grid"/>
        <result property="l3NPhaseVoltageOfGrid" column="l3_n_phase_voltage_of_grid"/>
        <result property="l1CurrentOfGrid" column="l1_current_of_grid"/>
        <result property="l2CurrentOfGrid" column="l2_current_of_grid"/>
        <result property="l3CurrentOfGrid" column="l3_current_of_grid"/>
        <result property="l1NPhaseVoltageOfLoad" column="l1_n_phase_voltage_of_load"/>
        <result property="l2NPhaseVoltageOfLoad" column="l2_n_phase_voltage_of_load"/>
        <result property="l3NPhaseVoltageOfLoad" column="l3_n_phase_voltage_of_load"/>
        <result property="l1CurrentOfLoad" column="l1_current_of_load"/>
        <result property="l2CurrentOfLoad" column="l2_current_of_load"/>
        <result property="l3CurrentOfLoad" column="l3_current_of_load"/>
        <result property="todayImportEnergy" column="today_import_energy"/>
        <result property="todayExportEnergy" column="today_export_energy"/>
        <result property="todayLoadEnergy" column="today_load_energy"/>
        <result property="frequencyOfGrid" column="frequency_of_grid"/>
        <result property="phaseRVoltageOfEps" column="phase_r_voltage_of_eps"/>
        <result property="phaseRCurrentOfEps" column="phase_r_current_of_eps"/>
        <result property="phaseRWattOfEps" column="phase_r_watt_of_eps"/>
        <result property="frequencyOfEps" column="frequency_of_eps"/>
        <result property="phaseSVoltageOfEps" column="phase_s_voltage_of_eps"/>
        <result property="phaseSCurrentOfEps" column="phase_s_current_of_eps"/>
        <result property="phaseSWattOfEps" column="phase_s_watt_of_eps"/>
        <result property="phaseTVoltageOfEps" column="phase_t_voltage_of_eps"/>
        <result property="phaseTCurrentOfEps" column="phase_t_current_of_eps"/>
        <result property="phaseTWattOfEps" column="phase_t_watt_of_eps"/>
        <result property="dailyEnergyToEps" column="daily_energy_to_eps"/>
        <result property="accumulatedEnergyToEps" column="accumulated_energy_to_eps"/>
        <result property="batterySoc" column="battery_soc"/>
        <result property="batteryTemperature" column="battery_temperature"/>
        <result property="batteryVoltage" column="battery_voltage"/>
        <result property="batteryCurrent" column="battery_current"/>
        <result property="batteryPower" column="battery_power"/>
        <result property="batteryDailyChargeEnergy" column="battery_daily_charge_energy"/>
        <result property="batteryAccumulatedChargeEnergy" column="battery_accumulated_charge_energy"/>
        <result property="batteryDailyDischargeEnergy" column="battery_daily_discharge_energy"/>
        <result property="batteryAccumulatedDischargeEnergy" column="battery_accumulated_discharge_energy"/>
        <result property="errorMessage4" column="error_message4"/>
        <result column="plant_id" property="plantId"/>
        <result property="deviceDateTime" column="device_date_time"/>
        <result property="modbusProtocolVersion" column="modbus_protocol_version"/>
        <result property="deviceSerialNumber" column="device_serial_number"/>
        <result property="createUserAccount" column="create_user_account"/>
        <result property="updateUserAccount" column="update_user_account"/>
        <result property="batteryMaximumCellVoltage" column="battery_maximum_cell_voltage"/>
        <result property="batteryMinimumCellVoltage" column="battery_minimum_cell_voltage"/>
        <result property="batteryMaximumCellTemperature" column="battery_maximum_cell_temperature"/>
        <result property="batteryMinimumCellTemperature" column="battery_minimum_cell_temperature"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result property="numberOfBattery" column="number_of_battery"/>
        <result property="pvTotalInputPower" column="pv_total_input_power"/>
        <result property="soh" column="soh"/>
        <result column="l1_phase_voltage_of_ac_couple" property="l1PhaseVoltageOfAcCouple"/>
        <result column="l1_phase_current_of_ac_couple" property="l1PhaseCurrentOfAcCouple"/>
        <result column="l1_phase_power_of_ac_couple" property="l1PhasePowerOfAcCouple"/>
        <result column="l2_phase_voltage_of_ac_couple" property="l2PhaseVoltageOfAcCouple"/>
        <result column="l2_phase_current_of_ac_couple" property="l2PhaseCurrentOfAcCouple"/>
        <result column="l2_phase_power_of_ac_couple" property="l2PhasePowerOfAcCouple"/>
        <result column="l3_phase_voltage_of_ac_couple" property="l3PhaseVoltageOfAcCouple"/>
        <result column="l3_phase_current_of_ac_couple" property="l3PhaseCurrentOfAcCouple"/>
        <result column="l3_phase_power_of_ac_couple" property="l3PhasePowerOfAcCouple"/>
        <result column="frequency_of_ac_couple" property="frequencyOfAcCouple"/>
        <result column="energy_today_of_ac_couple_kwh" property="energyTodayOfAcCoupleKwh"/>
        <result column="energy_total_of_ac_couple" property="energyTotalOfAcCouple"/>
        <result column="phase_l1_watt_of_grid_sum" property="phaseL1WattOfGridSum"/>
        <result column="phase_l2_watt_of_grid_sum" property="phaseL2WattOfGridSum"/>
        <result column="phase_l3_watt_of_grid_sum" property="phaseL3WattOfGridSum"/>
        <result column="phase_l1_watt_of_load_sum" property="phaseL1WattOfLoadSum"/>
        <result column="phase_l2_watt_of_load_sum" property="phaseL2WattOfLoadSum"/>
        <result column="phase_l3_watt_of_load_sum" property="phaseL3WattOfLoadSum"/>
        <result column="daily_energy_of_load_sum" property="dailyEnergyOfLoadSum"/>
        <result column="monthly_energy_of_load_sum" property="monthlyEnergyOfLoadSum"/>
        <result column="accumulated_energy_of_load_sum" property="accumulatedEnergyOfLoadSum"/>
        <result column="phase_l1_watt_sum_of_backup" property="phaseL1WattSumOfBackup"/>
        <result column="phase_l2_watt_sum_of_backup" property="phaseL2WattSumOfBackup"/>
        <result column="phase_l3_watt_sum_of_backup" property="phaseL3WattSumOfBackup"/>
        <result column="phase_l1_apparent_power_sum_of_backup" property="phaseL1ApparentPowerSumOfBackup"/>
        <result column="phase_l2_apparent_power_sum_of_backup" property="phaseL2ApparentPowerSumOfBackup"/>
        <result column="phase_l3_apparent_power_sum_of_backup" property="phaseL3ApparentPowerSumOfBackup"/>
        <result column="daily_support_energy_sum_to_backup" property="dailySupportEnergySumToBackup"/>
        <result column="accumulated_support_energy_sum_to_backup" property="accumulatedSupportEnergySumToBackup"/>
        <result column="phase_l1_watt_sum_of_generator" property="phaseL1WattSumOfGenerator"/>
        <result column="phase_l2_watt_sum_of_generator" property="phaseL2WattSumOfGenerator"/>
        <result column="phase_l3_watt_sum_of_generator" property="phaseL3WattSumOfGenerator"/>
        <result column="phase_l1_apparent_power_sum_of_generator" property="phaseL1ApparentPowerSumOfGenerator"/>
        <result column="phase_l2_apparent_power_sum_of_generator" property="phaseL2ApparentPowerSumOfGenerator"/>
        <result column="phase_l3_apparent_power_sum_of_generator" property="phaseL3ApparentPowerSumOfGenerator"/>
        <result column="generator_today_energy_sum" property="generatorTodayEnergySum"/>
        <result column="generator_total_energy_sum" property="generatorTotalEnergySum"/>
        <result column="pvl_daily_generating_energy_sum" property="pvlDailyGeneratingEnergySum"/>
        <result column="pvl_accumulated_energy_sum" property="pvlAccumulatedEnergySum"/>
        <result column="totally_input_dc_watt_sum" property="totallyInputDcWattSum"/>
        <result column="battery_power_sum" property="batteryPowerSum"/>
        <result column="battery_daily_charge_energy_parallel" property="batteryDailyChargeEnergyParallel"/>
        <result column="battery_accumulated_charge_energy_parallel" property="batteryAccumulatedChargeEnergyParallel"/>
        <result column="battery_daily_discharge_energy_parallel" property="batteryDailyDischargeEnergyParallel"/>
        <result column="battery_accumulated_discharge_energy_parallel" property="batteryAccumulatedDischargeEnergyParallel"/>
        <result column="is_parallel_mode" property="isParallelMode"/>
    </resultMap>

    <select id="selectDataByLatestTime" resultMap="baseResult">
        SELECT t1.*
        FROM device_log22 t1
        JOIN (SELECT plant_id, MAX(device_date_time) AS max_date, device_serial_number
        FROM device_log22 WHERE DATE(device_date_time) BETWEEN CURDATE() - INTERVAL 1 DAY AND CURDATE()
        GROUP BY plant_id, device_serial_number) t2
        ON t1.plant_id = t2.plant_id AND t1.device_date_time = t2.max_date and
        t1.device_serial_number = t2.device_serial_number
    </select>
    <select id="selectDailyData" resultMap="baseResult">
        SELECT t1.*
        FROM device_log22 t1
        JOIN (SELECT plant_id, MAX(device_date_time) AS max_date, device_serial_number
        FROM device_log22
        where device_date_time BETWEEN CONVERT_TZ(#{beginTime} , '+00:00', #{timeZone}) and CONVERT_TZ(#{endTime} , '+00:00', #{timeZone}) and plant_id=#{plantId}
        GROUP BY plant_id, device_serial_number) t2
        ON t1.plant_id = t2.plant_id AND t1.device_date_time = t2.max_date and
        t1.device_serial_number = t2.device_serial_number
        where device_date_time BETWEEN CONVERT_TZ(#{beginTime} , '+00:00', #{timeZone}) and CONVERT_TZ(#{endTime} , '+00:00', #{timeZone}) and t1.plant_id=#{plantId}

    </select>

    <select id="stateCurve" resultType="com.alibaba.fastjson.JSONObject">
        select
        DATE_FORMAT(CONVERT_TZ(dl.device_date_time,'+00:00',#{condition.timeZone}), '%Y-%m-%d %H:%i:%s') as
        deviceDateTimeForCal,
        DATE_FORMAT(CONVERT_TZ(dl.device_date_time,'+00:00',#{condition.timeZone}), '%H:%i') as deviceDateTime,
        CAST(IFNULL(dl.battery_voltage, 0) AS DECIMAL(10,2)) as batteryVoltage,
        CAST(IFNULL(dl.battery_current, 0) AS DECIMAL(10,2)) as batteryCurrent,
        CAST(IFNULL(dl.battery_maximum_cell_voltage, 0) AS DECIMAL(10,2)) as batteryMaximumCellVoltage,
        CAST(IFNULL(dl.battery_minimum_cell_voltage, 0) AS DECIMAL(10,2)) as batteryMinimumCellVoltage,
        CAST(IFNULL(dl.battery_maximum_cell_temperature, 0) AS DECIMAL(10,2)) as batteryMaximumCellTemperature,
        CAST(IFNULL(dl.battery_minimum_cell_temperature, 0) AS DECIMAL(10,2)) as batteryMinimumCellTemperature,
        CAST(IFNULL(dl.battery_power, 0) AS DECIMAL(10,2)) as batteryPower,
        CAST(IFNULL(dl.battery_soc, 0) AS DECIMAL(10,2)) as batterySoc
        from device_log22 dl
        where dl.device_date_time BETWEEN CONVERT_TZ(#{condition.startDateTime}, #{condition.timeZone}, '+00:00')
        and CONVERT_TZ(#{condition.endDateTime}, #{condition.timeZone}, '+00:00')
        and dl.plant_id = #{condition.plantId}
        and dl.device_serial_number = #{condition.deviceSerialNumber}
        order by dl.device_date_time
    </select>

    <select id="selectStatusReportByTime" resultType="org.skyworth.ess.device.vo.InvertStatusReport">
        select
        CAST(IFNULL(mppt1_power, 0) AS DECIMAL(10,2)) mppt1Power,
        CAST(IFNULL(mppt2_power, 0) AS DECIMAL(10,2)) mppt2Power,
        CAST(IFNULL(mppt3_power, 0) AS DECIMAL(10,2)) mppt3Power,
        CAST(IFNULL(mppt4_power, 0) AS DECIMAL(10,2)) mppt4Power,
        CAST(IFNULL(phase_r_watt_of_grid, 0) AS DECIMAL(10,2)) phaseRWattOfGrid,
        CAST(IFNULL(phase_s_watt_of_grid, 0) AS DECIMAL(10,2)) phaseSWattOfGrid,
        CAST(IFNULL(phase_t_watt_of_grid, 0) AS DECIMAL(10,2)) phaseTWattOfGrid,
        CAST(IFNULL(phase_r_watt_of_load, 0) AS DECIMAL(10,2)) phaseRWattOfLoad,
        CAST(IFNULL(phase_s_watt_of_load, 0) AS DECIMAL(10,2)) phaseSWattOfLoad,
        CAST(IFNULL(phase_t_watt_of_load, 0) AS DECIMAL(10,2)) phaseTWattOfLoad,
        CAST(IFNULL(phase_r_watt_of_eps, 0) AS DECIMAL(10,2)) phaseRWattOfEps,
        CAST(IFNULL(phase_s_watt_of_eps, 0) AS DECIMAL(10,2)) phaseSWattOfEps,
        CAST(IFNULL(phase_t_watt_of_eps, 0) AS DECIMAL(10,2)) phaseTWattOfEps,
        CAST(IFNULL(l1_phase_power_of_ac_couple, 0) AS DECIMAL(10,2)) l1PhasePowerOfAcCouple,
        CAST(IFNULL(l2_phase_power_of_ac_couple, 0) AS DECIMAL(10,2)) l2PhasePowerOfAcCouple,
        CAST(IFNULL(l3_phase_power_of_ac_couple, 0) AS DECIMAL(10,2)) l3PhasePowerOfAcCouple,
        DATE_FORMAT(CONVERT_TZ(device_date_time,'+00:00', #{condition.timeZone}),'%H:%i') deviceDateTime,
        DATE_FORMAT(CONVERT_TZ(device_date_time,'+00:00', #{condition.timeZone}), '%Y-%m-%d') deviceDateTimeForDay,
        DATE_FORMAT(CONVERT_TZ(device_date_time,'+00:00', #{condition.timeZone}), '%Y-%m-%d %H:%i')  deviceDateTimeForCal,
        CAST(IFNULL(pv1_voltage, 0) AS DECIMAL(10,2)) pv1Voltage,
        CAST(IFNULL(pv2_voltage, 0) AS DECIMAL(10,2)) pv2Voltage,
        CAST(IFNULL(pv3_voltage, 0) AS DECIMAL(10,2)) pv3Voltage,
        CAST(IFNULL(pv4_voltage, 0) AS DECIMAL(10,2)) pv4Voltage,
        CAST(IFNULL(pv1_current, 0) AS DECIMAL(10,2)) pv1Current,
        CAST(IFNULL(pv2_current, 0) AS DECIMAL(10,2)) pv2Current,
        CAST(IFNULL(pv3_current, 0) AS DECIMAL(10,2)) pv3Current,
        CAST(IFNULL(pv4_current, 0) AS DECIMAL(10,2)) pv4Current
        from device_log22
        where device_date_time BETWEEN CONVERT_TZ(#{condition.startDateTime}, #{condition.timeZone}, '+00:00')
        and CONVERT_TZ(#{condition.endDateTime}, #{condition.timeZone}, '+00:00')
        and device_serial_number = #{condition.deviceSerialNumber}
        and plant_id = #{condition.plantId}
        order by device_date_time
    </select>
    <select id="appReportEstimate" resultType="org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO">
        select id,appTotalDate, battery_accumulated_charge_energy as appBatteryAccumulatedChargeEnergy,
        battery_daily_discharge_energy as appBatteryDailyDischargeEnergy,today_energy as pvTodayEnergy
        from
        (select id,DATE_FORMAT(device_date_time,'%H:00') as appTotalDate,ifnull( battery_accumulated_charge_energy,0) as
        battery_accumulated_charge_energy,
        ifnull (battery_daily_discharge_energy,0) as battery_daily_discharge_energy,ifnull (today_energy,0) as
        today_energy
        from device_log22 dl where 1=1 and dl.device_date_time BETWEEN
        STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','00:00:00'),'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','03:59:59'),'%Y-%m-%d
        %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} order by dl.id desc limit 1
        UNION all
        select id,DATE_FORMAT(device_date_time,'%H:00') as appTotalDate,ifnull( battery_accumulated_charge_energy,0) as
        battery_accumulated_charge_energy,
        ifnull (battery_daily_discharge_energy,0) as battery_daily_discharge_energy,ifnull (today_energy,0) as
        today_energy
        from device_log22 dl where 1=1 and dl.device_date_time BETWEEN
        STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','04:00:00'),'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','07:59:59'),'%Y-%m-%d
        %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} order by dl.id desc limit 1
        UNION all
        select id,DATE_FORMAT(device_date_time,'%H:00') as appTotalDate,ifnull( battery_accumulated_charge_energy,0) as
        battery_accumulated_charge_energy,
        ifnull (battery_daily_discharge_energy,0) as battery_daily_discharge_energy,ifnull (today_energy,0) as
        today_energy
        from device_log22 dl where 1=1 and dl.device_date_time BETWEEN
        STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','08:00:00'),'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','11:59:59'),'%Y-%m-%d
        %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} order by dl.id desc limit 1
        UNION all
        select id,DATE_FORMAT(device_date_time,'%H:00') as appTotalDate,ifnull( battery_accumulated_charge_energy,0) as
        battery_accumulated_charge_energy,
        ifnull (battery_daily_discharge_energy,0) as battery_daily_discharge_energy,ifnull (today_energy,0) as
        today_energy
        from device_log22 dl where 1=1 and dl.device_date_time BETWEEN
        STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','12:00:00'),'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','15:59:59'),'%Y-%m-%d
        %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} order by dl.id desc limit 1
        UNION all
        select id,DATE_FORMAT(device_date_time,'%H:00') as appTotalDate,ifnull( battery_accumulated_charge_energy,0) as
        battery_accumulated_charge_energy,
        ifnull (battery_daily_discharge_energy,0) as battery_daily_discharge_energy,ifnull (today_energy,0) as
        today_energy
        from device_log22 dl where 1=1 and dl.device_date_time BETWEEN
        STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','16:00:00'),'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','19:59:59'),'%Y-%m-%d
        %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} order by dl.id desc limit 1
        UNION all
        select id,DATE_FORMAT(device_date_time,'%H:00') as appTotalDate,ifnull( battery_accumulated_charge_energy,0) as
        battery_accumulated_charge_energy,
        ifnull (battery_daily_discharge_energy,0) as battery_daily_discharge_energy,ifnull (today_energy,0) as
        today_energy
        from device_log22 dl where 1=1 and dl.device_date_time BETWEEN
        STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','20:00:00'),'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(CONCAT(STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d'),' ','23:59:59'),'%Y-%m-%d
        %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} order by dl.id desc limit 1
        ) t
        order by id desc
    </select>

    <select id="appReportEstimateV2" resultType="org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO">
        select id,device_date_time as appDeviceDateTime,DATE_FORMAT(device_date_time,'%H:%i') as appTotalDate,
        ifnull(pv_total_input_power,0) as appPvTotalInputPower,
        ifnull ( battery_power,0) as appBatteryPower,

        ifnull (phase_r_watt_of_load,0) as appPhaserWattOfLoad,
        ifnull(phase_s_watt_of_load,0) as appPhasesWattOfLoad,
        ifnull(phase_t_watt_of_load,0) as appPhasetWattOfLoad,
        ifnull (phase_r_watt_of_eps,0) as appPhaserWattOfEps,
        ifnull (phase_s_watt_of_eps,0) as appPhasesWattOfEps,
        ifnull (phase_t_watt_of_eps,0) as appPhasetWattOfEps,
        ifnull (phase_r_watt_of_load,0) +
        ifnull (phase_s_watt_of_load,0) +
        ifnull (phase_t_watt_of_load,0) +
        ifnull ( phase_r_watt_of_eps,0) +
        ifnull ( phase_s_watt_of_eps,0) +
        ifnull ( phase_t_watt_of_eps,0) as appLoadAddEps,

        ifnull (phase_r_watt_of_grid,0) as appPhaserWattOfGrid,
        ifnull (phase_s_watt_of_grid,0) as appPhasesWattOfGrid,
        ifnull (phase_t_watt_of_grid,0) as appPhasetWattOfGrid,

        ifnull (l1_phase_power_of_ac_couple,0) +
        ifnull (l2_phase_power_of_ac_couple,0) +
        ifnull (l3_phase_power_of_ac_couple,0) appOtherPv,

        ifnull (battery_soc,0) as appBatterySoc
        from device_log22 dl where dl.device_date_time BETWEEN
        STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} and dl.device_serial_number = #{queryCondition.deviceSerialNumber}
        order by dl.device_date_time asc
    </select>

    <select id="appDailyFromPvAndGrid" resultType="org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO">
        select dl.device_date_time as deviceDateTime,
        IFNULL( dl.today_energy,0) as originalTodayEnergy,
        ifnull(dl.today_export_energy ,0) as originalTodayExportEnergy,
        ifnull(dl.today_load_energy,0) as originalTodayLoadEnergy,
        ifnull(dl.daily_energy_to_eps,0) as originalDailyEnergyToEps,
        ifnull(dl.battery_daily_charge_energy ,0) as originalBatteryDailyChargeEnergy,
        ifnull(dl.today_import_energy ,0) as originalTodayImportEnergy,
        ifnull(dl.energy_today_of_ac_couple_wh ,0) as energyTodayOfAcCoupleWh
        from device_log22 dl where
        dl.device_date_time BETWEEN
        STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} and dl.device_serial_number = #{queryCondition.deviceSerialNumber}
        order by dl.device_date_time desc limit 1
    </select>
    <select id="getSummaryStateCurve" resultType="com.alibaba.fastjson.JSONObject">
        select sum(IFNULL(a.phase_a_power, 0)) as phaseAPower,
        sum(IFNULL(a.phase_b_power, 0)) as phaseBPower,
        sum(IFNULL(a.phase_c_power, 0)) as phaseCPower,
        sum(IFNULL(a.phase_r_watt_of_grid, 0)) as phaseRWattOfGrid,
        sum(IFNULL(a.phase_s_watt_of_grid, 0)) as phaseSWattOfGrid,
        sum(IFNULL(a.phase_t_watt_of_grid, 0)) as phaseTWattOfGrid
        from device_log22 a
        join (select max(id) id
        from device_log22
        where device_date_time between #{sldt} and #{eldt}
        group by device_serial_number) b on
        a.id = b.id
    </select>

    <select id="queryLatestData" resultMap="baseResult">
        select * from device_log22 dl where dl.plant_id = #{queryCondition.plantId} and dl.device_serial_number =#{queryCondition.deviceSerialNumber}
        and dl.is_parallel_mode = #{queryCondition.isParallelMode} and device_date_time BETWEEN #{queryCondition.beginTime} and #{queryCondition.endTime}
        order by dl.id desc limit 1;
    </select>

    <select id="appParallelDailyFromPvAndGrid" resultMap="baseResult">
        select dl.device_date_time as device_date_time,
        IFNULL( dl.pvl_daily_generating_energy_sum ,0) as pvl_daily_generating_energy_sum,
        ifnull(dl.today_export_energy ,0) as today_export_energy,

        ifnull(dl.daily_energy_of_load_sum ,0) as daily_energy_of_load_sum,
        ifnull(dl.daily_support_energy_sum_to_backup,0) as daily_support_energy_sum_to_backup,
        ifnull(dl.battery_daily_charge_energy_parallel ,0) as battery_daily_charge_energy_parallel,

        ifnull(dl.today_import_energy ,0) as today_import_energy
        from device_log22 dl where
        dl.device_date_time BETWEEN
        STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} and dl.device_serial_number = #{queryCondition.deviceSerialNumber}
        order by dl.device_date_time desc limit 1
    </select>

    <select id="appParallelReportEstimateV2" resultType="org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO">
        select id,device_date_time as appDeviceDateTime,DATE_FORMAT(device_date_time,'%H:%i') as appTotalDate,
        ifnull(totally_input_dc_watt_sum,0) as appTotallyInputDcWattSum,
        ifnull ( battery_power_sum,0) as appBatteryPowerSum,

        ifnull (phase_l1_watt_of_load_sum,0) as appPhaseL1WattOfLoadSum,
        ifnull(phase_l2_watt_of_load_sum,0) as appPhaseL2WattOfLoadSum,
        ifnull(phase_l3_watt_of_load_sum,0) as appPhaseL3WattOfLoadSum,
        ifnull (phase_l1_watt_sum_of_backup,0) as appPhaseL1WattSumOfBackup,
        ifnull (phase_l2_watt_sum_of_backup,0) as appPhaseL2WattSumOfBackup,
        ifnull (phase_l3_watt_sum_of_backup,0) as appPhaseL3WattSumOfBackup,
        ifnull (phase_l1_watt_of_load_sum,0) +
        ifnull (phase_l2_watt_of_load_sum,0) +
        ifnull (phase_l3_watt_of_load_sum,0) +
        ifnull ( phase_l1_watt_sum_of_backup,0) +
        ifnull ( phase_l2_watt_sum_of_backup,0) +
        ifnull ( phase_l3_watt_sum_of_backup,0) as appLoadAddBackup,

        ifnull (phase_l1_watt_of_grid_sum,0) as appPhaseL1WattOfGridSum,
        ifnull (phase_l2_watt_of_grid_sum,0) as appPhaseL2WattOfGridSum,
        ifnull (phase_l3_watt_of_grid_sum,0) as appPhaseL3WattOfGridSum,

        ifnull (battery_soc,0) as appBatterySoc
        from device_log22 dl  where dl.device_date_time BETWEEN
        STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d %H:%i:%s')
        and dl.plant_id = #{queryCondition.plantId} and dl.device_serial_number = #{queryCondition.deviceSerialNumber}
        order by dl.device_date_time asc
    </select>
</mapper>
