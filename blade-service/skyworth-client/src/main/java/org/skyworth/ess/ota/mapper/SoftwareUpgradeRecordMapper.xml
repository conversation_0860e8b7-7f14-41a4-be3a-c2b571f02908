<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.ota.mapper.SoftwareUpgradeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="softwareUpgradeRecordResultMap" type="org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity">
        <result column="id" property="id"/>
        <result column="device_software_version_id" property="deviceSoftwareVersionId"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="big_type" property="bigType"/>
        <result column="small_type" property="smallType"/>
        <result column="company" property="company"/>
        <result column="upgrade_completion_time" property="upgradeCompletionTime"/>
        <result column="current_version_number" property="currentVersionNumber"/>
        <result column="latest_released_version" property="latestReleasedVersion"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectSoftwareUpgradeRecordPage" resultMap="softwareUpgradeRecordResultMap">
        select *
        from device_software_upgrade_record
        where is_deleted = 0
    </select>


    <select id="exportSoftwareUpgradeRecord" resultType="org.skyworth.ess.ota.excel.SoftwareUpgradeRecordExcel">
        SELECT *
        FROM device_software_upgrade_record ${ew.customSqlSegment}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into device_software_upgrade_record(
        id,
        device_software_version_id,
        serial_number,
        current_version_number,
        latest_released_version,
        status,
        create_user_account,
        update_user_account,
        big_type,
        small_type,
        company
        )values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.deviceSoftwareVersionId},
            #{item.serialNumber},
            #{item.currentVersionNumber},
            #{item.versionNumber},
            0,
            'system',
            'system',
            #{item.bigType},
            #{item.smallType},
            #{item.company}
            )
        </foreach>
    </insert>

</mapper>
