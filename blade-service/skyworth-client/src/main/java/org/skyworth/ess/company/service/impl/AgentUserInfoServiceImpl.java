/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.skyworth.ess.company.mapper.AgentUserInfoMapper;
import org.skyworth.ess.company.service.IAgentUserInfoService;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 代理商用户信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Service
public class AgentUserInfoServiceImpl extends BaseServiceImpl<AgentUserInfoMapper, AgentUserInfoEntity> implements IAgentUserInfoService {


	@Override
	public void deleteByAgentId(Long agentId) {
		baseMapper.deleteByAgentId(agentId);
	}

	@Override
	public void deleteUserByJsonList(List<JSONObject> jsonObjectList) {
		baseMapper.deleteUserByJsonList(jsonObjectList);
	}

	@Override
	public List<AgentUserInfoEntity> queryAgentUserList(List<Long> ids) {
		return baseMapper.queryAgentUserList(ids);
	}

	@Override
	public Boolean updateUserInfo4Delete(List<Long> userIdList) {
		return SqlHelper.retBool(baseMapper.updateUserInfo4Delete(userIdList));
	}
}
