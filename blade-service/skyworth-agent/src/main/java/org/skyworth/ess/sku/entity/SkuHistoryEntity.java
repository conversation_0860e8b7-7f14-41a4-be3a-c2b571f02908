/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.sku.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 物料记录表，每次新增写入一条 实体类
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Data
@TableName("sku_history")
@ApiModel(value = "SkuHistory对象", description = "物料记录表，每次新增写入一条")
@EqualsAndHashCode(callSuper = true)
public class SkuHistoryEntity extends SkyWorthEntity {

	/**
	 * sku编码
	 */
	@ApiModelProperty(value = "sku编码")
	private String skuCode;
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private Long quantity;


}
