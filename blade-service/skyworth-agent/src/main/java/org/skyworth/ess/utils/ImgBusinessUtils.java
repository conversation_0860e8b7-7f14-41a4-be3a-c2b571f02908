package org.skyworth.ess.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.map.HashedMap;
import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.system.entity.AttachmentInfoEntity;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 图片处理 工具类
 *
 * <AUTHOR>
 * @since 2023-12-4
 */
public class ImgBusinessUtils {
	public static Map<String, Object> getImgBizKey(Object object) throws Exception {
		Map<String, Object> imgBusinessMap = new HashedMap<String, Object>();
		Map<String, Long> properties = new HashedMap<String, Long>();
		List<Long> businessIds = new ArrayList<Long>();
		Class<?> superclass = object.getClass().getSuperclass();
		Class<?> clazz = object.getClass();
		Field[] superFields  = superclass.getDeclaredFields();
		Field[] fields = clazz.getDeclaredFields();
		if (superFields.length > 0) {
			getField(object, superFields, businessIds, properties);
		}
		if (fields.length > 0) {
			getField(object, fields, businessIds, properties);
		}
		imgBusinessMap.put("businessIds", businessIds);
		imgBusinessMap.put("properties", properties);
		return imgBusinessMap;
	}

	public static Map<String, List<AttachmentInfoEntity>> getAttachmentInfoDesc(Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap, Map<String, Long> properties) {
		Map<String, List<AttachmentInfoEntity>> attachmentInfoDesc = new HashMap<String, List<AttachmentInfoEntity>>();
		Set<Map.Entry<String, Long>> set = properties.entrySet();
		Iterator<Map.Entry<String, Long>> iter = set.iterator();
		while (iter.hasNext()) {
			Map.Entry<String, Long> entry = iter.next();
			if (attachmentInfoMap.get(entry.getValue()) != null) {
				attachmentInfoDesc.put(entry.getKey(), attachmentInfoMap.get(entry.getValue()));
			}
		}
		return attachmentInfoDesc;
	}


	public static Map<String, String> getAttachmentInfoDescView(Map<Long, String> attachmentInfoMap, Map<String, Long> properties) {
		Map<String, String> attachmentInfoDescView = new HashMap<>();
		Set<Map.Entry<String, Long>> set = properties.entrySet();
		Iterator<Map.Entry<String, Long>> iter = set.iterator();
		while (iter.hasNext()) {
			Map.Entry<String, Long> entry = iter.next();
			if (attachmentInfoMap.get(entry.getValue()) != null) {
				attachmentInfoDescView.put(entry.getKey(), attachmentInfoMap.get(entry.getValue()));
			}
		}
		return attachmentInfoDescView;
	}

	private static void getField(Object object,Field[] fields,List<Long> businessIds,Map<String, Long> properties) throws Exception {
		for (Field field : fields) {
			field.setAccessible(true);
			if (field.getName().indexOf("BizKey") != -1) {
				Object businessId = field.get(object);
				if (businessId != null) {
					businessIds.add(Long.parseLong(businessId.toString()));
					properties.put(field.getName(), Long.parseLong(businessId.toString()));
				}
			}
		}
	}

	private static void getAttachmentInfoField(Object object, Field[] fields, Map<String, Object> properties) {
		try {
			for (Field field : fields) {
				field.setAccessible(true);
				if (field.getName().equals("batchVO")) {
					if (Objects.nonNull(field.get(object))) {
						properties.put("batchVO", (BatchVO<AttachmentInfoEntity>) field.get(object));
					}
				}
				if (field.getName().equals("imgDescOperationList")) {
					if (Objects.nonNull(field.get(object))) {
						List<Map<String, String>> imgDescOperationList = (List<Map<String, String>>) field.get(object);
						properties.put("imgDescOperationList", JSON.parseArray(JSON.toJSONString(imgDescOperationList), AdditionalInfoEntity.class));
					}
				}
			}
		} catch (Exception e) {
			throw new BusinessException("parse properties failure");
		}

	}

	public static Map<String, Object> getBatchVODescOperationList(Object object) {
		Map<String, Object> properties = new HashedMap<String, Object>();
		Class<?> superclass = object.getClass().getSuperclass();
		Field[] superFields  = superclass.getDeclaredFields();

		Class<?> clazz = object.getClass();
		Field[] fields = clazz.getDeclaredFields();

		if (superFields.length > 0) {
			getAttachmentInfoField(object, superFields, properties);
		}
		if (properties.isEmpty()) {
			if (fields.length > 0) {
				getAttachmentInfoField(object, fields, properties);
			}
		}

		return properties;
	}

}
