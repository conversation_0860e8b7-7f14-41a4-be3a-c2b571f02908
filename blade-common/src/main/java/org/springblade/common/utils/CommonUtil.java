/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.utils;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.net.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * 通用工具类
 *
 * <AUTHOR>
 */
public class CommonUtil {


	/**
	 * 项目启动打印启动配置文件
	 *
	 * @Description
	 * @Param springEnv
	 * @Return void
	 * <AUTHOR>
	 * @Date 2023/8/22 14:37
	 **/
	public static void printConfigurationParameters(ConfigurableEnvironment springEnv) {
		MutablePropertySources mutablePropertySources = springEnv.getPropertySources();
		// 获取所有配置
		Map<String, String> props = StreamSupport.stream(mutablePropertySources.spliterator(), false)
			.filter(ps -> ps instanceof EnumerablePropertySource)
			.map(ps -> ((EnumerablePropertySource) ps).getPropertyNames())
			.flatMap(Arrays::stream)
			.distinct()
			.collect(Collectors.toMap(Function.identity(), springEnv::getProperty));

		// key 和 value 之间的最小间隙
		int interval = 20;
		int max = props.keySet().stream()
			.max(Comparator.comparingInt(String::length))
			.orElse("")
			.length();

		// 打印
		props.keySet().stream()
			.sorted()
			.forEach(k -> {
				int i = max - k.length() + interval;
				String join = String.join("", Collections.nCopies(i, " "));
				System.out.println(String.format("%s%s%s", k, join, props.get(k)));
			});
	}

	/**
	 * 获取当前语言
	 *
	 * @return String
	 * <AUTHOR>
	 * @since 2023/10/18 9:56
	 **/
	public static String getCurrentLanguage() {
		Locale currentLocale = LocaleContextHolder.getLocale();
		String language = currentLocale.getLanguage();
		if (StringUtil.isBlank(language)) {
			return CommonConstant.CURRENT_LANGUAGE_EN;
		}
		return language;
	}

	/**
	 * 获取当前请求头中的时区
	 *
	 * @return String
	 * <AUTHOR>
	 * @since 2023/10/18 9:56
	 **/
	public static String getCurrentTimeZoneByRequest() {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes != null) {
			HttpServletRequest request = attributes.getRequest();
			String currentTimeZone = request.getHeader("current-timezone");
			if (StringUtil.isNotBlank(currentTimeZone)) {
				return currentTimeZone;
			}
		}
		return null;
	}

	/**
	 * 转驼峰
	 * @param param
	 * @return
	 */
	public static String toCamelCase(String param) {
		if (param == null || param.trim().isEmpty()) {
			return "";
		}

		// 转换为小写
		String paramLower = param.toLowerCase();

		int len = paramLower.length();
		StringBuilder sb = new StringBuilder(len);
		boolean tmp = false;
		for (int i = 0; i < len; i++) {
			char c = paramLower.charAt(i);
			if (i == 0) {
				// 首个字符转为小写
				sb.append(Character.toLowerCase(c));
			} else {
				if (tmp) {
					sb.append(Character.toUpperCase(c));
					tmp = false;
				} else {
					if (Character.isSpaceChar(c)) { //是否包含空格
						tmp = true;
					} else {
						sb.append(c);
					}
				}
			}
		}
		return sb.toString();
	}

	/**
	 * 转数据库字段
	 * @param camelCaseString
	 * @return
	 */
	public static String toDBField(String camelCaseString) {
		if (camelCaseString == null || camelCaseString.isEmpty()) {
			return camelCaseString;
		}
		StringBuilder output = new StringBuilder();

		for (int i = 0; i < camelCaseString.length(); i++) {
			char c = camelCaseString.charAt(i);

			if (Character.isUpperCase(c)) {
				output.append("_").append(Character.toLowerCase(c));
			} else {
				output.append(c);
			}
		}

		return output.toString();


	}

	/**
	 * 获取本机eth0网卡的IP地址
	 *
	 * @return 返回本机eth0网卡的IP地址，如果获取失败则返回null
	 */
	public static String getEth0IPAddress() {
		try {
			for (NetworkInterface networkInterface : Collections.list(NetworkInterface.getNetworkInterfaces())) {
				if (networkInterface.getName().equals("eth0")) {
					byte[] ip = networkInterface.getInetAddresses().nextElement().getAddress();
					return InetAddress.getByAddress(ip).getHostAddress();
				}
			}
		} catch (SocketException e) {
			e.printStackTrace();
		} catch (UnknownHostException e) {
			e.printStackTrace();
		}
		return "127.0.0.1";
	}

	public static String getIPAddress(String networkInterfaceName) {
		try {
			Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
			while (networkInterfaces.hasMoreElements()) {
				NetworkInterface networkInterface = networkInterfaces.nextElement();
				if (networkInterface.getName().equals(networkInterfaceName)) {
					Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
					while (inetAddresses.hasMoreElements()) {
						InetAddress inetAddress = inetAddresses.nextElement();
						if (inetAddress instanceof Inet4Address) {
							return inetAddress.getHostAddress();
						}
					}
				}
			}
		} catch (SocketException e) {
			throw new RuntimeException(e);
		}
		return "127.0.0.1";
	}
}
