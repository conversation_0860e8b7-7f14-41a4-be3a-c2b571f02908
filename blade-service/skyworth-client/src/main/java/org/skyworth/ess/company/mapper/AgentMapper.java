/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.company.entity.AgentEntity;
import org.skyworth.ess.company.vo.AgentListVO;

/**
 * 代理商公司信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
public interface AgentMapper extends BaseMapper<AgentEntity> {

	/**
	 * 获取代理商列表
	 *
	 * @param page        分页对象
	 * @param companyName 代理商
	 * @param agentArea   代理区域
	 * @param deptId      用户部门id
	 * @return IPage 分页的代理商列表视图对象
	 */
	IPage<AgentListVO> getAgentList(IPage<AgentListVO> page, @Param("companyName") String companyName, @Param("agentArea") Integer agentArea, @Param("deptId") String deptId);

}
