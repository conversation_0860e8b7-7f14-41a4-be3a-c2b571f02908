
package org.skyworth.ess.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.device.entity.DeviceLog22;

import java.util.List;

/**
 * 设备/逆变器表，记录2.2数据 Mapper 接口
 *
 * <AUTHOR>
 */
public interface DeviceLog22Mapper extends BaseMapper<DeviceLog22> {

	List<DeviceLog22> queryDeviceLog22list(@Param("list")List<DeviceLog22> deviceLog22List);
}
