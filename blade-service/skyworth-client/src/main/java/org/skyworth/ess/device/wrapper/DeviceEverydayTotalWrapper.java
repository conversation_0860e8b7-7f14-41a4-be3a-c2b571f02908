/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.wrapper;

import org.skyworth.ess.device.entity.DeviceEverydayTotalEntity;
import org.skyworth.ess.device.vo.DeviceEverydayTotalVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 设备/逆变器每日统计 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public class DeviceEverydayTotalWrapper extends BaseEntityWrapper<DeviceEverydayTotalEntity, DeviceEverydayTotalVO>  {

	public static DeviceEverydayTotalWrapper build() {
		return new DeviceEverydayTotalWrapper();
 	}

	@Override
	public DeviceEverydayTotalVO entityVO(DeviceEverydayTotalEntity deviceEverydayTotal) {
		if (deviceEverydayTotal == null) {
			return null;
		}
		DeviceEverydayTotalVO deviceEverydayTotalVO = Objects.requireNonNull(BeanUtil.copy(deviceEverydayTotal, DeviceEverydayTotalVO.class));

		//User createUser = UserCache.getUser(deviceEverydayTotal.getCreateUser());
		//User updateUser = UserCache.getUser(deviceEverydayTotal.getUpdateUser());
		//deviceEverydayTotalVO.setCreateUserName(createUser.getName());
		//deviceEverydayTotalVO.setUpdateUserName(updateUser.getName());

		return deviceEverydayTotalVO;
	}


}
