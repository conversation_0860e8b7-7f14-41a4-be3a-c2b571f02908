<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.qcsubmission.electricalcomponents.mapper.ElectricalComponentsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="electricalcomponentsResultMap" type="org.skyworth.ess.qcsubmission.electricalcomponents.entity.ElectricalComponentsEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="electrical_verify_cable_wiring_img_biz_key" property="electricalVerifyCableWiringImgBizKey"/>
        <result column="confirm_use_condult_img_biz_key" property="confirmUseCondultImgBizKey"/>
        <result column="all_sealed_joints_img_biz_key" property="allSealedJointsImgBizKey"/>
        <result column="electrical_box_img_biz_key" property="electricalBoxImgBizKey"/>
        <result column="check_generator_battery_img_biz_key" property="checkGeneratorBatteryImgBizKey"/>
        <result column="check_battery_system_img_biz_key" property="checkBatterySystemImgBizKey"/>
        <result column="current_meter_ct_img_biz_key" property="currentMeterCtImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectElectricalComponentsPage" resultMap="electricalcomponentsResultMap">
        select * from qc_electrical_components where is_deleted = 0
    </select>


    <select id="exportElectricalComponents" resultType="org.skyworth.ess.qcsubmission.electricalcomponents.excel.ElectricalComponentsExcel">
        SELECT * FROM qc_electrical_components ${ew.customSqlSegment}
    </select>

</mapper>
