/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.setItem.entity.SetItemMultiLanguageEntity;
import org.skyworth.ess.setItem.vo.SetItemMultiLanguageVO;

import java.util.List;
import java.util.Objects;

/**
 * APP设置项配置名称多语言 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public class SetItemMultiLanguageWrapper extends BaseEntityWrapper<SetItemMultiLanguageEntity, SetItemMultiLanguageVO>  {

	public static SetItemMultiLanguageWrapper build() {
		return new SetItemMultiLanguageWrapper();
 	}

	@Override
	public SetItemMultiLanguageVO entityVO(SetItemMultiLanguageEntity setItemMultiLanguage) {
		SetItemMultiLanguageVO setItemMultiLanguageVO = Objects.requireNonNull(BeanUtil.copy(setItemMultiLanguage, SetItemMultiLanguageVO.class));

		//User createUser = UserCache.getUser(setItemMultiLanguage.getCreateUser());
		//User updateUser = UserCache.getUser(setItemMultiLanguage.getUpdateUser());
		//setItemMultiLanguageVO.setCreateUserName(createUser.getName());
		//setItemMultiLanguageVO.setUpdateUserName(updateUser.getName());

		return setItemMultiLanguageVO;
	}



}
