/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理商公司信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
@TableName("agent_company_info")
@ApiModel(value = "AgentCompanyInfo对象", description = "代理商公司信息")
@EqualsAndHashCode(callSuper = true)
public class AgentCompanyInfoEntity extends SkyWorthEntity {

	/**
	 * 代理商编号
	 */
	@ApiModelProperty(value = "代理商编号")
	private String agentNumber;
	/**
	 * 公司名称
	 */
	@NotBlank(message = "{agent.companyName.notNull}")
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 部门id
	 */
	@ApiModelProperty(value = "部门id")
	private Long deptId;
	/**
	 * 公司地址
	 */
	@ApiModelProperty(value = "公司地址")
	private String companyAddress;
	/**
	 * 注册时间
	 */
	@ApiModelProperty(value = "注册时间")
	private Date registrationTime;
	/**
	 * 联系邮箱
	 */
	@ApiModelProperty(value = "联系邮箱")
	private String contactEmail;
	/**
	 * 代理区域
	 */
	@ApiModelProperty(value = "代理区域")
	private String agentArea;
	/**
	 * 代理费
	 */
	@ApiModelProperty(value = "代理费")
	private BigDecimal agentFee;
	/**
	 * 银行账户名
	 */
	@ApiModelProperty(value = "银行账户名")
	private String bankAccountName;
	/**
	 * 开票银行
	 */
	@ApiModelProperty(value = "开票银行")
	private String invoicingBank;
	/**
	 * 开票信息
	 */
	@ApiModelProperty(value = "开票信息")
	private String invoicingInfo;
	/**
	 * 纳税人识别编号
	 */
	@ApiModelProperty(value = "纳税人识别编号")
	private String taxpayerIdentificationNumber;
	/**
	 * 实控人姓名
	 */
	@ApiModelProperty(value = "实控人姓名")
	private String controllerName;
	/**
	 * 实控人联系方式
	 */
	@ApiModelProperty(value = "实控人联系方式")
	private String controllerContact;
	/**
	 * 法人姓名
	 */
	@ApiModelProperty(value = "法人姓名")
	private String legalPersonName;
	/**
	 * 法人手机
	 */
	@ApiModelProperty(value = "法人手机")
	private String legalPersonPhone;
	/**
	 * 法人身份证
	 */
	@ApiModelProperty(value = "法人身份证")
	private String legalPersonId;
	/**
	 * 代理商属性，业务字典company_attributes
	 */
	@NotBlank(message = "{agent.company.attributes.notNull}")
	@ApiModelProperty(value = "代理商属性，业务字典company_attributes")
	private String companyAttributes;
}
