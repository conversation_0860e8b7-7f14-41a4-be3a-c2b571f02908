package org.skyworth.ess.homepage.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.dailyStatistics.service.impl.DeviceLog22ByDorisServiceImpl;
import org.skyworth.ess.device.service.IDeviceCurrentStatusService;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.homepage.service.IHomePageService;
import org.skyworth.ess.homepage.vo.BatteryHomePageVO;
import org.skyworth.ess.homepage.vo.InverterHomePageVO;
import org.skyworth.ess.plant.service.IPlantService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 首页
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-16 16:17
 **/
@Service
@AllArgsConstructor
public class HomePageServiceImpl implements IHomePageService {
	private final IDeviceCurrentStatusService deviceCurrentStatusService;
	private final DeviceLog22ByDorisServiceImpl deviceLog22ByDorisService;
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	private final IPlantService plantService;

	@Override
	public List<JSONObject> inverterAndLoadSummaryStateCurve() {
		return deviceLog22ByDorisService.inverterAndLoadSummaryStateCurve();
	}

	@Override
	public InverterHomePageVO inverterPerformance() {
		InverterHomePageVO inverterHomePageVO = new InverterHomePageVO();
		JSONObject jsonObject = deviceCurrentStatusService.inverterTodaySummary();
		JSONObject jsonObjectAccumulate = deviceCurrentStatusService.inverterAccumulateSummary();
		BigDecimal ratedTotalPowerOfInverter = deviceExitFactoryInfoService.queryRatedTotalPowerOfInverter();
		if (jsonObject != null) {
			inverterHomePageVO.setDailyLoadEnergy(DataUnitConversionUtil.getChangeEnergyResult(jsonObject.getBigDecimal("dailyLoadEnergy"), BizConstant.NUMBER_ONE));
			inverterHomePageVO.setDailyPowerGeneration(DataUnitConversionUtil.getChangeEnergyResult(jsonObject.getBigDecimal("dailyPowerGeneration"), BizConstant.NUMBER_ONE));
			inverterHomePageVO.setCurrentTotalPower(DataUnitConversionUtil.getChangeEnergyResult(jsonObject.getBigDecimal("currentTotalPower"), BizConstant.NUMBER_TWO));
		}
		if (jsonObjectAccumulate != null) {
			inverterHomePageVO.setAccumulatedLoadEnergy(DataUnitConversionUtil.getChangeEnergyResult(jsonObjectAccumulate.getBigDecimal("accumulatedLoadEnergy"), BizConstant.NUMBER_ONE));
			inverterHomePageVO.setAccumulatedPowerGeneration(DataUnitConversionUtil.extractDecimalPlaces(jsonObjectAccumulate.getBigDecimal("accumulatedPowerGeneration"), null) + BizConstant.UNIT_THOUSAND_WH);
		}
		inverterHomePageVO.setRatedTotalPower(ratedTotalPowerOfInverter.setScale(BizConstant.NUMBER_ZERO, RoundingMode.DOWN) + BizConstant.UNIT_KW);
		return inverterHomePageVO;
	}

	@Override
	public BatteryHomePageVO batteryPerformance() {
		BatteryHomePageVO batteryHomePageVO = new BatteryHomePageVO();
		JSONObject jsonObject = deviceCurrentStatusService.batteryTodaySummary();
		JSONObject jsonObjectAccumulate = deviceCurrentStatusService.batteryAccumulateSummary();
		BigDecimal totalInstalledPower = batteryExitFactoryInfoService.queryTotalInstalledPower();
		if (jsonObject != null) {
			batteryHomePageVO.setChargingEnergyToday(DataUnitConversionUtil.getChangeEnergyResult(jsonObject.getBigDecimal("chargingEnergyToday"), BizConstant.NUMBER_ONE));
			batteryHomePageVO.setDisChargingEnergyToday(DataUnitConversionUtil.getChangeEnergyResult(jsonObject.getBigDecimal("disChargingEnergyToday"), BizConstant.NUMBER_ONE));
		}
		if (jsonObjectAccumulate != null) {
			batteryHomePageVO.setAccumulatedChargingEnergy(DataUnitConversionUtil.getChangeEnergyResult(jsonObjectAccumulate.getBigDecimal("accumulatedChargingEnergy"), BizConstant.NUMBER_ONE));
			batteryHomePageVO.setAccumulatedDisChargingEnergy(DataUnitConversionUtil.getChangeEnergyResult(jsonObjectAccumulate.getBigDecimal("accumulatedDisChargingEnergy"), BizConstant.NUMBER_ONE));
		}
		batteryHomePageVO.setTotalInstalledPower(totalInstalledPower.setScale(BizConstant.NUMBER_ZERO, RoundingMode.DOWN) + BizConstant.UNIT_THOUSAND_WH);
		return batteryHomePageVO;
	}

	@Override
	public JSONObject plantStatusSummary() {
		JSONObject jsonObject = new JSONObject();
		List<JSONObject> list = plantService.queryPlantStatusCount();
		Optional.ofNullable(list).orElse(new ArrayList<>()).forEach(a -> {
			jsonObject.put(a.getString("plantStatus"), a.getLongValue("plantStatusCount"));
		});
		// 设置 0
		if (!jsonObject.containsKey(BizConstant.CHAR_ZERO)) {
			jsonObject.put(BizConstant.CHAR_ZERO, 0);
		}
		if (!jsonObject.containsKey(BizConstant.CHAR_ONE)) {
			jsonObject.put(BizConstant.CHAR_ONE, 0);
		}
		if (!jsonObject.containsKey(BizConstant.CHAR_TWO)) {
			jsonObject.put(BizConstant.CHAR_TWO, 0);
		}
		return jsonObject;
	}
}
