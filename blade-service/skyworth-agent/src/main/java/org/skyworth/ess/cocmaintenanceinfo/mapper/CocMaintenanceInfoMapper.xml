<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.cocmaintenanceinfo.mapper.CocMaintenanceInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cocMaintenanceInfoResultMap" type="org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="temporary_coc_img_biz_key" property="temporaryCocImgBizKey"/>
        <result column="temporary_coc_start_date" property="temporaryCocStartDate"/>
        <result column="fac_landlord_sign" property="facLandlordSign"/>
        <result column="fac_landlord_doc_img_biz_key" property="facLandlordDocImgBizKey"/>
        <result column="fac_plant_id" property="facPlantId"/>
        <result column="fac_plant_name" property="facPlantName"/>
        <result column="fac_technician_sign" property="facTechnicianSign"/>
        <result column="fac_technician_img_biz_key" property="facTechnicianImgBizKey"/>
        <result column="fac_declaration" property="facDeclaration"/>
        <result column="balance_payment_confirm_doc_biz_key" property="balancePaymentConfirmDocBizKey"/>
        <result column="final_coc_img_biz_key" property="finalCocImgBizKey"/>
        <result column="final_coc_type" property="finalCocType"/>
        <result column="final_coc_start_date" property="finalCocStartDate"/>
        <result column="final_coc_end_date" property="finalCocEndDate"/>
        <result column="free_maintenance_service_date" property="freeMaintenanceServiceDate"/>
        <result column="warranty_date" property="warrantyDate"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
        <result column="fac_declaration" property="facDeclaration"/>
        <result column="delivery_number" property="deliveryNumber"/>
        <result column="client_confirmation_biz_key" property="clientConfirmationBizKey"/>
        <result column="delivery" property="delivery"/>
        <result column="customers_acceptance" property="customersAcceptance"/>
    </resultMap>


    <resultMap id="basicOrderInfo" type="org.skyworth.ess.cocmaintenanceinfo.dto.BasicOrderDTO">
         <result column="order_number" property="orderId"/>
         <result column="site_country_code" property="siteCountryCode"/>
         <result column="site_province_code" property="siteProvinceCode"/>
         <result column="site_city_code" property="siteCityCode"/>
         <result column="site_address" property="siteAddress"/>
         <result column="constructionDate" property="constructionDate"/>
         <result column="warranty_date" property="warrantyDate"/>
         <result column="free_maintenance_service_date" property="freeMaintenanceServiceDate"/>
    </resultMap>
    <update id="updateCocMaintenanceByOrderId">
        UPDATE coc_maintenance_info SET
        <foreach collection="data" item="val" index="key" separator=",">
            ${key} = #{val}
        </foreach>
        WHERE order_id = #{orderId}
    </update>


    <select id="selectCocMaintenanceInfoPage" resultMap="cocMaintenanceInfoResultMap">
        select * from coc_maintenance_info where is_deleted = 0
    </select>


    <select id="exportCocMaintenanceInfo" resultType="org.skyworth.ess.cocmaintenanceinfo.excel.CocMaintenanceInfoExcel">
        SELECT * FROM coc_maintenance_info ${ew.customSqlSegment}
    </select>
    <select id="selectBasicOrderInfo" resultMap="basicOrderInfo">
        select bo.order_number, bo.site_country_code, bo.site_province_code, bo.site_city_code , bo.site_address ,
        CONVERT(iri.construction_date, CHAR) as constructionDate, cmi.warranty_date ,cmi.free_maintenance_service_date from business_order  bo
        inner join install_related_info  iri
        on bo.id= iri.order_id
        inner join coc_maintenance_info cmi on cmi.order_id =bo.id
        where bo.id =#{orderId}
    </select>

</mapper>
