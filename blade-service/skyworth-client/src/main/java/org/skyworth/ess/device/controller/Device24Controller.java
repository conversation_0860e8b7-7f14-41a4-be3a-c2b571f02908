/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.excel.Device24Excel;
import org.skyworth.ess.device.service.IDevice24Service;
import org.skyworth.ess.device.vo.Device24VO;
import org.skyworth.ess.device.wrapper.Device24Wrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Device24 控制器
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("skyworth-Device24/device24")
@Api(value = "Device24", tags = "Device24接口")
public class Device24Controller extends BladeController {

	private final IDevice24Service Device24Service;

	/**
	 * Device24 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入Device24")
	public R<Device24VO> detail(Device24Entity Device24) {
		Device24Entity detail = Device24Service.getOne(Condition.getQueryWrapper(Device24));
		return R.data(Device24Wrapper.build().entityVO(detail));
	}
	/**
	 * Device24 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入Device24")
	public R<IPage<Device24VO>> list(@ApiIgnore @RequestParam Map<String, Object> Device24, Query query) {
		IPage<Device24Entity> pages = Device24Service.page(Condition.getPage(query), Condition.getQueryWrapper(Device24, Device24Entity.class));
		return R.data(Device24Wrapper.build().pageVO(pages));
	}

	/**
	 * Device24 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入Device24")
	public R<IPage<Device24VO>> page(Device24VO Device24, Query query) {
		IPage<Device24VO> pages = Device24Service.selectDevice24Page(Condition.getPage(query), Device24);
		return R.data(pages);
	}

	/**
	 * Device24 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入Device24")
	public R save(@Valid @RequestBody Device24Entity Device24) {
		return R.status(Device24Service.save(Device24));
	}

	/**
	 * Device24 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入Device24")
	public R update(@Valid @RequestBody Device24Entity Device24) {
		return R.status(Device24Service.updateById(Device24));
	}

	/**
	 * Device24 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入Device24")
	public R submit(@Valid @RequestBody Device24Entity Device24) {
		return R.status(Device24Service.saveOrUpdate(Device24));
	}

	/**
	 * Device24 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(Device24Service.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-Device24")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入Device24")
	public void exportDevice24(@ApiIgnore @RequestParam Map<String, Object> Device24, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<Device24Entity> queryWrapper = Condition.getQueryWrapper(Device24, Device24Entity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Device24::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(Device24Entity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<Device24Excel> list = Device24Service.exportDevice24(queryWrapper);
		ExcelUtil.export(response, "Device24数据" + DateUtil.time(), "Device24数据表", list, Device24Excel.class);
	}

}
