/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.resource.feign;

import cn.hutool.core.map.MapUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.FileUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.sms.model.SmsCode;
import org.springblade.core.sms.model.SmsData;
import org.springblade.core.sms.model.SmsResponse;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.resource.builder.oss.OssBuilder;
import org.springblade.resource.builder.sms.SmsBuilder;
import org.springblade.resource.utils.HuaweiObsSign;
import org.springblade.resource.utils.HuaweiUploadUtils;
import org.springblade.resource.vo.AttachVO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.springblade.resource.utils.SmsUtil.*;

/**
 * 短信远程调用服务
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@Slf4j
public class SmsClient implements ISmsClient {

	private final SmsBuilder smsBuilder;
	private final OssBuilder ossBuilder;
	private final BladeRedis bladeRedis;
	private final HuaweiObsSign huaweiObsSign;
	private static final String IMAGE_CONTENT_TYPE = "image/jpg;image/jpeg;image/png;";
	/**
	 * 图片后缀
	 */
	private static final String IMAGE_SUFFIX = ".jpg;.jpeg;.png;.heif;.heic;";
	private static final String OTA_STORAGE_CONTAINER = "ota-storage";
	@Override
	@PostMapping(SEND_MESSAGE)
	public R<SmsResponse> sendMessage(String code, String params, String phones) {
		SmsData smsData = new SmsData(JsonUtil.readMap(params, String.class, String.class));
		SmsResponse response = smsBuilder.template(code).sendMessage(smsData, Func.toStrList(phones));
		return R.data(response);
	}

	@Override
	@PostMapping(SEND_VALIDATE)
	public R sendValidate(String code, String phone) {
		Map<String, String> params = getValidateParams();
		SmsCode smsCode = smsBuilder.template(code).sendValidate(new SmsData(params).setKey(PARAM_KEY), phone);
		return smsCode.isSuccess() ? R.data(smsCode, SEND_SUCCESS) : R.fail(SEND_FAIL);
	}

	@Override
	@PostMapping(VALIDATE_MESSAGE)
	public R validateMessage(String code, String id, String value, String phone) {
		log.info("validateMessage SMS_CODE : {} ; requestId : {} ; verificationCode : {}  ; phone : {} ", code, id, value, phone);
		SmsCode smsCode = new SmsCode().setId(id).setValue(value).setPhone(phone);
		boolean validate = smsBuilder.template(code).validateMessage(smsCode);
		return validate ? R.success(VALIDATE_SUCCESS) : R.fail(VALIDATE_FAIL);
	}

	/**
	 * 文件上传华为云obs
	 *
	 * @param bucketName    桶
	 * @param multipartFile 文件
	 * @return R<List < AttachVO>>
	 * <AUTHOR>
	 * @since 2024/3/20 10:49
	 **/
	@Override
	@PostMapping(value = PUT_FILES, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public R<List<AttachVO>> putFiles(@RequestParam("bucketName") String bucketName, @RequestPart("file") MultipartFile[] multipartFile) throws Exception {
		List<AttachVO> attachVOList = new ArrayList<>();
		for (MultipartFile file : multipartFile) {
			if (!file.isEmpty()) {
				try {
					String fileAliasName = file.getOriginalFilename() + HuaweiUploadUtils.getFileExtension(Objects.requireNonNull(file.getOriginalFilename()));
					BladeFile bladeFile = ossBuilder.template().putFile(bucketName, fileAliasName, file.getInputStream());
					AttachVO attachVO = HuaweiUploadUtils.buildAttachVO(file.getOriginalFilename(), file.getSize(), bladeFile);
					// 上传缩略图
					if (IMAGE_CONTENT_TYPE.contains(Objects.requireNonNull(file.getContentType()).toLowerCase()) || IMAGE_SUFFIX.contains(fileAliasName)) {
						HuaweiUploadUtils.uploadThumbnailImages(bucketName, file, attachVO, ossBuilder);
					}
					// 如果是OTA存储桶，则计算校验和
					calculateChecksumIfOtaBucket(file, bucketName, attachVO);
					attachVOList.add(attachVO);
				} catch (Exception e) {
					log.error("File upload exception,error:", e);
					throw new BusinessException("resource.file.upload.exception",e.getMessage());
				}
			}
		}
		// 获取sasToken
		generateSasToken(attachVOList, bucketName);
		return R.data(attachVOList);
	}
	/**
	 * 如果是OTA存储桶，则计算文件的校验和
	 * 本函数的主要目的是对上传到特定OTA存储桶的文件进行处理，计算其校验和以便后续验证文件的完整性
	 * 只有当存储桶名称包含OTA_STORAGE_CONTAINER（即OTA存储桶）时，才会计算校验和
	 *
	 * @param file       上传的文件对象，包含文件的相关信息和内容
	 * @param bucketName 存储桶的名称，用于判断是否为OTA存储桶
	 * @param attachVO   用于存储文件校验和的对象，假设计算校验和的操作是必要的，则在此设置
	 * @throws Exception 如果文件处理或校验和计算过程中发生错误，则可能抛出异常
	 */
	private void calculateChecksumIfOtaBucket(MultipartFile file, String bucketName, AttachVO attachVO) throws Exception {
		// 检查存储桶名称是否包含OTA_STORAGE_CONTAINER，以确定是否为OTA存储桶
		if (bucketName.contains(OTA_STORAGE_CONTAINER)) {
			// 尝试读取文件的输入流，以便计算校验和
			try (InputStream inputStream = file.getInputStream()) {
				// 计算文件的校验和，并将结果存储在AttachVO对象中
				attachVO.setCheckSum(FileUtil.calculateChecksum(inputStream));
			}
		}
	}
	/**
	 * 生成sasToken
	 *
	 * @param attachVOList 入参
	 * <AUTHOR>
	 * @since 2024/3/21 17:46
	 **/
	private void generateSasToken(List<AttachVO> attachVOList, String bucketName) {
		// 获取图片和压缩图片的sasToken
		if (!CollectionUtils.isNullOrEmpty(attachVOList)) {
			// 远程接口获取sasToken
			Map<String, String> map = new HashMap<>(attachVOList.size() * 2);
			attachVOList.forEach(a -> {
				map.put(a.getExtension(), bucketName);
				if (StringUtils.isNotBlank(a.getThumbnailExtension())) {
					map.put(a.getThumbnailExtension(), bucketName);
				}
			});
			Map<String, String> sasTokenMap = this.getSasToken(map).getData();
			// 设置附件和压缩图片的sasToken
			attachVOList.forEach(a -> {
				String key = a.getExtension();
				String thumbnailKey = a.getThumbnailExtension();
				if (sasTokenMap.containsKey(key)) {
					a.setSasToken(sasTokenMap.get(key));
				}
				if (StringUtils.isNotBlank(thumbnailKey) && sasTokenMap.containsKey(thumbnailKey)) {
					a.setThumbnailSasToken(sasTokenMap.get(thumbnailKey));
				}
			});
		}
	}

	@Override
	@PostMapping(GET_SAS_TOKEN)
	public R<Map<String, String>> getSasToken(@RequestBody Map<String, String> map) {
		if (MapUtil.isEmpty(map)) {
			return R.data(null);
		}
		Map<String, String> resultMap = new HashMap<>(map.size());
		map.forEach((key, value) -> {
			try {
				String redisKey = value + key;
				// 从redis获取sasToken数据，redis有效期半小时，避免频繁访问
				String sasToken = bladeRedis.get(redisKey);
				if (StringUtils.isNotBlank(sasToken)) {
					resultMap.put(key, sasToken);
				} else {
					// 获取token，有效期1h
					sasToken = huaweiObsSign.getSasToken(new HashMap<>(0), value, key, new HashMap<>(0), (System.currentTimeMillis() + 3600000L) / 1000);
					resultMap.put(key, sasToken);
				}
			} catch (Exception e) {
				log.error("SmsClient->getSasToken error:" + e);
				throw new BusinessException("resource.sastoken.get.exception");
			}
		});
		return R.data(resultMap);
	}

	@Override
	@PostMapping(DELETE_FILES)
	public R<Boolean> deleteAzureAttachment(@RequestBody Map<String, String> map) {
		if (MapUtil.isEmpty(map)) {
			return R.status(true);
		}
		RequestAttributes requestAttribute = RequestContextHolder.getRequestAttributes();
		// 异步方法删除华为云文件
		CompletableFuture.supplyAsync(() -> {
			RequestContextHolder.setRequestAttributes(requestAttribute);
			map.forEach((key, value) -> {
				ossBuilder.template().removeFile(value, key);
			});
			return true;
		});
		return R.status(true);
	}

}
