package org.skyworth.ess.app.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class AppDeviceInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 站点名称
	 */
	private String plantName;
	/**
	 * 逆变器SN号
	 */
	private String deviceSerialNumber;
	/**
	 * 设备型号
	 */
	private String deviceModel;
	/**
	 * 额定电压
	 */
	private String ratedVoltage;
	/**
	 * 额定输出功率
	 */
	private String ratedPower;
	/**
	 * 质保有效期
	 */
	private String newQualityQuaranteeYear;
	/**
	 * 状态
	 */
	private String onLineStatus;
	/**
	 * 安装日期
	 */
	private Date installDate;

	/**
	 * 逆变器开关状态 0开 1关
	 */
	private String inverterControl;

	private BigDecimal l1nPhaseVoltageOfGrid;
	private BigDecimal l1CurrentOfGrid;
	private BigDecimal frequencyOfGrid;
	private BigDecimal phaserVoltageOfEps;
	private BigDecimal phaserCurrentOfEps;
	private BigDecimal frequencyOfEps;
	private BigDecimal pv1Voltage;
	private BigDecimal pv1Current;
	private BigDecimal pv2Voltage;
	private BigDecimal mppt2Power;
	private BigDecimal innerTemperature;
	/**
	 * 质保年限
	 */
	private String newQualityGuaranteeYear;
	/**
	 * 质保日期
	 */
	private String newQualityGuaranteeYearDate;

	//质保年限
	private String qualityGuaranteeYear;

	//质保开始日期
	private String warrantyStartDate;
	//质保结束日期
	private String warrantyDeadline;

	/**是否存在用户类告警(0/1:不存在/存在)*/
	private Integer existUserTypeAlarm;

	/**是否存在代理类告警(0/1:不存在/存在)*/
	private Integer existAgentTypeAlarm;

	private String inverterKind;
}
