/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 实体类
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Data
@TableName("blade_user_mapping")
@ApiModel(value = "UserMapping对象", description = "用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用")
@EqualsAndHashCode(callSuper = true)
public class UserMappingEntity extends TenantEntity {

	/**
	 * 源头userId
	 */
	@ApiModelProperty(value = "源头userId")
	private Long sourceUserId;
	/**
	 * 映射userId
	 */
	@ApiModelProperty(value = "映射userId")
	private Long mappingUserId;

}
