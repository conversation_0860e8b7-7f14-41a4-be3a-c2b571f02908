<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.company.mapper.AgentMapper">

    <select id="getAgentList" resultType="org.skyworth.ess.company.vo.AgentListVO">
        SELECT
        c.id,
        c.registration_time,
        c.agent_area,
        c.company_name,
        c.controller_name,
        c.controller_contact,
        c.dept_id
        FROM
        agent_company_info c
        WHERE
        c.is_deleted = 0
        <if test="companyName!=null and companyName!=''">
            and c.company_name like concat(#{companyName},'%')
        </if>
        <if test="agentArea!=null and agentArea!=''">
            and find_in_set(concat('"',#{agentArea},'"'),replace(replace(c.agent_area,'[',''),']',''))
        </if>
        <if test="deptId!=null and deptId!=''">
            and find_in_set(c.dept_id,#{deptId})
        </if>
        ORDER BY c.id desc
    </select>

</mapper>
