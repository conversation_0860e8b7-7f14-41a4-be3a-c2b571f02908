package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.service.IAddressMapDefinitionService;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.constant.SetupItemEnum;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.entity.SetItemEntity;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 模式设置基类
 * @create-time 2023/12/1 09:40:20
 */
@Slf4j
@Component
public abstract class AppSetupTemplate {

	public IDevice21Service device21Service = SpringUtil.getBean(IDevice21Service.class);
	public IAddressMapDefinitionService addressMapDefinitionService = SpringUtil.getBean(IAddressMapDefinitionService.class);
	private TimeZoneDeviceService timeZoneDeviceService = SpringUtil.getBean(TimeZoneDeviceService.class);
	private IDeviceExitFactoryInfoService deviceExitFactoryInfoService = SpringUtil.getBean(IDeviceExitFactoryInfoService.class);
	private IBatteryExitFactoryInfoService batteryExitFactoryInfoService = SpringUtil.getBean(IBatteryExitFactoryInfoService.class);
	private ISetItemConfigService setItemConfigService = SpringUtil.getBean(ISetItemConfigService.class);

	private IPlantService plantService = SpringUtil.getBean(IPlantService.class);

	private IDevice23Service device23Service = SpringUtil.getBean(IDevice23Service.class);
	public static final String DATETIME = "dateTime";
	public static final String REGULATION_CODE = "regulationCode";
	public static final String ITEMS = "items";
	public static final String DEFINITION = "definition";
	public static final String DATA = "data";
	public static final String RANGE = "range";
	public static final String DEFAULT_MODBUS_VERSION = "0.0.1";

	/**
	 * 获取设置项
	 *
	 * @param appSetRequestVO
	 * @return
	 */
	public JSONObject getSetupItem(AppSetRequestVO appSetRequestVO) {
		JSONObject SetupItemDataObject = new JSONObject();
		try {
			String deviceSerialNumber = appSetRequestVO.getDeviceSerialNumber();
			// 获取sn对应的型号和modbus协议版本
			this.getModelAndProtocolBySn(appSetRequestVO, deviceSerialNumber);
			// 从数据库获取设置项数据
			Map<String, Object> setupDataByDb = this.getSetupDataByDb(appSetRequestVO.getPlantId(), deviceSerialNumber);
			// 获取动态配置项
			SetItemEntity setupItemConfig = this.getSetupConfig(appSetRequestVO);
			// 子类根据业务去完善设置项
			completeConfigItemBySubTemplate(setupDataByDb, setupItemConfig, SetupItemDataObject, appSetRequestVO);
			return SetupItemDataObject;
		} catch (Exception e) {
			log.error("getSetupItem occur error  -> " + e.getMessage(), e);
			SetupItemDataObject.put("msg", "system getSetupItem error");
			return SetupItemDataObject;
		}
	}

	/**
	 * 从device21、23、24表查询 设备上报的设置值
	 *
	 * @param plantId
	 * @param deviceSerialNumber
	 * @return
	 */
	public abstract Map<String, Object> getSetupDataByDb(Long plantId, String deviceSerialNumber);

	/**
	 * 从动态配置中获取设置项
	 *
	 * @param
	 * @return
	 */
	public SetItemEntity getSetupConfig(AppSetRequestVO appSetRequestVO) {
		SetItemEntity setItemEntity = new SetItemEntity();
		//查询设置项
		setItemEntity.setSetItemConfigEntityList(setItemConfigService.getSetupConfig(appSetRequestVO));
		return setItemEntity;
	}

	/**
	 * 遍历设置项与设置值
	 *
	 * @param setupDataByDb
	 * @param
	 */
	public abstract void completeConfigItemBySubTemplate(Map<String, Object> setupDataByDb, SetItemEntity setupItemConfig,
														 JSONObject dataObject, AppSetRequestVO appSetRequestVO);


	// 因为时区不是一个协议中的设置项，所以通过该方式构建一个时区的设置项返回前端
	void buildTimeZoneInfo(JSONObject dataObject, AppSetRequestVO appSetRequestVO){
		String timeZone =  CommonConstant.COMMON_DEFAULT_TIME_ZONE;
		Map<String, String> timeZoneDeviceMap = timeZoneDeviceService.getMapFromCacheByPlantIdList(Collections.singletonList(appSetRequestVO.getPlantId()));
		if (ValidationUtil.isNotEmpty(timeZoneDeviceMap)) {
			timeZone = timeZoneDeviceMap.getOrDefault(appSetRequestVO.getPlantId() + "", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		}
		JSONObject singleItemConfig = new JSONObject();
		singleItemConfig.put(DATA, timeZone);
		singleItemConfig.put(DEFINITION, "timeZone");
		singleItemConfig.put(ITEMS, new JSONArray());
		singleItemConfig.put(RANGE, new JSONObject());
		singleItemConfig.put("itemName", "timeZone");
		singleItemConfig.put("setItemType", "2");
		singleItemConfig.put("setItemBigType", "timeZone");
		singleItemConfig.put("setItemSort", "100");
		singleItemConfig.put("readOnly", "readOnly");
		dataObject.put("timeZone", singleItemConfig);
	}
	/**
	 * 构建返回的JSON信息
	 *
	 * @param setupItem
	 * @param setupItemConfig
	 * @param dataObject
	 * @return
	 */
	void buildSetupItemInfo(Map.Entry<String, Object> setupItem, SetItemEntity setupItemConfig, JSONObject dataObject) {
		List<SetItemConfigEntity> setItemConfigEntity = setupItemConfig.getSetItemConfigEntityList();
		for (SetItemConfigEntity itemConfigEntity : setItemConfigEntity) {
			if (!setupItem.getKey().equals(itemConfigEntity.getSetItemKey())) {
				continue;
			}
			JSONObject singleItemConfig = new JSONObject();
			Object value = setupItem.getValue();
			singleItemConfig.put(DATA, value);
			singleItemConfig.put(DEFINITION, setupItem.getKey());
			singleItemConfig.put(ITEMS, new JSONArray());
			singleItemConfig.put(RANGE, new JSONObject());
			singleItemConfig.put("itemName", itemConfigEntity.getOtherLanguage() == null ? itemConfigEntity.getSetItemName() : itemConfigEntity.getOtherLanguage());
			singleItemConfig.put("setItemType", itemConfigEntity.getSetItemType());
			singleItemConfig.put("setItemBigType", itemConfigEntity.getSetItemBigType());
			singleItemConfig.put("setItemSort", itemConfigEntity.getSetItemSort());
			singleItemConfig.put("readOnly", itemConfigEntity.getAttribute1());

			// BigDecimal 字段去掉小数点
			if (ObjectUtil.isNotNull(value) && value instanceof BigDecimal) {
				String plainString = ((BigDecimal) value).stripTrailingZeros().toPlainString();
				singleItemConfig.put(DATA, plainString);
			}
			// 数据库无值则设置默认值
			if (ObjectUtil.isNull(setupItem.getValue())) {
				singleItemConfig.put(DATA, "0");
				if (ObjectUtil.isNotNull(itemConfigEntity.getSetItemDefault())) {
					singleItemConfig.put("data", itemConfigEntity.getSetItemDefault());
				}
			}else{
				if ((0 == itemConfigEntity.getSetItemType()) || 1 == itemConfigEntity.getSetItemType()){
					if (value == null) {
						// 处理 null 值的情况
						singleItemConfig.put(DATA, "");
					} else if (value instanceof Number) {
						// 处理各种数值类型
						Number numberValue = (Number) value;
						if (numberValue instanceof BigDecimal) {
							BigDecimal decimalValue = (BigDecimal) value;
							// 根据实际需求决定是否需要设置精度
							decimalValue = decimalValue.setScale(0, RoundingMode.DOWN); // 可以注释掉或保留
							singleItemConfig.put(DATA, decimalValue.toString());
						} else {
							// 其他数值类型直接转换为字符串
							singleItemConfig.put(DATA, numberValue.toString());
						}
					} else {
						// 非数值类型直接转换为字符串
						singleItemConfig.put(DATA, value.toString());
					}
				}
			}

			// 输入框
			if (itemConfigEntity.getSetItemType().equals(2)) {
				HashMap<String, Object> rangeMap = new HashMap<>();
				if (ObjectUtil.isNotNull(itemConfigEntity.getSetItemRangeMin())) {
					rangeMap.put("min", itemConfigEntity.getSetItemRangeMin());
				}
				if (ObjectUtil.isNotNull(itemConfigEntity.getSetItemRangeMax())) {
					rangeMap.put("max", itemConfigEntity.getSetItemRangeMax());
				}
				singleItemConfig.put("range", rangeMap);
				List<Map<String, Object>> rangeListMap = new ArrayList<>();
				if (StringUtils.isNotEmpty(itemConfigEntity.getSetItemRangeList())) {
					String[] rangeList = itemConfigEntity.getSetItemRangeList().split(";");
					for (String range : rangeList) {
						String[] rangeArr = range.split(",");
						Map<String, Object> mixMaxMap = new HashMap<>();
						mixMaxMap.put("min", rangeArr[0]);
						mixMaxMap.put("max", rangeArr[1]);
						rangeListMap.add(mixMaxMap);
					}
				}
				singleItemConfig.put("rangeList", rangeListMap);
			} else if ((0 == itemConfigEntity.getSetItemType()) || 1 == itemConfigEntity.getSetItemType()) {
				// 按钮、选择框
				List<SetItemConfigEntity> childrenSetItemConfigList = itemConfigEntity.getChildren();
				List<SetItemConfigEntity> sortedList = childrenSetItemConfigList.stream()
					.filter(entity -> entity.getSetItemSort() != null)
					.sorted(Comparator.comparing(SetItemConfigEntity::getSetItemSort))
					.collect(Collectors.toList());
				List<Map<String, Object>> jsonArray = IntStream.range(0, sortedList.size())
					.mapToObj(i -> {
						SetItemConfigEntity setItemConfig = sortedList.get(i);
						Map<String, Object> itemMap = new HashMap<>();
						itemMap.put("idx", i + 1);
						itemMap.put("item", setItemConfig.getSetItemKey());
						itemMap.put("itemName", setItemConfig.getOtherLanguage() == null ? setItemConfig.getSetItemName() : setItemConfig.getOtherLanguage());
						itemMap.put("value", setItemConfig.getSetItemValue());
						return itemMap;
					})
					.collect(Collectors.toList());
				singleItemConfig.put(ITEMS, jsonArray);
			}

			dataObject.put(setupItem.getKey(), singleItemConfig);

		}
	}


	// =======================================================================================================


	@Transactional
	public R issueSetupCommon(AppAdvancedSetup deviceAdvancedSetup) {
		try {
			R<String> r = null;
			// 如果是通过自动出发的下发设置项，不需要校验设备是否在线，此时设备一定是在线的
			if (ValidationUtil.isEmpty(deviceAdvancedSetup.getIssueSource())){
				r =determineDeviceStatus(deviceAdvancedSetup.getPlantId(),deviceAdvancedSetup.getDeviceSerialNumber());
				if (r != null) {
					return r;
				}
			}
			// 记录表与字段与更新值的关系
			Table<String, String, Object> table = HashBasedTable.create();
			// 处理UTC时区+号丢失问题
			String advancedSetupTimeZone = deviceAdvancedSetup.getTimeZone();
			String normalizeTimeZone = DateUtil.normalizeTimeZone(advancedSetupTimeZone);
			if (ValidationUtil.isNotEmpty(normalizeTimeZone)){
				deviceAdvancedSetup.setTimeZone(normalizeTimeZone);
			}
			// 获取地址映射
			Map<String, AddressMapDefinitionEntity> allAddressMap4Definition = addressMapDefinitionService.getAllAddressMap4Definition();
			List<AppAdvancedSetup.SetupItem> items = deviceAdvancedSetup.getSetupItems();
			List<AppAdvancedSetup.SetupItem> dateTimeItems = new ArrayList<>();
			List<AppAdvancedSetup.SetupItem> newItems = new ArrayList<>();
			AtomicBoolean atomicBoolean4DateTime = new AtomicBoolean(false);
			AtomicBoolean atomicBoolean4NoMapping = new AtomicBoolean(false);
			AtomicReference<String> oldRegularCode = new AtomicReference<>("");
			// 遍历映射 做业务处理
			items.forEach(item -> {
				String definition = item.getDefinition();
				if (ObjectUtil.isNotNull(item.getData())) {
					// 部分数据需要通过特定算法进行转换后再下发到设备端
					// 下发的时间 要做特殊处理
					if (DATETIME.equals(definition)) {
						Map<String,String> timeZoneDeviceMap =
							timeZoneDeviceService.getMapFromCacheByPlantIdList(Collections.singletonList(deviceAdvancedSetup.getPlantId()));
						String timeZone = timeZoneDeviceMap.getOrDefault(deviceAdvancedSetup.getPlantId()+"", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
						processDateTime(item, newItems, deviceAdvancedSetup.getTimeZone() ,timeZone);
						dateTimeItems.add(item);
						atomicBoolean4DateTime.set(true);

						return;
					} else if (REGULATION_CODE.equals(definition)) {
						// regulationCode 由app端下发的数据，需要转换为10进制 数字
						String data = (String) item.getData();
						int i = BinaryToHexUtils.hexToDecimalForSymbol(data);
						item.setData(i);
						oldRegularCode.set(data);
					} else {
						// 其他设置项值需要把String转int
						Object data = item.getData();
						// 24中的无功控制包含小数，转成整数不能直接强转 Integer.parseInt，由后面的转换
						Map<String, String> device24ColumnMap = getDevice24ColumnMap();
						if(device24ColumnMap.containsKey(definition)) {
							item.setData(data);
						} else {
							Object o = convertToInt(data);
							item.setData(o);
						}
					}
				}

				// 根据设置项寻找地址码（十进制）、寄存器长度
				AddressMapDefinitionEntity addressMapDefinitionEntity = allAddressMap4Definition.get(definition);
				if (ObjectUtil.isNotNull(addressMapDefinitionEntity)) {
					log.info("AddressMapDefinitionEntity==========>:{}", addressMapDefinitionEntity.toString());
					Integer address = addressMapDefinitionEntity.getDecimalAddress();
					item.setAddress(address);
					item.setLen(addressMapDefinitionEntity.getLength());
					atomicBoolean4NoMapping.set(false);
					String unit = addressMapDefinitionEntity.getUnit();
					if ("ratedOutputVoltage".equals(definition) || "backupMinimumOutputVoltage".equals(definition) || "backupMaximumOutputVoltage".equals(definition)) {
						if (ObjectUtil.isNotNull(item.getData()) && "0.1".equals(unit)) {
							// 下发的时候要*10
							BigDecimal inputDataBigDecimal = getDataBigDecimal(item);
							BigDecimal result = inputDataBigDecimal.multiply(new BigDecimal("10")).setScale(0, RoundingMode.HALF_UP);
							item.setData(result.intValue());
						}
					} else {
						setChangeDevice24Value(item, definition, unit, addressMapDefinitionEntity);
					}
				} else {
					log.error("The setting item could not be mapped to a register address -> {}", definition);
					atomicBoolean4NoMapping.set(true);
					return;
				}
				SetupItemEnum setupItemEnum = SetupItemEnum.getInstanceByDefinition(definition);

				// 驼峰转下划线
				String dbField = CommonUtil.toDBField(definition);
				table.put(setupItemEnum.getDataTable(), dbField, item.getData());

				newItems.add(item);
			});
			if (atomicBoolean4NoMapping.get()) {
				String currentLanguage = CommonUtil.getCurrentLanguage();
				r.setCode(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_COMMON_ERROR_900000.autoGetMessage(currentLanguage));
				return r;
			}

			items.addAll(newItems);
			deviceAdvancedSetup.setSetupItems(newItems);
			log.info("deviceAdvancedSetup==========> : {}", deviceAdvancedSetup.getSetupItems());
			R<String> result = issueSetupToToolKit(deviceAdvancedSetup);

			// 因为要记录日志，所以要把数据处理一下
			if (atomicBoolean4DateTime.get()) {
				newItems.addAll(dateTimeItems);
				deviceAdvancedSetup.setSetupItems(newItems);
				atomicBoolean4DateTime.set(false);
			}

			List<AppAdvancedSetup.SetupItem> setupItems = deviceAdvancedSetup.getSetupItems();
			setupItems.forEach(item -> {
				String definition = item.getDefinition();
				// 特殊处理
				// 转回16进制，记录数据库
				if (REGULATION_CODE.equals(definition)) {
					// regulationCode 由app端下发的数据，需要转换为10进制 数字
					item.setData(oldRegularCode.get());
					table.put("device_24", "regulation_code", oldRegularCode.get());
				}
				// 特殊处理
				if (ObjectUtil.isNotNull(item.getData())) {
					// 此处获取的值，已经是前端传过来的值， 经过了上面的计算后 乘以 unit的 值
					if ("ratedOutputVoltage".equals(definition)) {
						BigDecimal inputDataBigDecimal = getDataBigDecimal(item);
						table.put("device_23", "rated_output_voltage", inputDataBigDecimal.divide( new BigDecimal("10"), 0, RoundingMode.HALF_UP));
					}
					if ("backupMinimumOutputVoltage".equals(definition)) {
						BigDecimal inputDataBigDecimal = getDataBigDecimal(item);
						table.put("device_23", "backup_minimum_output_voltage", inputDataBigDecimal.divide( new BigDecimal("10"), 0, RoundingMode.HALF_UP));
					}
					if ("backupMaximumOutputVoltage".equals(definition)) {
						BigDecimal inputDataBigDecimal = getDataBigDecimal(item);
						table.put("device_23", "backup_maximum_output_voltage", inputDataBigDecimal.divide( new BigDecimal("10"), 0, RoundingMode.HALF_UP));
					}
					// 高级设置设置 device24 表字段值设置
					putDevice24Value(table, definition, item, allAddressMap4Definition);
				}
			});

			return getReturnByMqttResult(deviceAdvancedSetup, result, table, items);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			throw new BusinessException("client.system.error");
			//return R.fail("system error");
		}
	}

	@Nullable
	private static BigDecimal getDataBigDecimal(AppAdvancedSetup.SetupItem item) {
		Object inputData = item.getData();
		BigDecimal inputDataBigDecimal = null;
		if(inputData instanceof String) {
			inputDataBigDecimal = new BigDecimal(String.valueOf(inputData));
		} else if (inputData instanceof Integer) {
			inputDataBigDecimal = new BigDecimal(Integer.parseInt(inputData.toString()));
		}
		return inputDataBigDecimal;
	}

	private void setChangeDevice24Value(AppAdvancedSetup.SetupItem item, String definition, String unit, AddressMapDefinitionEntity addressMapDefinitionEntity) {
		// 只处理 24表中的高级设置字段，其他的保留原状
		Map<String, String> device24ColumnMap = getDevice24ColumnMap();
		if(device24ColumnMap.containsKey(definition)) {
			// 如果是数字类型的，下发时需要将数值 除以 协议中的单位，（上报时 乘以了 协议中的单位）
			if (ObjectUtil.isNotNull(item.getData())  &&
				("U16".equalsIgnoreCase(addressMapDefinitionEntity.getDataType()) || "U32".equalsIgnoreCase(addressMapDefinitionEntity.getDataType())
					|| "S16".equalsIgnoreCase(addressMapDefinitionEntity.getDataType()) || "S32".equalsIgnoreCase(addressMapDefinitionEntity.getDataType()))) {
				if(StringUtils.isEmpty(unit)) {
					unit = "1";
				}
				Object inputData = item.getData();
				BigDecimal inputDataBigDecimal = new BigDecimal(String.valueOf(inputData));
				BigDecimal result = inputDataBigDecimal.divide(new BigDecimal(unit), 0, RoundingMode.HALF_UP);
				log.info("setChangeDevice24Value -> definition : {} , data : {} ",definition, result);
				item.setData(result);
			}
		}
	}

	public static void main(String[] args) {
		BigDecimal inputDataBigDecimal = new BigDecimal(String.valueOf("7."));
		BigDecimal result = inputDataBigDecimal.divide(new BigDecimal(1), 0, RoundingMode.HALF_UP);
		System.out.println(result);
	}
	private void putDevice24Value(Table<String, String, Object> table,String definition, AppAdvancedSetup.SetupItem item,
										Map<String, AddressMapDefinitionEntity> allAddressMap4Definition) {
		Map<String, String> device24ColumnMap = getDevice24ColumnMap();
		if(device24ColumnMap.containsKey(definition)) {
			Object afterChangeInputData = item.getData();
			BigDecimal afterChangeInputDataBigDecimal = new BigDecimal(String.valueOf(afterChangeInputData));
			AddressMapDefinitionEntity addressMapDefinitionEntity = allAddressMap4Definition.get(definition);
			String unit = addressMapDefinitionEntity.getUnit();
			if (ObjectUtil.isNotNull(afterChangeInputData) && StringUtils.isNotEmpty(unit) &&
				("U16".equalsIgnoreCase(addressMapDefinitionEntity.getDataType()) || "U32".equalsIgnoreCase(addressMapDefinitionEntity.getDataType())
				|| "S16".equalsIgnoreCase(addressMapDefinitionEntity.getDataType()) || "S32".equalsIgnoreCase(addressMapDefinitionEntity.getDataType()))) {
				// 和下发设置相反， 存入数据库 需要 除以 unit （此处获取的值，已经是前端传过来的值， 经过了上面的计算后 乘以 unit的 值）
				BigDecimal result = afterChangeInputDataBigDecimal.multiply(new BigDecimal(unit));
				table.put("device_24", device24ColumnMap.get(definition), result);
			}
		}
	}

	private Map<String,String> getDevice24ColumnMap() {
		Map<String, String> fieldMap = new HashMap<>();
		fieldMap.put("cospCurveNode1Percent", "cosp_curve_node1_percent");
		fieldMap.put("cospCurveNode2Percent", "cosp_curve_node2_percent");
		fieldMap.put("cospCurveNode3Percent", "cosp_curve_node3_percent");
		fieldMap.put("cospCurveNode4Percent", "cosp_curve_node4_percent");
		fieldMap.put("cospCurveNode1Value", "cosp_curve_node1_value");
		fieldMap.put("cospCurveNode2Value", "cosp_curve_node2_value");
		fieldMap.put("cospCurveNode3Value", "cosp_curve_node3_value");
		fieldMap.put("cospCurveNode4Value", "cosp_curve_node4_value");
		fieldMap.put("outputReactivePowerMode", "output_reactive_power_mode");
		fieldMap.put("powerFactorSetting", "power_factor_setting");
		fieldMap.put("reactiveControlResponseTime", "reactive_control_response_time");
		fieldMap.put("curveNode1Percent", "curve_node1_percent");
		fieldMap.put("curveNode2Percent", "curve_node2_percent");
		fieldMap.put("curveNode3Percent", "curve_node3_percent");
		fieldMap.put("curveNode4Percent", "curve_node4_percent");
		fieldMap.put("curveNode1ValueSetting", "curve_node1_value_setting");
		fieldMap.put("curveNode2ValueSetting", "curve_node2_value_setting");
		fieldMap.put("curveNode3ValueSetting", "curve_node3_value_setting");
		fieldMap.put("curveNode4ValueSetting", "curve_node4_value_setting");
		fieldMap.put("curveNode1PercentageSetting", "curve_node1_percentage_setting");
		fieldMap.put("curveNode2PercentageSetting", "curve_node2_percentage_setting");
		fieldMap.put("curveNode3PercentageSetting", "curve_node3_percentage_setting");
		fieldMap.put("curveNode4PercentageSetting", "curve_node4_percentage_setting");
		fieldMap.put("curveNode1ValueSettingPn", "curve_node1_value_setting_pn");
		fieldMap.put("curveNode2ValueSettingPn", "curve_node2_value_setting_pn");
		fieldMap.put("curveNode3ValueSettingPn", "curve_node3_value_setting_pn");
		fieldMap.put("curveNode4ValueSettingPn", "curve_node4_value_setting_pn");
		fieldMap.put("reactivePowerPercent", "reactive_power_percent");
		return fieldMap;
	}
	/**
	 * 下发mqtt消息
	 *
	 * @param deviceAdvancedSetup
	 * @return
	 */
	public abstract R<String> issueSetupToToolKit(AppAdvancedSetup deviceAdvancedSetup);

	/**
	 * 处理mqtt结果
	 *
	 * @param deviceAdvancedSetup
	 * @param result
	 * @param table
	 * @param items
	 * @return
	 */
	protected abstract R<?> getReturnByMqttResult(AppAdvancedSetup deviceAdvancedSetup, R<String> result, Table<String, String, Object> table, List<AppAdvancedSetup.SetupItem> items);

	public R<String> determineDeviceStatus(Long plantId,String deviceSerialNumber) {
		R<String> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		Device21Entity queryDevice21 = new Device21Entity();
		queryDevice21.setPlantId(plantId);
		queryDevice21.setDeviceSerialNumber(deviceSerialNumber);
		List<Device21Entity> list = device21Service.list(Condition.getQueryWrapper(queryDevice21));

		if (CollectionUtils.isEmpty(list)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100017.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100017.autoGetMessage(currentLanguage));
			return r;
		}
		Device21Entity device21Entity = list.get(0);
		String deviceStatus = device21Entity.getDeviceStatus();
		if ("0".equalsIgnoreCase(deviceStatus)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100018.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100018.autoGetMessage(currentLanguage));
			return r;
		}
		return null;
	}

	private void processDateTime(AppAdvancedSetup.SetupItem item, List<AppAdvancedSetup.SetupItem> newItems,
								 String timeZonePhone,String timeZoneDevice) {
		// 解析成年月日时分秒
		String data = (String) item.getData();
		LocalDateTime dateTime = null;
		boolean  isExistTimeZone = false;
		// 根据设备和手机的时区信息转换时间字符串
		if(StringUtils.isNotEmpty(timeZonePhone) && StringUtils.isNotEmpty(timeZoneDevice)){
		    dateTime = DateUtil.convertStringTimeZone(data, timeZonePhone, timeZoneDevice);
			isExistTimeZone = true;
		}else {
		    // 如果时区信息不完整，则直接将字符串转换为本地服务器日期时间
		    dateTime = DateUtil.stringDateTime2LocalDateTime(data);
		}
		// 将下发更新时间的操作日志描述内容进行修改，添加当前下发的时间对应时区
		String localDateTimeToStringDateTime = DateUtil.convertLocalDateTimeToStringDateTime(dateTime);
		item.setData(localDateTimeToStringDateTime);
		if (isExistTimeZone){
			item.setDataDesc(localDateTimeToStringDateTime + " " + timeZoneDevice);
		}else {
			item.setDataDesc(localDateTimeToStringDateTime + " " + CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		}
		// 调用方法将日期部分转换为特定的数据格式
		int dataFormatConversion = dataFormatConversion(dateTime.getYear(), dateTime.getMonthValue(), dateTime.getDayOfMonth());
		// 调用方法将时间部分转换为特定的数据格式
		int dataFormatConversionForHour = dataFormatConversionForHour(dateTime.getHour(), dateTime.getMinute(), dateTime.getSecond());
		// 创建并配置第一个设置项，用于保存日期格式转换后的数据
		AppAdvancedSetup.SetupItem setupItem1 = new AppAdvancedSetup.SetupItem();
		setupItem1.setAddress(0x3000);
		setupItem1.setLen(2);
		setupItem1.setData(dataFormatConversion);
		newItems.add(setupItem1);
		// 创建并配置第二个设置项，用于保存时间格式转换后的数据
		AppAdvancedSetup.SetupItem setupItem2 = new AppAdvancedSetup.SetupItem();
		setupItem2.setAddress(0x3002);
		setupItem2.setLen(2);
		setupItem2.setData(dataFormatConversionForHour);
		newItems.add(setupItem2);
	}

	/**
	 * 年月日 数据格式转换
	 */
	public static int dataFormatConversion(int year, int month, int day) {
		return year << 16 | month << 8 | day;
	}

	/**
	 * 时分秒 数据格式转换
	 */
	public static int dataFormatConversionForHour(int hour, int minute, int second) {
		return (hour << 8 | minute) << 16 | second << 8;
	}

	/**
	 * 类型转换
	 *
	 * @param obj
	 * @return
	 */
	public static Object convertToInt(Object obj) {
		if (obj instanceof Integer) {
			return obj; // Already an int, no conversion needed
		} else if (obj instanceof Double) {
			try {
				return Double.parseDouble(String.valueOf(obj));
			} catch (NumberFormatException e) {
				return obj; // Cannot convert to int, return original object
			}
		} else if (obj instanceof String) {
			try {
				return Integer.parseInt((String) obj);
			} catch (NumberFormatException e) {
				// 处理无法转换的情况
				log.error("Cannot convert String to Integer: " + obj, e);
				return null; // 或者抛出异常，根据需求决定
			}
		}
		else {
			try {
				return Integer.parseInt((String) obj); // Convert string to int
			} catch (NumberFormatException e) {
				return obj; // Cannot convert to int, return original object
			}
		}
	}

	public void updatePlantInfo(AppAdvancedSetup deviceAdvancedSetup) {
		log.info("/commonSetup/issue updatePlantInfo ");
		if (ObjectUtil.isNotNull(deviceAdvancedSetup.getIssueSetupType())) {
			List<AppAdvancedSetup.SetupItem> setupItems = deviceAdvancedSetup.getSetupItems();
			for (AppAdvancedSetup.SetupItem setupItem : setupItems) {
				//更新并机模式
				if ("parallelModeFunction".equals(setupItem.getDefinition())) {
					log.info("/commonSetup/issue updatePlantInfo update parallelModeFunction plantId: {}",deviceAdvancedSetup.getPlantId());
					PlantEntity updatePlantEntity = new PlantEntity();
					LambdaQueryWrapper<Device23Entity> queryOtherSn = Wrappers.<Device23Entity>query().lambda().eq(Device23Entity::getPlantId, deviceAdvancedSetup.getPlantId())
							.ne(Device23Entity::getDeviceSerialNumber, deviceAdvancedSetup.getDeviceSerialNumber()).eq(Device23Entity::getParallelModeFunction, Constants.ONE);
					long dbOtherSnCount = device23Service.count(queryOtherSn);
					log.info("/commonSetup/issue updatePlantInfo updatePlant : {}, dbOtherSnCount : {}",deviceAdvancedSetup.getPlantId(),dbOtherSnCount);
					// 如果解绑站点下还有其他逆变器时并机，则站点为并机，如果站点下其他逆变器全部不是并机，则站点为非并机
					if (dbOtherSnCount == 0) {
						updatePlantEntity.setIsParallelMode(BizConstant.CLIENT_PLANT_PARALLEL_MODE_NO);
						if(BizConstant.CLIENT_PLANT_PARALLEL_MODE_YES.equals(setupItem.getData().toString())) {
							updatePlantEntity.setIsParallelMode(BizConstant.CLIENT_PLANT_PARALLEL_MODE_YES);
						}
					} else {
						updatePlantEntity.setIsParallelMode(BizConstant.CLIENT_PLANT_PARALLEL_MODE_YES);
					}
					updatePlantEntity.setId(deviceAdvancedSetup.getPlantId());
					log.info("/commonSetup/issue updatePlantInfo updatePlant : {}",deviceAdvancedSetup.getPlantId());
					plantService.updatePlant(updatePlantEntity);
					log.info("/commonSetup/issue updatePlantInfo updatePlant end : {}",deviceAdvancedSetup.getPlantId());
				}
			}
		}
	}
	void saveOrUpdateTimeZoneDevice(AppAdvancedSetup deviceAdvancedSetup) {
		String timeZone = deviceAdvancedSetup.getTimeZone();
		if (ObjectUtil.isNotNull(timeZone)) {
			LambdaQueryWrapper<TimeZoneDevice> queryWrapper = Wrappers.<TimeZoneDevice>query().lambda();
			// 改为站点id查询是否有记录时区
			queryWrapper.eq(TimeZoneDevice::getPlantId, deviceAdvancedSetup.getPlantId());
			TimeZoneDevice timeZoneDeviceServiceOne = timeZoneDeviceService.getOne(queryWrapper);
			TimeZoneDevice timeZoneDevice = new TimeZoneDevice();
			if (ObjectUtil.isNotNull(timeZoneDeviceServiceOne)) {
				// 如果有历史数据则设置id，mybatis-plus走update方法
				timeZoneDevice.setId(timeZoneDeviceServiceOne.getId());
			}

			// 无数据，走save方法
			timeZoneDevice.setPlantId(deviceAdvancedSetup.getPlantId());
			timeZoneDevice.setTimeZone(timeZone);
			timeZoneDevice.setUpdateTime(new Date());
			timeZoneDeviceService.saveOrUpdate(timeZoneDevice);
		}
	}

	/**
	 * 通过sn获取出厂信息表中的型号、协议版本
	 *
	 * @param appSetRequestVO
	 * @param deviceSerialNumber
	 */
	private void getModelAndProtocolBySn(AppSetRequestVO appSetRequestVO, String deviceSerialNumber) {
		//逆变器
		if (appSetRequestVO.getDeviceType().equals(0)) {
			//默认协议版本
			appSetRequestVO.setSetItemProtocolVersion(DEFAULT_MODBUS_VERSION);
			DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
			deviceExitFactoryInfoEntity.setDeviceSerialNumber(deviceSerialNumber);
			DeviceExitFactoryInfoEntity deviceExitFactoryInfo = deviceExitFactoryInfoService.getModelAndProtocolBySn(deviceExitFactoryInfoEntity);
			if (ObjectUtil.isNotNull(deviceExitFactoryInfo)) {
				if (ObjectUtil.isNotNull(deviceExitFactoryInfo.getDeviceType())) {
					appSetRequestVO.setDeviceModel(deviceExitFactoryInfo.getDeviceType());
				} else if (ObjectUtil.isNotNull(deviceExitFactoryInfo.getModbusProtocolVersion())) {
					appSetRequestVO.setSetItemProtocolVersion(deviceExitFactoryInfo.getModbusProtocolVersion());
				}
			}
			//电池
		} else if (appSetRequestVO.getDeviceType().equals(1)) {
			appSetRequestVO.setSetItemProtocolVersion(DEFAULT_MODBUS_VERSION);
			BatteryExitFactoryInfoEntity batteryExitFactoryInfoEntity = new BatteryExitFactoryInfoEntity();
			batteryExitFactoryInfoEntity.setBatterySerialNumber(deviceSerialNumber);
			BatteryExitFactoryInfoEntity batteryExitFactoryInfo = batteryExitFactoryInfoService.getModelAndProtocolBySn(batteryExitFactoryInfoEntity);
			if (ObjectUtil.isNotNull(batteryExitFactoryInfo)) {
				if (ObjectUtil.isNotNull(batteryExitFactoryInfo.getBatteryType())) {
					appSetRequestVO.setDeviceModel(batteryExitFactoryInfo.getBatteryType());
				} else if (ObjectUtil.isNotNull(batteryExitFactoryInfo.getModbusProtocolVersion())) {
					appSetRequestVO.setSetItemProtocolVersion(batteryExitFactoryInfo.getModbusProtocolVersion());
				}
			}
		}
	}

	protected Integer getDecimalAddressByDefinition(String definition) {
		return addressMapDefinitionService.getDecimalAddressByDefinition(definition);
	}
}
