package org.springblade.resource.utils;

import lombok.RequiredArgsConstructor;
import org.springblade.core.redis.cache.BladeRedis;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 华为云共享签名
 *
 * @author: SDT50545
 * @since: 2024-03-19 17:36
 **/
@Component
@RequiredArgsConstructor
public class HuaweiObsSign {
	private static final String SIGN_SEP = "\n";

	private static final String OBS_PREFIX = "x-obs-";

	private static final String DEFAULT_ENCODING = "UTF-8";

	private static final List<String> SUB_RESOURCES = List.of("CDNNotifyConfiguration", "acl", "append", "attname", "backtosource", "cors", "customdomain", "delete", "deletebucket", "directcoldaccess", "encryption", "inventory", "length", "lifecycle", "location", "logging", "metadata", "mirrorBackToSource", "modify", "name", "notification", "obscompresspolicy", "orchestration", "partNumber", "policy", "position", "quota", "rename", "replication", "response-cache-control", "response-content-disposition", "response-content-encoding", "response-content-language", "response-content-type", "response-expires", "restore", "storageClass", "storagePolicy", "storageinfo", "tagging", "torrent", "truncate", "uploadId", "uploads", "versionId", "versioning", "versions", "website", "x-image-process", "x-image-save-bucket", "x-image-save-object", "x-obs-security-token", "object-lock", "retention");
	@Value("${oss.access-key:}")
	private String ak;

	@Value("${oss.secret-key:}")
	private String sk;

	@Value("${oss.endpoint:}")
	private String endpoint;

	private final BladeRedis bladeRedis;

	private static final String PATTERN_BUCKET_NAME_RULE1 = "^[a-z0-9][a-z0-9.-]+$";

	private static final String PATTERN_BUCKET_NAME_RULE2 = "(\\d{1,3}\\.){3}\\d{1,3}";

	private boolean isBucketNameValid(String bucketName) {
		if (bucketName == null || bucketName.length() > 63 || bucketName.length() < 3) {
			return false;
		}

		if (!Pattern.matches(PATTERN_BUCKET_NAME_RULE1, bucketName)) {
			return false;
		}

		if (Pattern.matches(PATTERN_BUCKET_NAME_RULE2, bucketName)) {
			return false;
		}

		String[] fragments = bucketName.split("\\.");
		for (String fragment : fragments) {
			if (Pattern.matches("^-.*", fragment) || Pattern.matches(".*-$", fragment)
				|| Pattern.matches("^$", fragment)) {
				return false;
			}
		}

		return true;
	}

	private String encodeUrlString(String path) throws UnsupportedEncodingException {
		return URLEncoder.encode(path, DEFAULT_ENCODING)
			.replaceAll("\\+", "%20")
			.replaceAll("\\*", "%2A")
			.replaceAll("%7E", "~");
	}

	private String encodeObjectName(String objectName) throws UnsupportedEncodingException {
		StringBuilder result = new StringBuilder();
		String[] tokens = objectName.split("/");
		for (int i = 0; i < tokens.length; i++) {
			result.append(this.encodeUrlString(tokens[i]));
			if (i < tokens.length - 1) {
				result.append("/");
			}
		}
		return result.toString();
	}

	private String join(List<?> items) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < items.size(); i++) {
			String item = items.get(i).toString();
			sb.append(item);
			if (i < items.size() - 1) {
				sb.append(",");
			}
		}
		return sb.toString();
	}

	private boolean isValid(String input) {
		return input != null && !input.isEmpty();
	}

	private String hmacSha1(String input) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
		SecretKeySpec signingKey = new SecretKeySpec(this.sk.getBytes(DEFAULT_ENCODING), "HmacSHA1");
		Mac mac = Mac.getInstance("HmacSHA1");
		mac.init(signingKey);
		return Base64.getEncoder().encodeToString(mac.doFinal(input.getBytes(DEFAULT_ENCODING)));
	}

	private String stringToSign(String httpMethod, Map<String, String[]> headers, Map<String, String> queries,
								String bucketName, String objectName, long expires) throws Exception {
		String contentMd5 = "";
		String contentType = "";
		TreeMap<String, String> canonicalizeHeaders = new TreeMap<>();
		String key;
		List<String> temp = new ArrayList<>();
		for (Map.Entry<String, String[]> entry : headers.entrySet()) {
			key = entry.getKey();
			if (key == null || entry.getValue() == null || entry.getValue().length == 0) {
				continue;
			}
			key = key.trim().toLowerCase(Locale.ENGLISH);
			if ("content-md5".equals(key)) {
				contentMd5 = entry.getValue()[0];
				continue;
			}
			if ("content-type".equals(key)) {
				contentType = entry.getValue()[0];
				continue;
			}
			if (key.startsWith(OBS_PREFIX)) {
				for (String value : entry.getValue()) {
					if (value != null) {
						temp.add(value.trim());
					}
				}
				canonicalizeHeaders.put(key, this.join(temp));
				temp.clear();
			}
		}
		// handle method/content-md5/content-type
		StringBuilder stringToSign = new StringBuilder();
		stringToSign.append(httpMethod).append(SIGN_SEP)
			.append(contentMd5).append(SIGN_SEP)
			.append(contentType).append(SIGN_SEP)
			.append(expires).append(SIGN_SEP);


		// handle canonicalHeaders
		for (Map.Entry<String, String> entry : canonicalizeHeaders.entrySet()) {
			stringToSign.append(entry.getKey()).append(":").append(entry.getValue()).append(SIGN_SEP);
		}


		// handle canonicalResource
		stringToSign.append("/");
		if (this.isValid(bucketName)) {
			stringToSign.append(bucketName).append("/");
			if (this.isValid(objectName)) {
				stringToSign.append(this.encodeObjectName(objectName));
			}
		}

		TreeMap<String, String> canonicalResource = new TreeMap<>();
		for (Map.Entry<String, String> entry : queries.entrySet()) {
			key = entry.getKey();
			if (key == null) {
				continue;
			}

			if (SUB_RESOURCES.contains(key)) {
				canonicalResource.put(key, entry.getValue());
			}
		}

		if (!canonicalResource.isEmpty()) {
			stringToSign.append("?");
			for (Map.Entry<String, String> entry : canonicalResource.entrySet()) {
				stringToSign.append(entry.getKey());
				if (this.isValid(entry.getValue())) {
					stringToSign.append("=").append(entry.getValue());
				}
				stringToSign.append("&");
			}
			stringToSign.deleteCharAt(stringToSign.length() - 1);
		}
		return stringToSign.toString();
	}

	/**
	 * 获取签名
	 *
	 * @param httpMethod 方法类型 GET/POST/PUT/DELETE
	 * @param headers    头部参数
	 * @param queries    参数
	 * @param bucketName 桶
	 * @param objectName 文件名
	 * @param expires    入参
	 * @return String
	 * <AUTHOR>
	 * @since 2024/3/20 9:34
	 **/
	private String querySignature(String httpMethod, Map<String, String[]> headers, Map<String, String> queries,
								  String bucketName, String objectName, long expires) throws Exception {
		if (!isBucketNameValid(bucketName)) {
			throw new IllegalArgumentException("the bucketName is illegal");
		}
		//1. stringToSign
		String stringToSign = this.stringToSign(httpMethod, headers, queries, bucketName, objectName, expires);

		//2. signature
		return this.encodeUrlString(this.hmacSha1(stringToSign));
	}

	/**
	 * 获取完整临时url
	 *
	 * @param queries    参数集
	 * @param bucketName 桶
	 * @param objectName 文件名
	 * @param headers    头信息参数集
	 * @param expires    失效时间
	 * @return String
	 * <AUTHOR>
	 * @since 2024/3/20 9:32
	 **/
	public String getUrl(Map<String, String> queries,
						 String bucketName, String objectName, Map<String, String[]> headers, long expires) throws Exception {
		return "https://" + bucketName + "." + this.endpoint + "/" +
			this.encodeObjectName(objectName) + "?" + getSasToken(queries, bucketName, objectName, headers, expires);
	}

	/**
	 * 获取sasToken
	 *
	 * @param queries    参数集
	 * @param bucketName 桶
	 * @param objectName 文件名
	 * @param headers    头信息参数集
	 * @param expires    失效时间
	 * @return String 返回签名
	 */
	public String getSasToken(Map<String, String> queries,
							  String bucketName, String objectName, Map<String, String[]> headers, long expires) throws Exception {
		StringBuilder url = new StringBuilder();
		String signature = this.querySignature("GET", headers, queries, bucketName, objectName, expires);
		String key;
		for (Map.Entry<String, String> entry : queries.entrySet()) {
			key = entry.getKey();
			if (key == null) {
				continue;
			}
			if (SUB_RESOURCES.contains(key)) {
				String value = entry.getValue();
				url.append(key);
				if (value != null) {
					url.append("=").append(value).append("&");
				} else {
					url.append("&");
				}
			}
		}
		url.append("AccessKeyId=").append(this.ak).append("&Expires=").append(expires).
			append("&Signature=").append(signature);
		//设置redis签名有效时间为半小时
		bladeRedis.setEx(objectName, url.toString(), Duration.ofMinutes(30));
		return url.toString();
	}
}
