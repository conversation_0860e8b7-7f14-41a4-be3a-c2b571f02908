/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.excel;


import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 电池每日统计 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class BatteryEverydayTotalExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 统计日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("统计日期")
	private BigDecimal totalDate;
	/**
	 * 充电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("充电能量")
	private BigDecimal batteryDailyChargeEnergy;
	/**
	 * 放电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("放电能量")
	private BigDecimal batteryDailyDischargeEnergy;
	/**
	 * 累计充电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计充电能量")
	private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 * 累计放电能量
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计放电能量")
	private BigDecimal batteryAccumulatedDischargeEnergy;
	/**
	 * 吞吐量使用率
	 */
	@ColumnWidth(20)
	@ExcelProperty("吞吐量使用率")
	private BigDecimal throughputUseRate;
	/**
	 * 最高单体电池电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("最高单体电池电压")
	private BigDecimal batteryMaximumCellVoltage;
	/**
	 * 最低单体电池电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("最低单体电池电压")
	private BigDecimal batteryMinimumCellVoltage;
	/**
	 * 最高单体电池温度
	 */
	@ColumnWidth(20)
	@ExcelProperty("最高单体电池温度")
	private BigDecimal batteryMaximumCellTemperature;
	/**
	 * 最低单体电池温度
	 */
	@ColumnWidth(20)
	@ExcelProperty("最低单体电池温度")
	private BigDecimal batteryMinimumCellTemperature;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
