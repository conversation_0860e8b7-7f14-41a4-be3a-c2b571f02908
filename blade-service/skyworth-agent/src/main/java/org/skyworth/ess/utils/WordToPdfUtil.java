package org.skyworth.ess.utils;

import com.aspose.words.*;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.utils.FileUtil;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;

@Slf4j
public class WordToPdfUtil {


    public static void main(String[] args) {
        System.out.println("开始转pdf...");
        // 获取当前项目路径
        String projectDir = System.getProperty("user.dir");
        if (StringUtils.isBlank(projectDir)) {
            return;
        }
        setFontsSources();
        WordToPdfUtil.doc2pdf(projectDir + "/blade-service/skyworth-agent/src/main/resources/quotefile/Advanced Standard Quote Template.docx",
                projectDir + "/blade-service/skyworth-agent/src/main/resources/Advanced Standard Quote.pdf");
    }

    /**
     * 设置字体资源
     * 不设置会导致word转pdf的时候字体丢失
     */
    public static void setFontsSources() {
        // 把模板文件读取到本地目录中
        Resource resource = new ClassPathResource("fonts/SimHei.ttf");
        InputStream in = null;
        File file = null;
        try {
            in = resource.getInputStream();
            file = new File("/tmp/SimHei.ttf");
            FileUtils.copyToFile(in, file);
            String fontsDir = "/tmp";
            //将用户目录字体添加到字体源中
            FontSourceBase[] originalFontSources = FontSettings.getDefaultInstance().getFontsSources();
            FolderFontSource folderFontSource = new FolderFontSource(fontsDir, true);

            FontSourceBase[] updatedFontSources = {originalFontSources[0], folderFontSource};
            FontSettings fontSettings = FontSettings.getDefaultInstance();
            fontSettings.setFontsSources(updatedFontSources);
            fontSettings.getSubstitutionSettings().getDefaultFontSubstitution().setDefaultFontName("SimHei");
            log.info("字体资源设置成功");
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            // 删除临时文件
            if (file != null) {
                try {
                    Files.delete(file.toPath());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    /**
     * Word转PDF操作
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     */
    public static void doc2pdf(String sourceFile, String targetFile) {
        FileOutputStream os = null;
        try {
            // 设置字体资源
            setFontsSources();
            long old = System.currentTimeMillis();
            os = new FileOutputStream(targetFile);
            Document document= new Document(sourceFile);
            Document doc = new Document();
            doc.removeAllChildren();
            // 保留原始样式
            doc.appendDocument(document, ImportFormatMode.USE_DESTINATION_STYLES);
            // 设置字体
//            doc.getChildNodes(NodeType.RUN, true).forEach(node -> {
//                Run run = (Run) node;
//                try {
//                    run.getFont().setName("SimHei");
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    log.error("设置字体失败");
//                }
//            });
            doc.save(os, SaveFormat.PDF);
            long now = System.currentTimeMillis();
            log.info("pdf转换成功，共耗时：{} 秒", (now - old) / 1000.0);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("pdf转换失败, 错误信息：{}", e.getMessage());
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


}

