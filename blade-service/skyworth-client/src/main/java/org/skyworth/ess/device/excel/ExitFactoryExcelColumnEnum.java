package org.skyworth.ess.device.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum ExitFactoryExcelColumnEnum {
	deviceSerialNumber("Inverter SN", "设备/逆变器SN"),
	deviceType("Gerätemodell", "设备/逆变器型号"),
	company("Hersteller", "厂家"),
	qualityGuaranteeYear("Garantiezeit", "质保年限"),
	exitFactoryDate("Fabrikdatum", "出厂日期"),
	power("Nennleistung(kW)", "功率"),
	singleThirdPhase("einphasig/dreiphasig", ""),
	netType("Off Grid/Grid Connected/Hybrid", "单相/三相/混合"),

	firmwareBatch("Device Firmware Batch", "固件批次");

	private String columnEn;
	private String columnCn;

	ExitFactoryExcelColumnEnum(String columnEn, String columnCn) {
		this.columnEn = columnEn;
		this.columnCn = columnCn;
	}

	public static Set<String> getColumn(String language) {
		Set<String> result = new HashSet<>();
		for (ExitFactoryExcelColumnEnum item : ExitFactoryExcelColumnEnum.values()) {
			if (CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
				result.add(item.columnCn);
			} else {
				result.add(item.columnEn);
			}
		}
		return result;
	}
}
