/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 站点信息表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlantVO extends PlantEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 国家名称
	 */
	private String countryName;
	/**
	 * 一级行政区域名称
	 */
	private String provinceName;
	/**
	 * 二级行政区域名称
	 */
	private String cityName;
	/**
	 * 三级行政区域名称
	 */
	private String countyName;
	/**
	 * 站点数
	 */
	private Long plantNum;
	/**
	 * 碳排放
	 */
	private Integer carbonEmission = 0;
	/**
	 * 光伏发电功率总和
	 */
	private BigDecimal pvPowerGenerationSum;
	/**
	 * 历史光伏发电功率累计总和  所有电站的1021
	 */
	private BigDecimal pvHisPowerGenerationSum;
	/**
	 * 储能总功率
	 */
	private BigDecimal totalEnergy;
	/**
	 * 电池装机总电量
	 */
	private BigDecimal totalBatteryCapacity;
	/**
	 * 国家
	 */
	private String country;
	/**
	 * 一级区域
	 */
	private String regionArea;
	/**
	 * 一级区域
	 */
	private String includeDevices;
	/**
	 * 逆变器总功率
	 */
	private String devicePower;
	/**
	 * 储能电池总能量
	 */
	private String energyBatteryPower;
	/**
	 * 安全运行天数
	 */
	private Long runDay;
	/**
	 * 日发电量 pv
	 */
	private String powerGeneration ;
	/**
	 * 日储电量  es
	 */
	private String loadCapacity ;
	/**
	 * 日放电量  ec
	 */
	private String powerConsumption ;
	/**
	 * 逆变器sn
	 */
	private String deviceSerialNumber;
	/**
	 * 状态
	 */
	private String plantStatus;
	/**
	 * 名称
	 */
	private String plantName;
	private String address;
	/**
	 * 0 开机
	 */
	private String inverterControl;

	/**
	 * 该逆变器是否开过机：0是 1否
	 */
	private Integer startupByBackstage;
	/**
	 * 该逆变器是否配网成功：0是 1否
	 */
	private Integer inverterConfigureNetwork;

	/**
	 * 手机
	 */
	private String phone;

	/**
	 * 电话区号
	 */
	private String phoneDiallingCode;

	/**
	 *
	 * 手机用户的ids
	 */
	private List<Long> phoneUserIds;
	/**
	 *
	 * 创建用户的ids
	 */
	private List<Long> createUserIds;
	/**
	 *
	 * 运维团队的ids
	 */
	private List<Long> operationCompanyIds;
	/**
	 *
	 * 运维人员的ids
	 */
	private List<Long> operationUserIds;

	/**
	 * 运维团队信息
	 * */
	private AgentCompanyVO companyInfo;

	/**
	 * 运维人员信息
	 * */
	private AgentUserVo agentUserInfo;
	/**
	 * 运维团队
	 * */
	private String agentCompanyName;

	/**
	 * 运维团队的dept_id
	 */
	private Long agentId;

	/**
	 *
	 * 运维团队的dept_id
	 */
	private Long operationCompanyDeptId;

	/**
	 * 运维人员id
	 * */
	private Long agentUserId;
	/**
	 * 运维人员
	 * */
	private String agentUserName;

	@ApiModelProperty(value = "用户手机号")
	private String userPhoneAndAreaCode;

	@ApiModelProperty(value = "用户名")
	private String createdByName;

	@ApiModelProperty(value = "部门id")
	private String deptId;

	@ApiModelProperty(value = "设备/逆变器种类")
	@TableField(exist = false)
	private String inverterKind;

	private String isParallelMode;
	// 站点下所有设备sn信息
	private List<WifiStickPlantEntity> wifiStickPlantEntityList;
	// app 模式查询条件
	private String listSearchCondition;

	//@TableField(exist = false)
	//// 是否并机 0否 1是
	//private String parallelModeFunction;

	public Long getOperationCompanyDeptId() {
		if(this.operationCompanyDeptId != null){
			return this.operationCompanyDeptId ;
		}
		return super.getOperationCompanyId();
	}
}

