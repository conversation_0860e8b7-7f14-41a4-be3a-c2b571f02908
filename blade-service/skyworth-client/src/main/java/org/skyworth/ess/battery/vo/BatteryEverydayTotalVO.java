/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.vo;

import org.skyworth.ess.app.vo.AppReportDataVO;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.springblade.core.tool.node.INode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 电池每日统计 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BatteryEverydayTotalVO extends BatteryEverydayTotalEntity {
	private static final long serialVersionUID = 1L;

	private String appTotalDate;
	/**
	 * 光伏日发电量 200B
	 */
	private BigDecimal appBatteryDailyChargeEnergy;
	// 地址码 200F
	private BigDecimal appBatteryDailyDischargeEnergy;
	/**
	 * 电池总储电量
	 */
	private BigDecimal appBatteryAccumulatedChargeEnergy;
	// 地址码 1027
	private BigDecimal appTodayEnergy;
	// 地址码 1336
	private BigDecimal appTodayLoadEnergy;
	// 地址码 1360
	private BigDecimal appDailyEnergyToEps;
	// 地址码 1336 + 1360
	private BigDecimal appLoadAddEps;
	// 1332
	private BigDecimal appTodayImportEnergy;
	// 1334
	private BigDecimal appTodayExportEnergy;
	/* 1、天报表为： L1+L2+L3=1503~1504+1508~1509+150D~150E
	 2、周月年报表为：0x1514~0x1515
	 */
	private BigDecimal appOtherPv;
	// 并机 begin
	// 13D8
	private BigDecimal appPvlDailyGeneratingEnergySum;
	// 13E4
	private BigDecimal appBatteryDailyDischargeEnergyParallel;
	// 13AC + 13C0
	private BigDecimal appLoadAddBackup;
	// 13E0
	private BigDecimal appBatteryDailyChargeEnergyParallel;
	// 并机 end
}
