package org.skyworth.ess.fegin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.client.ISoftVersionBizClient;
import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity;
import org.skyworth.ess.ota.service.IDeviceSoftwareVersionInfoService;
import org.skyworth.ess.ota.service.ISoftwareUpgradeRecordService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class SoftVersionBizClient implements ISoftVersionBizClient {

	private final IDeviceSoftwareVersionInfoService softwareVersionInfoService;
	private final ISoftwareUpgradeRecordService softwareUpgradeRecordService;


	@PostMapping(address)
	@Override
	public void updateVersion(@RequestBody List<JSONObject> jsonObjects) {
		jsonObjects.forEach(data -> {
			String type = data.getString("type");
			if (ValidationUtil.isNotEmpty(type)) {
				if ("inverter".equals(type) || "wifi".equals(type) || "battery".equals(type)) {//逆变器 或者wifi棒 电池
					String deviceSn = data.getString("deviceSn");
					String company = data.getString("company");
					Map<String, String> map = new HashMap<>();
					String bigType;
					// 查询当前已存在的记录
					LambdaQueryWrapper<DeviceSoftwareVersionInfoEntity> eq = Wrappers.<DeviceSoftwareVersionInfoEntity>query().lambda()
						.eq(DeviceSoftwareVersionInfoEntity::getSerialNumber, deviceSn);
					List<DeviceSoftwareVersionInfoEntity> softwareVersionInfoEntityList = softwareVersionInfoService.list(eq);
					Map<String, Long> existsVersionMap = Optional.ofNullable(softwareVersionInfoEntityList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(a -> a.getBigType() + a.getSmallType(), a -> a.getId(), (a, b) -> a));
					Map<String, DeviceSoftwareVersionInfoEntity> updateStatus = Optional.ofNullable(softwareVersionInfoEntityList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(a -> a.getBigType() + a.getSmallType(), entity -> entity));


					List<DeviceSoftwareVersionInfoEntity> addList = new ArrayList<>();
					List<DeviceSoftwareVersionInfoEntity> updateList = new ArrayList<>();
					if ("wifi".equals(type)) {
						bigType = "wifibom";
						String wifiVersion = data.getString("wifiVersion");
						DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo = new DeviceSoftwareVersionInfoEntity();
						deviceSoftwareVersionInfo.setCompany(company);
						deviceSoftwareVersionInfo.setSerialNumber(deviceSn);
						deviceSoftwareVersionInfo.setBigType(bigType);
						deviceSoftwareVersionInfo.setSmallType("wifibom-smalltype");
						deviceSoftwareVersionInfo.setCurrentVersionNumber(wifiVersion);
						deviceSoftwareVersionInfo.setStatus(2);
						deviceSoftwareVersionInfo.setTenantId(CommonConstant.CLIENT_TENANT_ID);
						deviceSoftwareVersionInfo.setLatestReleasedVersion(wifiVersion);
						if (existsVersionMap.containsKey(bigType + "wifibom-smalltype")) {
							deviceSoftwareVersionInfo.setId(existsVersionMap.get(bigType + "wifibom-smalltype"));
							DeviceSoftwareVersionInfoEntity entity = updateStatus.get(bigType + "wifibom-smalltype");
							if (!(0 == entity.getStatus() || 1 == entity.getStatus() || wifiVersion.equals(entity.getCurrentVersionNumber()))) {
								updateList.add(deviceSoftwareVersionInfo);
							}
						} else {
							addList.add(deviceSoftwareVersionInfo);
						}
					} else if ("inverter".equals(type)) {
						bigType = "inverter";
						String master = data.getString("master");
						String slave = data.getString("slave");
						String ems = data.getString("ems");
						String dc = data.getString("dc");
						map.put("master", master);
						map.put("slave", slave);
						map.put("ems", ems);
						map.put("dc", dc);
						for (Map.Entry<String, String> entry : map.entrySet()) {
							String value = entry.getValue();
							String key = entry.getKey();
							if (ValidationUtil.isNotEmpty(value)) {
								DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo = new DeviceSoftwareVersionInfoEntity();
								deviceSoftwareVersionInfo.setCompany(company);
								deviceSoftwareVersionInfo.setSerialNumber(deviceSn);
								deviceSoftwareVersionInfo.setBigType(bigType);
								deviceSoftwareVersionInfo.setSmallType(key);
								deviceSoftwareVersionInfo.setCurrentVersionNumber(value);
								deviceSoftwareVersionInfo.setStatus(2);
								deviceSoftwareVersionInfo.setTenantId(CommonConstant.CLIENT_TENANT_ID);
								deviceSoftwareVersionInfo.setLatestReleasedVersion(value);
								if (existsVersionMap.containsKey(bigType + key)) {
									deviceSoftwareVersionInfo.setId(existsVersionMap.get(bigType + key));
									DeviceSoftwareVersionInfoEntity entity = updateStatus.get(bigType + key);
									if (!(0 == entity.getStatus() || 1 == entity.getStatus() || value.equals(entity.getCurrentVersionNumber()))) {
										updateList.add(deviceSoftwareVersionInfo);
									}
								} else {
									addList.add(deviceSoftwareVersionInfo);
								}
							}
						}
					} else { //电池
						bigType = "battery";
						String bmsFirmwareVersion = data.getString("bmsFirmwareVersion");
						if (ValidationUtil.isNotEmpty(bmsFirmwareVersion)) {
							DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo = new DeviceSoftwareVersionInfoEntity();
							deviceSoftwareVersionInfo.setCompany(company);
							deviceSoftwareVersionInfo.setSerialNumber(deviceSn);
							deviceSoftwareVersionInfo.setBigType(bigType);
							deviceSoftwareVersionInfo.setSmallType("battery-bms");
							deviceSoftwareVersionInfo.setCurrentVersionNumber(bmsFirmwareVersion);
							deviceSoftwareVersionInfo.setStatus(2);
							deviceSoftwareVersionInfo.setTenantId(CommonConstant.CLIENT_TENANT_ID);
							deviceSoftwareVersionInfo.setLatestReleasedVersion(bmsFirmwareVersion);
							if (existsVersionMap.containsKey(bigType + "battery-bms")) {
								deviceSoftwareVersionInfo.setId(existsVersionMap.get(bigType + "battery-bms"));
								DeviceSoftwareVersionInfoEntity entity = updateStatus.get(bigType + "battery-bms");
								if (!(0 == entity.getStatus() || 1 == entity.getStatus() || bmsFirmwareVersion.equals(entity.getCurrentVersionNumber()))) {
									updateList.add(deviceSoftwareVersionInfo);
								}
							} else {
								addList.add(deviceSoftwareVersionInfo);
							}
						}
					}
					if (!CollectionUtils.isNullOrEmpty(addList)) {
						// 维护软件版本号
						softwareVersionInfoService.saveOrUpdateBatch(addList);
						// 维护升级日志
						insertDeviceSoftLog(addList, true);
					}
					if (!CollectionUtils.isNullOrEmpty(updateList)) {
						// 维护软件版本号
						softwareVersionInfoService.saveOrUpdateBatch(updateList);
						// 维护升级日志
						insertDeviceSoftLog(updateList, false);
					}
				}
			}
		});
	}

	/**
	 * 保存升级日志
	 *
	 * @param dataList 数据对象
	 * @param isCreate 入参
	 * <AUTHOR>
	 * @since 2024/6/11 11:26
	 **/
	private void insertDeviceSoftLog(List<DeviceSoftwareVersionInfoEntity> dataList, boolean isCreate) {
		List<SoftwareUpgradeRecordEntity> softwareUpgradeRecordEntityList = new ArrayList<>();
		dataList.forEach(a -> {
			SoftwareUpgradeRecordEntity softwareUpgradeRecordEntity = new SoftwareUpgradeRecordEntity();
			softwareUpgradeRecordEntity.setDeviceSoftwareVersionId(a.getId());
			softwareUpgradeRecordEntity.setBigType(a.getBigType());
			softwareUpgradeRecordEntity.setSmallType(a.getSmallType());
			softwareUpgradeRecordEntity.setSerialNumber(a.getSerialNumber());
			softwareUpgradeRecordEntity.setCompany(a.getCompany());
			softwareUpgradeRecordEntity.setCurrentVersionNumber(a.getCurrentVersionNumber());
			softwareUpgradeRecordEntity.setStatus(BizConstant.NUMBER_TWO);
			softwareUpgradeRecordEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			softwareUpgradeRecordEntity.setLatestReleasedVersion(a.getCurrentVersionNumber());
			// 初始化上报版本
			if (isCreate) {
				softwareUpgradeRecordEntity.setFailureReason("Initialize device reporting");
			}
			// wifi棒断线重连，刷新版本号
			else {
				softwareUpgradeRecordEntity.setFailureReason("Device reconnection, refreshing version number");
			}
			softwareUpgradeRecordEntityList.add(softwareUpgradeRecordEntity);
		});
		softwareUpgradeRecordService.saveBatch(softwareUpgradeRecordEntityList);
	}
}
