package org.skyworth.ess.device.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.springblade.core.mp.mapper.BladeMapper;

import java.util.List;
import java.util.Map;

public interface TimeZoneDeviceMapper extends BladeMapper<TimeZoneDevice> {


    List<TimeZoneDevice> getListBySnList(@Param("deviceSnList") List<String> deviceSnList);

	List<TimeZoneDevice> getListByPlantIdList(@Param("plantIdList") List<Long> plantIdList);

	@MapKey("id")
	List<Map<String,String>> getTimeZoneListByPlantIds(@Param("plantIds") List<String> plantIds);

    void deleteByPlantId(@Param("plantIdList")List<Long> longList);
}
