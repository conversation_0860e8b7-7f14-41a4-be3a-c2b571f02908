package org.springblade.resource.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.utils.UniqueIdentifierGenerator;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.resource.builder.oss.OssBuilder;
import org.springblade.resource.vo.AttachVO;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 华为云上传文件帮助类
 * @description:
 * @author: SDT50545
 * @since: 2024-03-20 10:50
 **/
public class HuaweiUploadUtils {
	private static final Logger logger = LoggerFactory.getLogger(HuaweiUploadUtils.class);
	/**
	 * 设置缩略图的宽高
	 */
	private static final int THUMBNAIL_WIDTH = 150;
	/**
	 * 设置缩略图的宽高
	 */
	private static final int THUMBNAIL_HEIGHT = 150;

	public static String getFileExtension(String fileName) {
		int position = fileName.lastIndexOf('.');
		if (position > 0) {
			return fileName.substring(position);
		}
		return "";
	}

	public static void uploadThumbnailImages(String bucketName, MultipartFile tempMultipartFile, AttachVO attachVO, OssBuilder ossBuilder) throws IOException {
		// 获取原图的宽度和高度
		BufferedImage bufferedImage = ImageIO.read(tempMultipartFile.getInputStream());
		// 原图宽度
		int originalDrawingWidth = bufferedImage.getWidth();
		// 原图高度
		int originalDrawingHeight = bufferedImage.getHeight();
		// 缩略图宽度
		int thumbnailWidth = THUMBNAIL_WIDTH;
		// 缩略图高度
		int thumbnailHeight = THUMBNAIL_HEIGHT;
		// 计算缩略图高度,宽度
		if (originalDrawingWidth / originalDrawingHeight > thumbnailWidth / thumbnailHeight) {
			thumbnailHeight = originalDrawingHeight * thumbnailWidth / originalDrawingWidth;
		} else {
			thumbnailWidth = originalDrawingWidth * thumbnailHeight / originalDrawingHeight;
		}
		// 上传缩略图
		extracted(bucketName, tempMultipartFile, attachVO, thumbnailWidth, thumbnailHeight, ossBuilder);
	}

	/**
	 * 缩略图
	 *
	 * @param tempMultipartFile 文件流
	 * @param thumbnailWidth    缩略图宽度
	 * @param thumbnailHeight   入参
	 * <AUTHOR>
	 * @since 2024/1/4 16:21
	 **/
	private static void extracted(String bucketName, MultipartFile tempMultipartFile, AttachVO attachVO, int thumbnailWidth, int thumbnailHeight, OssBuilder ossBuilder) throws IOException {
		//上传缩略图，并上传至AzureStorage
		BufferedImage img = new BufferedImage(thumbnailWidth, thumbnailHeight, BufferedImage.TYPE_INT_RGB);
		img.createGraphics().drawImage(ImageIO.read(tempMultipartFile.getInputStream()).getScaledInstance(thumbnailWidth, thumbnailHeight, Image.SCALE_SMOOTH), 0, 0, null);
		ByteArrayOutputStream thumbnailStream = new ByteArrayOutputStream();
		ImageIO.write(img, "jpg", thumbnailStream);
		InputStream inputStream = null;
		try {
			inputStream = new ByteArrayInputStream(thumbnailStream.toByteArray());
			String thumbnailCheckSum = UniqueIdentifierGenerator.generateUniqueId();
			String thumbnailFileAliasName = thumbnailCheckSum + ".jpg";
			BladeFile bladeFile = ossBuilder.template().putFile(bucketName, thumbnailFileAliasName, inputStream);
			attachVO.setThumbnailDomainUrl(bladeFile.getDomain());
			attachVO.setThumbnailLink(bladeFile.getLink());
			attachVO.setThumbnailName(thumbnailFileAliasName);
			attachVO.setThumbnailOriginalName(thumbnailFileAliasName);
			attachVO.setThumbnailExtension(bladeFile.getName());
		} catch (Exception e) {
			logger.error("Abnormal uploading thumbnail,error:", e);
			throw new BusinessException("resource.upload.thumbnail.exception",e.getMessage());
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (Exception e) {
					logger.error("File stream closing exception,error:", e);
				}
			}
		}
	}

	public static AttachVO buildAttachVO(String fileName, Long fileSize, BladeFile bladeFile) {
		AttachVO attachVO = new AttachVO();
		attachVO.setDomainUrl(bladeFile.getDomain());
		attachVO.setLink(bladeFile.getLink());
		attachVO.setName(fileName);
		attachVO.setOriginalName(fileName);
		attachVO.setAttachSize(fileSize);
		attachVO.setExtension(bladeFile.getName());
		return attachVO;
	}
}
