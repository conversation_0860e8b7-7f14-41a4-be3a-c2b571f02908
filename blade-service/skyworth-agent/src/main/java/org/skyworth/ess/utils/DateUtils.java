package org.skyworth.ess.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtils {

	public static int dateDiff(Date startDate, Date endDate) {
		DateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
		int i = 0;
		try {
			Date star = dft.parse(dft.format(startDate));//开始时间
			Date endDay = dft.parse(dft.format(endDate));//结束时间
			Date nextDay = star;

			while (nextDay.before(endDay)) {//当明天不在结束时间之前是终止循环
				Calendar cld = Calendar.getInstance();
				cld.setTime(star);
				cld.add(Calendar.DATE, 1);
				star = cld.getTime();
				//获得下一天日期字符串
				nextDay = star;
				i++;
			}
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		return i;
	}

}
