/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.fegin;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.device.client.IDeviceLog23Client;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.DeviceLog23;
import org.skyworth.ess.device.mapper.DeviceLog23Mapper;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.LockType;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 字典服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class DeviceLog23BizClient extends BaseServiceImpl<DeviceLog23Mapper,DeviceLog23>  implements IDeviceLog23Client {

	private BladeRedis redis;

	private IDevice23Service device23Service;

	private RedisLockClient client;

	private IPlantService plantService;

	@Override
	@PostMapping(device23)
	public void insertBatchDeviceLog23Column(@RequestBody List<DeviceLog23> deviceLog23List) {
		baseMapper.insertBatchSomeColumn(deviceLog23List);
		List<String> snList = deviceLog23List.stream()
			.map(DeviceLog23::getDeviceSerialNumber)
			.collect(Collectors.toList());

		//查出所有sn的信息
		Map<String, Device23Entity> finalDeviceMap = getEntityMap(snList);
		ThreadPoolCustom.getCustomThreadPool().submit(() -> deviceLog23List.forEach(v->{
            String deviceSn=v.getDeviceSerialNumber();
            long plantId=v.getPlantId();
            String key= CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSn+"_3";
			boolean redisLock;
			try {
				redisLock = client.tryLock(key+"_Lock", LockType.REENTRANT,0,0, TimeUnit.SECONDS);
				if(redisLock){
					redis.setEx(key,"3", Duration.ofMinutes(5));
					common(v, deviceSn,finalDeviceMap);
					client.unLock(key+"_Lock", LockType.REENTRANT);
				}
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
        }));
	}

	private void common(DeviceLog23 v, String deviceSn,Map<String, Device23Entity> deviceMap) {
		String mode = v.getParallelModeFunction();
		if(deviceMap.isEmpty()){
			Device23Entity device23Entity=new Device23Entity();
			org.springframework.beans.BeanUtils.copyProperties(v,device23Entity);
			device23Entity.setId(null);
			device23Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			device23Service.save(device23Entity);
		}else {
			Device23Entity device23Entity=deviceMap.get(deviceSn);
			if(ValidationUtil.isNotEmpty(device23Entity)){
				long id=device23Entity.getId();
				Date createTime = device23Entity.getCreateTime();
				org.springframework.beans.BeanUtils.copyProperties(v,device23Entity);
				device23Entity.setId(id);
				device23Entity.setCreateTime(createTime);
				device23Service.updateById(device23Entity);
			}else {
				device23Entity=new Device23Entity();
				org.springframework.beans.BeanUtils.copyProperties(v,device23Entity);
				device23Entity.setId(null);
				device23Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				device23Service.save(device23Entity);
			}
		}

		//上报数据为并机模式，只要有一个是并机，则站点是并机
		if(Constants.ONE.equals(mode)) {
			device23Service.updatePlantMode(v.getPlantId(), deviceSn);
		}else{
			LambdaQueryWrapper<Device23Entity> queryOtherSn = Wrappers.<Device23Entity>query().lambda().eq(Device23Entity::getPlantId, v.getPlantId())
				.ne(Device23Entity::getDeviceSerialNumber, deviceSn).eq(Device23Entity::getParallelModeFunction,Constants.ONE);
			long dbOtherSnCount = device23Service.count(queryOtherSn);
			// 如果解绑站点下还有其他逆变器时并机，则站点为并机，如果站点下其他逆变器全部不是并机，则站点为非并机
			if (dbOtherSnCount == 0) {
				LambdaQueryWrapper<PlantEntity> eq = Wrappers.<PlantEntity>query().lambda()
					.in(PlantEntity::getId, v.getPlantId()).eq(PlantEntity::getIsDeleted,0) ;
				List<PlantEntity> plantEntities=plantService.list(eq);
				if(ValidationUtil.isNotEmpty(plantEntities)&&!plantEntities.isEmpty()){
					PlantEntity plant=plantEntities.get(0);
					plant.setIsParallelMode("0");
					plantService.updateById(plant);
				}
			} else {
				device23Service.updatePlantMode(v.getPlantId(), deviceSn);
			}

		}
	}

	/**
	 * 新版本配网逻辑
	 * */
	@PostMapping(NETWORK)
	@Override
	public void insertBatchDeviceLog23ForNetWork(@RequestBody List<DeviceLog23> deviceLog23List) {
		baseMapper.insertBatchSomeColumn(deviceLog23List);
		List<String> snList = deviceLog23List.stream()
			.map(DeviceLog23::getDeviceSerialNumber)
			.collect(Collectors.toList());

		//查出所有sn的信息
		Map<String, Device23Entity> finalDeviceMap = getEntityMap(snList);
		deviceLog23List.stream().parallel().forEach(v->{
			String deviceSn=v.getDeviceSerialNumber();
			long plantId=v.getPlantId();
			common(v, deviceSn, finalDeviceMap);
			String key= CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSn+"_v2_3";
			redis.setEx(key,"1",Duration.ofSeconds(60));
		});
	}

	@NotNull
	private Map<String, Device23Entity> getEntityMap(List<String> snList) {
		//查出所有sn的信息
		LambdaQueryWrapper<Device23Entity> eq = Wrappers.<Device23Entity>query().lambda()
			.in(Device23Entity::getDeviceSerialNumber, snList).eq(Device23Entity::getIsDeleted,0) ;
		List<Device23Entity> device23EntityList=device23Service.list(eq);
		Map<String, Device23Entity> deviceMap=new HashMap<>();
		if(ValidationUtil.isNotEmpty(device23EntityList)&&!device23EntityList.isEmpty()){
			deviceMap = device23EntityList.stream()
				.collect(Collectors.toMap(Device23Entity::getDeviceSerialNumber, Function.identity()));
		}

		return deviceMap;
	}
}
