/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.wrapper;

import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.vo.DeviceSoftwareVersionInfoVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 设备软件版本信息表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public class DeviceSoftwareVersionInfoWrapper extends BaseEntityWrapper<DeviceSoftwareVersionInfoEntity, DeviceSoftwareVersionInfoVO>  {

	public static DeviceSoftwareVersionInfoWrapper build() {
		return new DeviceSoftwareVersionInfoWrapper();
 	}

	@Override
	public DeviceSoftwareVersionInfoVO entityVO(DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo) {
		if (deviceSoftwareVersionInfo == null) {
			return null;
		}
		DeviceSoftwareVersionInfoVO deviceSoftwareVersionInfoVO = Objects.requireNonNull(BeanUtil.copy(deviceSoftwareVersionInfo, DeviceSoftwareVersionInfoVO.class));

		//User createUser = UserCache.getUser(deviceSoftwareVersionInfo.getCreateUser());
		//User updateUser = UserCache.getUser(deviceSoftwareVersionInfo.getUpdateUser());
		//deviceSoftwareVersionInfoVO.setCreateUserName(createUser.getName());
		//deviceSoftwareVersionInfoVO.setUpdateUserName(updateUser.getName());

		return deviceSoftwareVersionInfoVO;
	}


}
