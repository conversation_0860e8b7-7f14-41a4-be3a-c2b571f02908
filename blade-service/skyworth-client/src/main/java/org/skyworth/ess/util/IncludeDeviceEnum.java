package org.skyworth.ess.util;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum IncludeDeviceEnum {

	/**
	 * 光伏
	 * */
	photovoltaic("photovoltaic",",PV",",PV",",光伏"),
	Inverter("Inverter",",Inverter",",Wechselrichter",",逆变器"),
	Battery("Battery",",Battery",",Batterie",",电池"),
	ChargingStation("photovoltaic",",Charging Station",",Ladestation",",充电桩"),
	;

	private String code;

	private String enMessage;

	private String deMessage;

	private String zhMessage;

	IncludeDeviceEnum(String code, String enMessage, String deMessage, String zhMessage) {
		this.code = code;
		this.enMessage = enMessage;
		this.deMessage = deMessage;
		this.zhMessage = zhMessage;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setEnMessage(String enMessage) {
		this.enMessage = enMessage;
	}

	public void setDeMessage(String deMessage) {
		this.deMessage = deMessage;
	}

	public void setZhMessage(String zhMessage) {
		this.zhMessage = zhMessage;
	}
}
