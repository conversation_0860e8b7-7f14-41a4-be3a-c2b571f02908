/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 设备/逆变器自定义模式表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceCustomModeExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 逆变器模式
	 */
	@ColumnWidth(20)
	@ExcelProperty("逆变器模式")
	private String deviceMode;
	/**
	 * 一次/每天
	 */
	@ColumnWidth(20)
	@ExcelProperty("一次/每天")
	private String onceEveryday;
	/**
	 * 充电开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("充电开始时间")
	private String chargeStartTime1;
	/**
	 * 充电结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("充电结束时间")
	private String chargeEndTime1;
	/**
	 * 放电开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("放电开始时间")
	private String dischargeStartTime1;
	/**
	 * 放电结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("放电结束时间")
	private String dischargeEndTime1;
	/**
	 * 结束充电容量比例
	 */
	@ColumnWidth(20)
	@ExcelProperty("结束充电容量比例")
	private Integer capacityOfChargerEnd;
	/**
	 * 结束放电容量比例
	 */
	@ColumnWidth(20)
	@ExcelProperty("结束放电容量比例")
	private Integer capacityOfDischargerEnd;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
