/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.springblade.system.feign;

import org.springblade.common.vo.BatchVO;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-10-20 16:21
 **/
@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME,
	fallback = IAttachmentInfoClientFallback.class
)
public interface IAttachmentInfoClient {
	String API_PREFIX = "/attach";
	String FIND_BY_BUSINESS_IDS = API_PREFIX + "/findByBusinessIds";
	String FIND_BY_BUSINESS_IDS_NO_TENANT = API_PREFIX + "/findByBusinessIdsNoTenant";
	String SAVE_AND_UPDATE_BATCH = API_PREFIX + "/saveAndUpdate";

	/**
	 * 查询附件
	 *
	 * @param businessIds 入参
	 * @return R<Map < Long, List < AttachmentInfoEntity>>>
	 * <AUTHOR>
	 * @since 2023/10/24 15:45
	 **/
	@PostMapping(FIND_BY_BUSINESS_IDS)
	R<Map<Long, List<AttachmentInfoEntity>>> findByBusinessIds(@RequestBody List<Long> businessIds);


	@PostMapping(FIND_BY_BUSINESS_IDS_NO_TENANT)
	R<Map<Long, List<AttachmentInfoEntity>>> findByBusinessIdsNoTent(@RequestBody List<Long> businessIds);
	/**
	 * 保存附件
	 *
	 * @param batchVO    附件信息
	 * @return R<Long>
	 * <AUTHOR>
	 * @since 2023/10/24 15:45
	 **/
	@PostMapping(SAVE_AND_UPDATE_BATCH)
	R saveAndUpdate(@RequestBody BatchVO<AttachmentInfoEntity> batchVO) ;


}
