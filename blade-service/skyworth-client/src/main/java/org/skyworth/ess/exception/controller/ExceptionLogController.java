/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exception.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.skyworth.ess.exception.excel.ExceptionLogExcel;
import org.skyworth.ess.exception.service.IExceptionLogService;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import org.skyworth.ess.exception.wrapper.ExceptionLogWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 异常日志 控制器
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("/exceptionLog")
@Api(value = "异常日志", tags = "异常日志接口")
public class ExceptionLogController extends BladeController {

	private final IExceptionLogService exceptionLogService;

	/**
	 * 异常日志 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入exceptionLog")
	public R<ExceptionLogVO> detail(ExceptionLogEntity exceptionLog) {
		ExceptionLogEntity detail = exceptionLogService.getOne(Condition.getQueryWrapper(exceptionLog));
		return R.data(ExceptionLogWrapper.build().entityVO(detail));
	}
	/**
	 * 异常日志 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入exceptionLog")
	public R<IPage<ExceptionLogVO>> list(@ApiIgnore @RequestParam Map<String, Object> exceptionLog, Query query) {
		IPage<ExceptionLogEntity> pages = exceptionLogService.page(Condition.getPage(query), Condition.getQueryWrapper(exceptionLog, ExceptionLogEntity.class));
		return R.data(ExceptionLogWrapper.build().pageVO(pages));
	}

	/**
	 * 异常日志 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入exceptionLog")
	public R<IPage<ExceptionLogVO>> page(ExceptionLogVO exceptionLog, Query query) {
		IPage<ExceptionLogVO> pages = exceptionLogService.selectExceptionLogPage(Condition.getPage(query), exceptionLog);
		return R.data(pages);
	}

	/**
	 * 异常日志 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入exceptionLog")
	public R save(@Valid @RequestBody ExceptionLogEntity exceptionLog) {
		return R.status(exceptionLogService.save(exceptionLog));
	}

	/**
	 * 异常日志 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入exceptionLog")
	public R update(@Valid @RequestBody ExceptionLogEntity exceptionLog) {
		return R.status(exceptionLogService.updateById(exceptionLog));
	}

	/**
	 * 异常日志 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入exceptionLog")
	public R submit(@Valid @RequestBody ExceptionLogEntity exceptionLog) {
		return R.status(exceptionLogService.saveOrUpdate(exceptionLog));
	}

	/**
	 * 异常日志 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(exceptionLogService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-exceptionLog")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入exceptionLog")
	public void exportExceptionLog(@ApiIgnore @RequestParam Map<String, Object> exceptionLog, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ExceptionLogEntity> queryWrapper = Condition.getQueryWrapper(exceptionLog, ExceptionLogEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ExceptionLog::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(ExceptionLogEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ExceptionLogExcel> list = exceptionLogService.exportExceptionLog(queryWrapper);
		ExcelUtil.export(response, "异常日志数据" + DateUtil.time(), "异常日志数据表", list, ExceptionLogExcel.class);
	}

}
