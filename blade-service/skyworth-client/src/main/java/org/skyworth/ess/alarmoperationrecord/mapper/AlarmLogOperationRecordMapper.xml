<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.alarmoperationrecord.mapper.AlarmLogOperationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="alarmLogOperationRecordResultMap" type="org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity">
        <result column="id" property="id"/>
        <result column="alarm_log_id" property="alarmLogId"/>
        <result column="remark" property="remark"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectAlarmLogOperationRecordPage" resultMap="alarmLogOperationRecordResultMap">
        select * from alarm_log_operation_record where is_deleted = 0
    </select>


    <select id="exportAlarmLogOperationRecord" resultType="org.skyworth.ess.alarmoperationrecord.excel.AlarmLogOperationRecordExcel">
        SELECT * FROM alarm_log_operation_record ${ew.customSqlSegment}
    </select>

    <select id="selectOperationRecordList"
            resultType="org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity">
        select
            alor.id,
            alor.alarm_log_id,
            alor.remark,
            alor.create_user_account,
            alor.update_user_account,
            alor.tenant_id,
            alor.create_user,
            alor.create_dept,
            alor.create_time,
            alor.update_user,
            alor.update_time,
            alor.status,
            alor.is_deleted,
            al.plant_id
        from alarm_log_operation_record alor
        left join alarm_log al on alor.alarm_log_id = al.id
        where alor.alarm_log_id = #{alarmLogId}
        and alor.is_deleted = 0 and al.is_deleted = 0
        order by alor.id desc
    </select>

    <select id="getRecords"
            resultType="org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity">
        select
            alor.id,
            alor.alarm_log_id,
            alor.remark,
            alor.create_user_account,
            alor.update_user_account,
            alor.tenant_id,
            alor.create_user,
            alor.create_dept,
            alor.create_time,
            alor.update_user,
            CONVERT_TZ(alor.update_time,'+00:00',#{map.timeZone}) as update_time,
            alor.status,
            alor.is_deleted,
            al.plant_id
        from alarm_log_operation_record alor
                 left join alarm_log al on alor.alarm_log_id = al.id
        where alor.alarm_log_id = #{map.alarmLogId}
          and alor.is_deleted = 0 and al.is_deleted = 0
        order by alor.id desc
    </select>

</mapper>
