package org.skyworth.ess.mqtt.ssl;

import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.CheckForNull;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class LinkSSLUtils {

    private LinkSSLUtils() {}

    private static Logger logger = LoggerFactory.getLogger(LinkSSLUtils.class);

    public static KeyStore createKeyStore(String keyPem, String certPem, String certKey) throws NoSuchAlgorithmException, IOException, CertificateException, KeyStoreException {
        PrivateKey privateKey = loadPrivateKey(keyPem);
        Objects.requireNonNull(privateKey);
        List<Certificate> privateCertificates = loadCertificates(certPem);
        KeyStore keyStore = KeyStore.getInstance("JKS");
        keyStore.load(null);
        keyStore.setKeyEntry(certKey, privateKey, certKey.toCharArray(), privateCertificates.toArray(new Certificate[privateCertificates.size()]));
        return keyStore;
    }

    public static List<Certificate> loadCertificates(String certPem) throws IOException, CertificateException {
        try(StringReader certReader = new StringReader(certPem);
            BufferedReader reader = new BufferedReader(certReader);
            PEMParser pemParser = new PEMParser(reader)) {
            List<Certificate> certificates = new ArrayList();
            JcaX509CertificateConverter certificateConverter = (new JcaX509CertificateConverter()).setProvider("BC");
            Object certObj = pemParser.readObject();
            if (certObj instanceof X509CertificateHolder) {
                X509CertificateHolder certificateHolder = (X509CertificateHolder)certObj;
                certificates.add(certificateConverter.getCertificate(certificateHolder));
            }
            return certificates;
        }
    }

    @CheckForNull
    private static PrivateKeyInfo getPrivateKeyInfoOrNull(Object pemObject) {
        PrivateKeyInfo privateKeyInfo = null;
        if (pemObject instanceof PEMKeyPair) {
            PEMKeyPair pemKeyPair = (PEMKeyPair)pemObject;
            privateKeyInfo = pemKeyPair.getPrivateKeyInfo();
        } else if (pemObject instanceof PrivateKeyInfo) {
            privateKeyInfo = (PrivateKeyInfo)pemObject;
        } else if (pemObject instanceof ASN1ObjectIdentifier) {
            ASN1ObjectIdentifier asn1ObjectIdentifier = (ASN1ObjectIdentifier)pemObject;
            logger.trace("Ignoring asn1ObjectIdentifier {}", asn1ObjectIdentifier);
        } else {
            logger.warn("Unknown object '{}' from PEMParser", pemObject);
        }

        return privateKeyInfo;
    }

    @CheckForNull
    public static PrivateKey loadPrivateKey(String keyPem) throws IOException {
        try(StringReader certReader = new StringReader(keyPem);
            BufferedReader reader = new BufferedReader(certReader);
            PEMParser pemParser = new PEMParser(reader)) {
            for(Object readObject = pemParser.readObject(); readObject != null; readObject = pemParser.readObject()) {
                PrivateKeyInfo privateKeyInfo = getPrivateKeyInfoOrNull(readObject);
                if (privateKeyInfo != null) {
                    return (new JcaPEMKeyConverter()).getPrivateKey(privateKeyInfo);
                }
            }
            return null;
        }
    }

    public static KeyStore createTrustStore(String caPem) throws IOException, CertificateException, KeyStoreException, NoSuchAlgorithmException {
        try(Reader certReader = new StringReader(caPem);
            PEMParser pemParser = new PEMParser(certReader)) {
            KeyStore trustStore = KeyStore.getInstance("JKS");
            trustStore.load(null);

            Object pemCert;
            for(int index = 1; (pemCert = pemParser.readObject()) != null; ++index) {
                Certificate caCertificate = (new JcaX509CertificateConverter()).setProvider("BC").getCertificate((X509CertificateHolder)pemCert);
                trustStore.setCertificateEntry("ca-" + index, caCertificate);
            }

            return trustStore;
        }
    }

}
