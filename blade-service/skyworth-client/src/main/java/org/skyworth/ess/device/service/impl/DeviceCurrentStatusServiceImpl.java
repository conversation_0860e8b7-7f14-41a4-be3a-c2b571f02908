/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.excel.DeviceCurrentStatusExcel;
import org.skyworth.ess.device.mapper.DeviceCurrentStatusMapper;
import org.skyworth.ess.device.service.IDeviceCurrentStatusService;
import org.skyworth.ess.device.vo.DeviceCurrentStatusVO;
import org.skyworth.ess.device.vo.InverterReportQueryVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 设备/逆变器当前状态 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
public class DeviceCurrentStatusServiceImpl extends BaseServiceImpl<DeviceCurrentStatusMapper, DeviceCurrentStatusEntity> implements IDeviceCurrentStatusService {

	@Override
	public IPage<DeviceCurrentStatusVO> selectDeviceCurrentStatusPage(IPage<DeviceCurrentStatusVO> page, DeviceCurrentStatusVO DeviceCurrentStatus) {
		return page.setRecords(baseMapper.selectDeviceCurrentStatusPage(page, DeviceCurrentStatus));
	}

	@Override
	public List<DeviceCurrentStatusEntity> selectDeviceCurrentStatusIsDelete(Long plantId, String deviceSerialNumber) {
		return baseMapper.selectDeviceCurrentStatusIsDelete(plantId, deviceSerialNumber);
	}


	@Override
	public List<DeviceCurrentStatusExcel> exportDeviceCurrentStatus(Wrapper<DeviceCurrentStatusEntity> queryWrapper) {
		List<DeviceCurrentStatusExcel> DeviceCurrentStatusList = baseMapper.exportDeviceCurrentStatus(queryWrapper);
		//DeviceCurrentStatusList.forEach(DeviceCurrentStatus -> {
		//	DeviceCurrentStatus.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceCurrentStatus.getType()));
		//});
		return DeviceCurrentStatusList;
	}

	@Override
	public JSONObject selectEnergyTotalStat(InverterReportQueryVO queryCondition) {
		JSONObject jsonObject = baseMapper.selectEnergyTotalStat(queryCondition);
		if (Objects.isNull(jsonObject)) {
			return jsonObject;
		}

		BigDecimal totalEnergy = jsonObject.getBigDecimal("totalEnergy");
		jsonObject.put("totalEnergy",totalEnergy.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));

		BigDecimal accumulatedEnergyOfPositive = jsonObject.getBigDecimal("accumulatedEnergyOfPositive");
		jsonObject.put("accumulatedEnergyOfPositive",accumulatedEnergyOfPositive.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));

		BigDecimal accumulatedEnergyOfNegative = jsonObject.getBigDecimal("accumulatedEnergyOfNegative");
		jsonObject.put("accumulatedEnergyOfNegative",accumulatedEnergyOfNegative.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));

		BigDecimal accumulatedEnergyOfLoad = jsonObject.getBigDecimal("accumulatedEnergyOfLoad");
		jsonObject.put("accumulatedEnergyOfLoad",accumulatedEnergyOfLoad.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));

		BigDecimal accumulatedEnergyToEps = jsonObject.getBigDecimal("accumulatedEnergyToEps");
		jsonObject.put("accumulatedEnergyToEps",accumulatedEnergyToEps.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));

		return jsonObject;
	}

	@Override
	public JSONObject inverterTodaySummary() {
		return baseMapper.inverterTodaySummary();
	}

	@Override
	public JSONObject inverterAccumulateSummary() {
		return baseMapper.inverterAccumulateSummary();
	}

	@Override
	public JSONObject batteryTodaySummary() {
		return baseMapper.batteryTodaySummary();
	}

	@Override
	public JSONObject batteryAccumulateSummary() {
		return baseMapper.batteryAccumulateSummary();
	}

    @Override
    public int batchDeleteLogicByPlantId(List<Long> longList, String account) {
        return baseMapper.batchDeleteLogicByPlantId(longList,account);
    }

	@Override
	public int batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account) {
		return baseMapper.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, account);
	}
}
